{"name": "worker", "version": "1.0.0", "description": "", "main": "dist/index.js", "type": "module", "scripts": {"test": "vitest --run", "test:watch": "vitest", "test:coverage": "jest --coverage", "lint": "eslint src/** --ext .ts --fix", "dev": "NODE_ENV=development tsx --watch ./src/index.ts", "dev:worker": "WORKER=true NODE_ENV=development tsx --watch ./src/index.ts", "build": "esbuild ./src/index.ts --bundle --platform=node --format=esm --packages=external --outdir=dist", "start": "node dist/index.js", "start:worker": "WORKER=true node dist/index.js", "start:worker:track": "WORKER=true TRACK=true node dist/index.js", "init-default-tags": "NODE_ENV=development tsx src/scripts/init_default_tag.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.850.0", "@bull-board/api": "^5.21.4", "@bull-board/fastify": "^5.21.4", "@bull-board/ui": "^5.21.4", "@fastify/autoload": "^5.9.0", "@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^2.1.0", "@google/generative-ai": "^0.24.0", "@json2csv/node": "^7.0.6", "@json2csv/plainjs": "^7.0.6", "@node-rs/xxhash": "^1.7.3", "@node-rs/xxhash-linux-x64-musl": "^1.7.6", "@repo/database": "workspace:^", "@ruguoapp/cache": "^0.5.0", "@sentry/node": "^8.42.0", "@sentry/profiling-node": "^8.42.0", "@sinclair/typebox": "^0.34.33", "@slack/web-api": "^7.9.1", "@supabase/supabase-js": "^2.43.4", "@turf/turf": "^7.2.0", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.4", "@types/node-schedule": "^2.1.7", "@zilliz/milvus2-sdk-node": "^2.5.6", "ali-oss": "^6.20.0", "async-mutex": "^0.5.0", "async-retry": "^1.3.3", "axios": "^1.7.2", "axios-proxy-fix": "^0.16.3", "axios-retry": "^4.5.0", "bluebird": "^3.7.2", "bull": "^4.16.3", "dayjs": "^1.11.13", "debug": "^4.3.4", "dotenv": "^16.4.5", "endent": "^2.1.0", "exceljs": "^4.4.0", "fastify": "^4.29.1", "fastify-graceful-shutdown": "^4.0.1", "fastify-plugin": "^4.5.1", "fastify-type-provider-zod": "^2.0.0", "gaxios": "^6.6.0", "google-auth-library": "^9.11.0", "googleapis": "^137.1.0", "gpt-3-encoder": "^1.1.4", "heap-js": "^2.6.0", "hpagent": "^1.2.0", "https-proxy-agent": "^7.0.4", "i18n-iso-countries": "^7.13.0", "ioredis": "^4.28.5", "javascript-time-ago": "^2.5.11", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "log4js": "^6.9.1", "node-fetch": "^3.3.2", "node-html-parser": "^7.0.1", "node-schedule": "^2.1.1", "nodemailer": "^6.9.14", "nunjucks": "^3.2.4", "ofetch": "^1.4.1", "openai": "^4.92.1", "p-limit": "^5.0.0", "point-of-view": "^6.3.0", "posthog-node": "^4.2.2", "promise-retry": "^2.0.1", "proxy-chain": "^2.4.0", "stripe": "^18.1.0", "undici": "^7.3.0", "uuid": "^9.0.1", "vm2": "^3.9.19", "youtubei": "^1.6.6", "youtubei.js": "^13.3.0", "zod": "^3.24.4"}, "devDependencies": {"@types/ali-oss": "^6.16.11", "@types/async-retry": "^1.4.8", "@types/bluebird": "^3.5.42", "@types/bull": "^4.10.4", "@types/debug": "^4.1.12", "@types/geojson": "^7946.0.16", "@types/ioredis": "^4.28.10", "@types/node": "^20.12.12", "@types/nodemailer": "^6.4.15", "@types/nunjucks": "^3.2.6", "@types/pino-pretty": "^5.0.0", "@types/promise-retry": "^1.1.6", "@types/uuid": "^9.0.8", "@vitest/coverage-v8": "^1.2.1", "esbuild": "^0.19.12", "jest": "^29.7.0", "lint-staged": "^15.2.0", "pino-pretty": "^13.0.0", "prettier": "^3.2.4", "prettier-plugin-organize-imports": "^3", "prisma": "^5.14.0", "ts-node": "^10.9.2", "tsx": "^4.7.0", "vite": "^5.2.11", "vite-tsconfig-paths": "^4.3.2"}}