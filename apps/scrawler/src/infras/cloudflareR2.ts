import {
  CLOUDFLARE_R2_ACCESS_KEY_ID,
  CL<PERSON>UDFLARE_R2_ACCOUNT_ID,
  CLOUDFLARE_R2_BUCKET_NAME,
  CLOUDFLARE_R2_PUBLIC_URL,
  CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  C<PERSON><PERSON><PERSON>DFLARE_WORKER_URL,
} from '@/config/env'
import {
  DeleteObjectCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3'
import crypto from 'crypto'
import path from 'path'
import { Readable } from 'stream'

export function validateR2Config(): void {
  if (
    !CLOUDFLARE_R2_ACCESS_KEY_ID ||
    !CLOUDFLARE_R2_SECRET_ACCESS_KEY ||
    !CLOUDFLARE_R2_BUCKET_NAME ||
    !CLOUDFLARE_R2_ACCOUNT_ID
  ) {
    console.error('❌ Cloudflare R2 环境变量未设置')
    console.error('请检查以下环境变量:')
    console.error('- CLOUDFLARE_R2_ACCOUNT_ID')
    console.error('- CLOUDFLARE_R2_ACCESS_KEY_ID')
    console.error('- CLOUDFLARE_R2_SECRET_ACCESS_KEY')
    console.error('- CLOUDFLARE_R2_BUCKET_NAME')
    console.error('- CLOUDFLARE_R2_PUBLIC_URL')
    process.exit(1)
  }

  console.log('✅ [R2] 配置验证通过:', {
    accountId: CLOUDFLARE_R2_ACCOUNT_ID,
    bucketName: CLOUDFLARE_R2_BUCKET_NAME,
    publicUrl: CLOUDFLARE_R2_PUBLIC_URL,
    accessKeyId: CLOUDFLARE_R2_ACCESS_KEY_ID.substring(0, 8) + '...',
    secretKeyLength: CLOUDFLARE_R2_SECRET_ACCESS_KEY.length,
    endpoint: `https://${CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyIdValid: /^[a-f0-9]{32}$/.test(CLOUDFLARE_R2_ACCESS_KEY_ID),
    secretKeyValid: /^[a-f0-9]{64}$/.test(CLOUDFLARE_R2_SECRET_ACCESS_KEY),
  })
}

// 按照 Cloudflare 官方文档配置: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-js-v3/
export const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  },
})

/**
 * 生成唯一的文件名
 */
function generateUniqueFileName(originalUrl: string, prefix: string = 'publication'): string {
  const hash = crypto.createHash('md5').update(originalUrl).digest('hex')
  const timestamp = Date.now()
  // 从 URL 中提取扩展名，去除查询参数
  const urlWithoutQuery = originalUrl.split('?')[0]
  const ext = path.extname(urlWithoutQuery) || '.jpg'
  // 确保文件名只包含安全字符
  return `${prefix}/${timestamp}-${hash}${ext.toLowerCase()}`
}

/**
 * 上传 Buffer 到 R2
 */
export async function uploadBufferToR2(
  buffer: Buffer,
  key: string,
  contentType: string = 'image/jpeg',
): Promise<{ key: string; url: string }> {
  try {
    const command = new PutObjectCommand({
      Bucket: CLOUDFLARE_R2_BUCKET_NAME,
      Key: key,
      Body: buffer,
      ContentType: contentType,
    })

    await r2Client.send(command)

    const publicUrl = CLOUDFLARE_R2_PUBLIC_URL
      ? `${CLOUDFLARE_R2_PUBLIC_URL}/${key}`
      : `https://${CLOUDFLARE_R2_BUCKET_NAME}.${CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com/${key}`

    console.log('R2 Upload success:', { key, url: publicUrl })
    return { key, url: publicUrl }
  } catch (err) {
    console.error('R2 Upload failed:', err)
    throw err
  }
}

/**
 * 上传 Stream 到 R2
 */
export async function uploadStreamToR2(
  stream: Readable,
  key: string,
  contentType: string = 'image/jpeg',
): Promise<{ key: string; url: string }> {
  try {
    const command = new PutObjectCommand({
      Bucket: CLOUDFLARE_R2_BUCKET_NAME,
      Key: key,
      Body: stream,
      ContentType: contentType,
    })

    await r2Client.send(command)

    const publicUrl = CLOUDFLARE_R2_PUBLIC_URL
      ? `${CLOUDFLARE_R2_PUBLIC_URL}/${key}`
      : `https://${CLOUDFLARE_R2_BUCKET_NAME}.${CLOUDFLARE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com/${key}`

    console.log('R2 Upload success:', { key, url: publicUrl })
    return { key, url: publicUrl }
  } catch (err) {
    console.error('R2 Upload failed:', err)
    throw err
  }
}

/**
 * 使用固定 key 上传图片到 R2（会覆盖已存在的文件）
 */
export async function uploadImageWithFixedKey(
  imageUrl: string,
  fixedKey: string,
): Promise<{ key: string; url: string; originalUrl: string }> {
  try {
    console.log(`[R2] 开始下载图片: ${imageUrl}`)

    // 下载图片 - 优先使用 Cloudflare Worker 代理
    let response: Response

    try {
      // 尝试使用 Cloudflare Worker 代理下载
      if (CLOUDFLARE_WORKER_URL) {
        const proxyUrl = `${CLOUDFLARE_WORKER_URL}?url=${encodeURIComponent(imageUrl)}`
        console.log(`[R2] 使用 CF Worker 代理下载: ${imageUrl.slice(0, 80)}...`)

        response = await fetch(proxyUrl)

        if (!response.ok) {
          let errorMessage = `CF Worker 下载失败: ${response.status}`
          try {
            const errorData = await response.json()
            if (errorData.error) {
              errorMessage = errorData.error
            }
          } catch {
            errorMessage += ` ${response.statusText}`
          }

          // 如果是服务器错误或权限问题，尝试直接下载
          if (response.status >= 500 || response.status === 403) {
            console.warn(`[R2] CF Worker 失败，尝试直接下载`)
            response = await fetch(imageUrl)

            if (!response.ok) {
              throw new Error(`直接下载图片失败: ${response.status} ${response.statusText}`)
            }
          } else {
            throw new Error(errorMessage)
          }
        } else {
          const cacheStatus = response.headers.get('x-cache-status') || 'MISS'
          const responseTime = response.headers.get('x-response-time') || '0ms'
          console.log(`[R2] CF Worker 下载成功 [${cacheStatus}] ${responseTime}`)
        }
      } else {
        // 没有配置 Worker URL，直接下载
        console.log(`[R2] 直接下载图片: ${imageUrl.slice(0, 80)}...`)
        response = await fetch(imageUrl)

        if (!response.ok) {
          throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
        }
      }
    } catch (error) {
      // 如果 Worker 不可用，回退到直接下载
      if (CLOUDFLARE_WORKER_URL) {
        console.warn(`[R2] CF Worker 不可用，回退到直接下载: ${error}`)
        response = await fetch(imageUrl)

        if (!response.ok) {
          throw new Error(`直接下载图片失败: ${response.status} ${response.statusText}`)
        }
      } else {
        throw error
      }
    }

    const buffer = Buffer.from(await response.arrayBuffer())
    const contentType = response.headers.get('content-type') || 'image/jpeg'

    // 使用固定的 key 上传到 R2
    const result = await uploadBufferToR2(buffer, fixedKey, contentType)

    console.log(`[R2] 图片上传成功: ${imageUrl} -> ${result.url}`)

    return {
      ...result,
      originalUrl: imageUrl,
    }
  } catch (err) {
    console.error(`[R2] 图片处理失败: ${imageUrl}`, err)
    throw err
  }
}

/**
 * 从外部 URL 下载图片并上传到 R2
 */
export async function downloadAndUploadToR2(
  imageUrl: string,
  keyPrefix: string = 'publication',
): Promise<{ key: string; url: string; originalUrl: string }> {
  try {
    console.log(`[R2] 开始下载图片: ${imageUrl}`)

    let response: Response

    try {
      if (CLOUDFLARE_WORKER_URL) {
        const proxyUrl = `${CLOUDFLARE_WORKER_URL}?url=${encodeURIComponent(imageUrl)}`
        console.log(`[R2] 使用 CF Worker 代理下载: ${imageUrl.slice(0, 80)}...`)

        response = await fetch(proxyUrl)

        if (!response.ok) {
          let errorMessage = `CF Worker 下载失败: ${response.status}`
          try {
            const errorData = await response.json()
            if (errorData.error) {
              errorMessage = errorData.error
            }
          } catch {
            errorMessage += ` ${response.statusText}`
          }

          if (response.status >= 500 || response.status === 403) {
            console.warn(`[R2] CF Worker 失败，尝试直接下载`)
            response = await fetch(imageUrl)

            if (!response.ok) {
              throw new Error(`直接下载图片失败: ${response.status} ${response.statusText}`)
            }
          } else {
            throw new Error(errorMessage)
          }
        } else {
          const cacheStatus = response.headers.get('x-cache-status') || 'MISS'
          const responseTime = response.headers.get('x-response-time') || '0ms'
          console.log(`[R2] CF Worker 下载成功 [${cacheStatus}] ${responseTime}`)
        }
      } else {
        console.log(`[R2] 直接下载图片: ${imageUrl.slice(0, 80)}...`)
        response = await fetch(imageUrl)

        if (!response.ok) {
          throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
        }
      }
    } catch (error) {
      if (CLOUDFLARE_WORKER_URL) {
        console.warn(`[R2] CF Worker 不可用，回退到直接下载: ${error}`)
        response = await fetch(imageUrl)

        if (!response.ok) {
          throw new Error(`直接下载图片失败: ${response.status} ${response.statusText}`)
        }
      } else {
        throw error
      }
    }

    const buffer = Buffer.from(await response.arrayBuffer())
    const contentType = response.headers.get('content-type') || 'image/jpeg'

    // 生成唯一文件名
    const key = generateUniqueFileName(imageUrl, keyPrefix)

    // 上传到 R2
    const result = await uploadBufferToR2(buffer, key, contentType)

    console.log(`[R2] 图片上传成功: ${imageUrl} -> ${result.url}`)

    return {
      ...result,
      originalUrl: imageUrl,
    }
  } catch (err) {
    console.error(`[R2] 图片处理失败: ${imageUrl}`, err)
    throw err
  }
}

/**
 * 从 R2 下载文件
 */
export async function downloadFromR2(key: string): Promise<Readable> {
  try {
    const command = new GetObjectCommand({
      Bucket: CLOUDFLARE_R2_BUCKET_NAME,
      Key: key,
    })

    const response = await r2Client.send(command)
    return response.Body as Readable
  } catch (err) {
    console.error('R2 Download failed:', err)
    throw err
  }
}

/**
 * 从 R2 删除文件
 */
export async function deleteFromR2(key: string): Promise<void> {
  try {
    const command = new DeleteObjectCommand({
      Bucket: CLOUDFLARE_R2_BUCKET_NAME,
      Key: key,
    })

    await r2Client.send(command)
    console.log('R2 Delete success:', key)
  } catch (err) {
    console.error('R2 Delete failed:', err)
    throw err
  }
}
