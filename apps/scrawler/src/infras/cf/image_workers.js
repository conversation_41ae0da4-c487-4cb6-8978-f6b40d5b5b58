export default {
  async fetch(request, env, ctx) {
    const ALLOWED_ORIGINS = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:5173',
      'https://talent-marking-backend.aiwaifu.top',
      'https://talent-marking-backend-beta.aiwaifu.top',
      'https://www.easykol.com',
      'https://easykol.com',
      'https://www-beta.easykol.com',
    ]

    const url = new URL(request.url)
    const imageUrl = url.searchParams.get('url')
    const origin = request.headers.get('Origin')
    const isAllowedOrigin = ALLOWED_ORIGINS.includes(origin)

    // Origin 验证
    if (!isAllowedOrigin && origin) {
      return new Response('Origin not allowed', {
        status: 403,
        headers: { 'Content-Type': 'text/plain' },
      })
    }

    const corsHeaders = isAllowedOrigin
      ? {
          'Access-Control-Allow-Origin': origin,
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Max-Age': '86400',
          'Access-Control-Allow-Credentials': 'true',
        }
      : {}

    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders })
    }

    if (!imageUrl) {
      return new Response(JSON.stringify({ error: 'Missing URL parameter' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // 基本 URL 格式验证
    try {
      new URL(imageUrl)
    } catch {
      return new Response(JSON.stringify({ error: 'Invalid URL format' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // 请求超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15_000)

    try {
      const response = await fetch(imageUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
        signal: controller.signal,
        cf: {
          cacheTtl: 0,
          cacheEverything: false,
        },
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        return new Response(
          JSON.stringify({
            error: `HTTP ${response.status}: ${response.statusText}`,
            url: imageUrl,
            status: response.status,
          }),
          {
            status: response.status,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          },
        )
      }

      const imageBuffer = await response.arrayBuffer()
      const contentType = response.headers.get('content-type') || 'image/jpeg'

      return new Response(imageBuffer, {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': contentType,
          'Cache-Control': 'public, max-age=86400', // 1天缓存
        },
      })
    } catch (error) {
      clearTimeout(timeoutId)

      if (error.name === 'AbortError') {
        return new Response(JSON.stringify({ error: 'Request timeout' }), {
          status: 408,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      return new Response(
        JSON.stringify({
          error: error.message,
          url: imageUrl,
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        },
      )
    }
  },
}
