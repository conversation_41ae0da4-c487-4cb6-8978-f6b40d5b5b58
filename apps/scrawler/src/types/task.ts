import { BatchAudienceMode } from '@/enums/TaskMode'
import { InstagramPriorityUser } from '@/utils/PriorityQueue'
import { KolPlatform, SimilarChannelTaskStatus, TaskReason, TaskType } from '@repo/database'
import { InstagramSortType } from './instagram'
import {
  DouyinTrackParams,
  InstagramParams,
  TiktokParams,
  XhsParams,
  YoutubeParams,
} from './publicationStatistics'

export enum TaskSearchType {
  REMIX = 'REMIX',
}

export interface SimilarTaskParams {
  projectId: string
  source: string
  platform: string
  regions?: string[]
  minSubscribers?: number
  maxSubscribers?: number
  lastPublishedDays?: number
  videosAverageViews?: number
  maxVideosAverageViews?: number
  minAverageLikeCount?: number
  maxAverageLikeCount?: number
  banList?: string[]
  allowList?: string[]
  channelId?: string
  kolDescription?: string
  taskRound?: number
  // tiktok mode
  ttMode?: number
  ttModeReason?: string
  // youtube mode
  ytbMode?: number
  ytbVideoIds?: string[]
  // instagram mode
  insMode?: number
  // twitter mode
  twitterMode?: number
  twitterUserNames?: string[]
}

export type KeywordTaskParams = {
  projectId: string
  platform: string // 必填：平台类型 (例如 YOUTUBE, TIKTOK, INSTAGRAM)
  keywords: string[]
  viewCountGte?: number
  url?: string
  description?: string
  regions?: string[]
}

export type TtHashTagBreakTaskParams = {
  projectId: string
  platform: string
  tag: string
  cursor: number
  currentVideoCount?: number
  maxVideoCount?: number
}

// instagram 的 hashTag爆破
export type InsHashTagBreakTaskParams = {
  projectId: string
  platform: string
  tag: string
  paginationToken: string
  currentVideoCount?: number
  maxVideoCount?: number
  total?: number
  sortType: InstagramSortType
}

// youtube 的 hashTag爆破
export type YoutubeHashTagBreakTaskParams = {
  projectId: string
  platform: string
  tag: string
  paginationToken: string
  currentVideoCount?: number
  maxVideoCount?: number
  total?: number
}

// youtube input search 参数
export type YoutubeSearchInputBreakTaskParams = {
  projectId: string
  platform: string
  searchInput: string
  paginationToken: string
  currentVideoCount?: number
  maxVideoCount?: number
  total?: number
  geo?: string
  lang?: string
  duration?: string
  sort_by?: string
  upload_date?: string
}

export type TtSearchInputBreakTaskParams = {
  projectId: string
  platform: string
  searchInput: string
  cursor: number
  sortType: 0 | 1 | 3
  publishTimeType: 0 | 1 | 7 | 30 | 90 | 180
  currentVideoCount?: number
  maxVideoCount: number
}

export type TtFollowersSimilarTaskParams = {
  projectId: string
  platform: string
  uniqueId: string
  excludeWords?: string[]
  // maxVideoCount: number
}

export type TtFollowingListTaskParams = {
  projectId: string
  platform: string
  uniqueId: string
  userId: string
  currentTime: number
  maxCount: number
  currentCount: number
}

// 存储在similarChannelTask的result字段中
export type TtFollowingListTaskResult = {
  taskId: string
  uniqueIds: string[]
  following: number // 总的关注用户数
  hasMore: boolean
  time: number
  total: number // 返回的total
  progress: {
    total: number
    current: number
    count?: number
  }
}

// 定义 HASH_TAG_BREAK 任务的结果类型
export type TtHashTagBreakTaskResult = {
  taskId: string
  tag: string
  uniqueIds: string[]
  cursor: number
  hasMore: boolean
  total: number
  count: number
  message?: string
  progress: {
    total: number
    current: number
    videoCount?: number
  }
}

export type TtSearchInputBreakTaskResult = {
  taskId: string
  searchInput: string
  uniqueIds: string[]
  cursor: number
  hasMore: boolean
  totalAuthorCount: number
  afterFilterAuthorCount: number
  videoCount: number
  message?: string
  progress: {
    // total: number
    // current: number
    videoCount?: number
  }
}

export type TtFollowersSimilarTaskResult = {
  taskId: string
  uniqueIds: string[]
  total: number
  count: number
}

export type TaskSituationResponse = {
  email: string
  taskCounts: {
    [key in TaskType]: number
  }
  totalTasks: number
  completedTasks: number
  failedTasks: number
  pendingTasks: number
  platformCounts: {
    YOUTUBE: number
    TIKTOK: number
    INSTAGRAM: number
  }
}

export interface TaskQueryParams {
  email?: string
  projectId?: string
  platform?: string
  status?: SimilarChannelTaskStatus[] // 支持多选
  taskType?: TaskType
  taskReason?: TaskReason
  page?: number
  pageSize?: number
}

export interface TaskQueryResponse {
  total: number
  page: number
  pageSize: number
  data: {
    email: string
    projectName: string
    platform: string // 从 params.platform 获取
    source: string // 从 params.source 获取
    createdAt: Date
    updatedAt: Date
    params: any
    errors: any
    hasResult: boolean // 根据 result 是否存在判断
    result: any
    meta: any
    taskType: TaskType
    reason: TaskReason
    isTerminated: boolean
  }[]
}

export type AudienceAnalysisTaskParams = {
  projectId: string
  platform: string
  source: string
}

export type AudienceFakeTaskParams = {
  handler: string
  platform: KolPlatform
}

export type TrackEasyKOLTaskParams = {
  spreadsheetId: string
  tiktok?: TiktokParams
  youtube?: YoutubeParams
  instagram?: InstagramParams
  douyin?: DouyinTrackParams
  xhs?: XhsParams
  tagIds?: string[]
}

export type TrackEasyKOLTaskRequestParams = {
  tiktok: TiktokParams
  youtube: YoutubeParams
  instagram: InstagramParams
  douyin: DouyinTrackParams
  xhs: XhsParams
}

export type InsHashTagBreakTaskResult = {
  taskId: string
  tag: string
  uniqueIds: string[]
  paginationToken: string
  hasMore: boolean
  progress: {
    total: number
    current: number
    count: number
  }
}

export type InsFollowingListTaskParams = {
  projectId: string
  platform: string
  username: string
  paginationToken: string
  currentCount?: number
  maxCount?: number
  total?: number
}

// 单词关注任务的返回结果
export type InsFollowingListTaskResult = {
  taskId: string
  username: string
  uniqueIds: string[]
  hasMore: boolean
  paginationToken: string
  progress: {
    total: number
    current: number
    count: number
  }
}

// tt bgm break request
export type TiktokBgmBreakRequest = {
  projectId: string
  platform: KolPlatform
  musicUrl: string
  reason?: string
}

// tt bgm task params
export type TtBgmBreakTaskParams = {
  projectId: string
  platform: string
  musicUrl: string
  musicId: string
  reason: string
  cursor: number
}

export type TtBgmBreakTaskResult = {
  taskId: string
  musicUrl: string
  uniqueIds: string[]
  hasMore: boolean
  cursor: number
  message?: string
}

// tt web list request
export type TiktokWebListRequest = {
  projectId: string
  platform: KolPlatform
  urls: string[] // tiktok的urls
  reason?: string
}

// tt web list task params
export type TtWebListTaskParams = {
  projectId: string
  platform: string
  urls: string[]
  reason: string
}

export type TtWebListTaskResult = {
  taskId: string
  uniqueIds: string[]
  message?: string
}

export type InsWebListTaskParams = {
  projectId: string
  platform: string
  urls: string[]
  reason: string
}

export type InsWebListTaskResult = {
  taskId: string
  usernames: string[]
  message?: string
}

export type InsTaggedBreakTaskParams = {
  projectId: string
  platform: string
  username: string
  paginationToken: string
  currentVideoCount?: number
  maxVideoCount?: number
}

export type InsTaggedBreakTaskResult = {
  taskId: string
  username: string
  usernames: string[]
  paginationToken: string
  hasMore: boolean
  total: number
  message?: string
  progress: {
    total: number
    current: number
    count: number
  }
}

export type YoutubeHashtagBreakTaskResult = {
  taskId: string
  tag: string
  channelIds: string[]
  paginationToken: string
  hasMore: boolean
  total: number
  message?: string
  progress: {
    total: number
    current: number
    count: number
  }
}

export type YoutubeSearchInputBreakTaskResult = {
  taskId: string
  searchInput: string
  channelIds: string[]
  paginationToken: string
  hasMore: boolean
  total: number
  message?: string
  progress: {
    total: number
    current: number
    count: number
  }
  geo?: string
  lang?: string
  duration?: string
  sort_by?: string
  upload_date?: string
}

export const SINGL_TASK_TYPE_ENUM = [
  TaskType.SIMILAR,
  TaskType.KEYWORD,
  TaskType.FOLLOWERS_SIMILAR,
  TaskType.WEB_LIST,
  TaskType.AUDIENCE_ANALYSIS,
  TaskType.EASYKOL_TRACK,
  TaskType.AUDIENCE_FAKE,
] as const

export type SINGL_TASK_TYPE = (typeof SINGL_TASK_TYPE_ENUM)[number]

export const PAGE_TASK_TYPE_ENUM = [
  TaskType.HASH_TAG_BREAK,
  TaskType.SEARCH_INPUT_BREAK,
  TaskType.FOLLOWING_LIST,
  TaskType.BGM_BREAK,
  TaskType.TAGGED_BREAK,
] as const

export type PAGE_TASK_TYPE = (typeof PAGE_TASK_TYPE_ENUM)[number]

// Instagram长时间爬取任务的过滤器参数类型
export interface InsLongCrawlerFilters {
  // 内容类别描述
  kolDescription: string
  // 粉丝数范围
  followerRange?: {
    min?: number
    max?: number
  }
  // 地区/国家筛选
  regions: string[]
  // 平均点赞数范围
  averageLikeCount?: {
    min?: number
    max?: number
  }
}

// YouTube长时间爬取任务的过滤器参数类型
export interface YtbLongCrawlerFilters {
  // 内容类别描述
  kolDescription: string
  // 订阅数范围
  followerRange?: {
    min?: number
    max?: number
  }
  // 地区/国家筛选
  regions: string[]
  // 平均播放量范围
  averagePlay?: {
    min?: number
    max?: number
  }
}

// Instagram长时间爬取任务的进度类型
export interface InsLongCrawlerProgress {
  processedUsers: string[]
  pendingUsersQueue: any // 序列化的优先级队列数据
  allPerfectFitUserNames: string[]
  allContentOkUserNames: string[]
  lastProcessedAt: string
  currentBatch: number
  batchLogs?: InsLongCrawlerBatchLogs[]
  allProcessedUsers?: string[] // 存储所有处理过的用户，用于任务暂停后恢复时去重
  hasConsumedQuotaCount: number // 已消耗的quota次数
}

// Instagram长时间爬取任务的参数类型
export interface InsLongCrawlerTaskParams {
  projectId: string
  platform: string
  filters: InsLongCrawlerFilters
  seedUsers: string[]
  maxQuotaCost: number
}

export interface InsLongCrawlerBatchLogs {
  batchNumber: number // 批次
  processedUsers: string[] // 已处理用户
  relatedUserNames: string[] // 相关用户总数
  uniqueRelatedUserNames: string[] // 去重后的相关用户总数
  perfectFitUserNames: string[] // AI相似符合
  contentOkUserNames: string[] // 完全匹配数
  quotaCost: number // 消耗的quota次数
  numberOfRuns: number // 运行次数
  timestamp: string // 时间戳
  success: boolean // 是否成功
  message?: string // 日志消息
}

// YouTube长时间爬取任务的进度类型
export interface YtbLongCrawlerProgress {
  processedUsers: string[]
  pendingUsersQueue: any // 序列化的优先级队列数据
  allPerfectFitUserNames: string[]
  allContentOkUserNames: string[]
  lastProcessedAt: string
  currentBatch: number
  batchLogs?: YtbLongCrawlerBatchLogs[]
  allProcessedUsers?: string[] // 存储所有处理过的用户，用于任务暂停后恢复时去重
  hasConsumedQuotaCount: number // 已消耗的quota次数
}

// YouTube长时间爬取任务的参数类型
export interface YtbLongCrawlerTaskParams {
  projectId: string
  platform: string
  filters: YtbLongCrawlerFilters
  seedUsers: string[]
  maxQuotaCost: number
}

export interface YtbLongCrawlerBatchLogs {
  batchNumber: number // 批次
  processedUsers: string[] // 已处理用户
  relatedChannelIds: string[] // 相关频道总数
  uniqueRelatedChannelIds: string[] // 去重后的相关频道总数
  perfectFitChannelIds: string[] // AI相似符合
  contentOkChannelIds: string[] // 完全匹配数
  quotaCost: number // 消耗的quota次数
  numberOfRuns: number // 运行次数
  timestamp: string // 时间戳
  success: boolean // 是否成功
  message?: string // 日志消息
}

// 候选用户接口
export interface candidates {
  contentOkUsers: InstagramPriorityUser[]
  perfectFitUsers: InstagramPriorityUser[]
}

// 批量创建受众分析任务的返回类型
export interface BatchAudienceTasksResponse {
  batchId: string // 批次记录ID
  results: BatchAudienceTaskResult[]
  summary: BatchAudienceTasksSummary
}

export interface BatchAudienceTaskResult {
  taskId: string
  link: string
  status: 'created' | 'failed'
  error?: string
  delay?: number
  batchIndex?: number
  username?: string // mode=1 时返回
  videoId?: string // mode=2 时返回
}

export interface BatchAudienceTasksSummary {
  total: number
  success: number
  failed: number
  batchSize: number
  totalBatches: number
  delayBetweenBatches: number
  mode: BatchAudienceMode
}
