import { KolPlatform, TaskType } from '@repo/database'
import { SearchSource } from './response/union-search.response'

export interface ProjectCandidate {
  kolId: string
  platformId: string
  platform: KolPlatform
  score: number
  reason?: SearchSource
}

export type ProjectCandidates = Record<
  TaskType,
  {
    kols: ProjectCandidate[]
    taskId: string
    updatedAt: Date
  }
>

export interface ProjectConfig {
  allowList?: string[]
  banList?: string[]
  /** @deprecated use kolDescription instead */
  requirement?: string
  kolDescription?: string
}

export interface SearchInputProjectCandidateMeta {
  token: string
  total: number
  hasMore: boolean
  progress: {
    count: number
    total: number
    current: number
  }
  updatedAt: string
  searchInput: string
  geo?: string
  lang?: string
  duration?: string
  sort_by?: string
  upload_date?: string
}

export interface SearchHashtagProjectCandidateMeta {
  tag: string
  total: number
  hasMore: boolean
  progress: {
    count: number
    total: number
    current: number
  }
  updatedAt: string
  paginationToken: string
}
