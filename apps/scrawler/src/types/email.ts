import SMTPTransport from 'nodemailer/lib/smtp-transport'
import { EmailContext } from './emailSend'

export type KolPrompt = {
  kolTitle: string
  kolDescription: string
  kolEmail: string
  kolHistoryEmails: string[]
  platformAccount: string
  infoLastUpdated: string
  projectTitle: string
  projectDescription: string
}

// 邮箱模版
export type EmailTemplatePrompt = {
  subject: string
  text: string
}

// ai prompt参数
export type EmailPrompt = {
  kolPrompt: KolPrompt
  emailTemplatePrompt: EmailTemplatePrompt
}

// 邮件返回的内容
export type EmailResult = {
  emailSubject: string
  emailBody: string
}

//邮件发送Request
export interface SendEmailRequest {
  email: string
  templateId: string
  kolId: string
  usingEmail: string
  projectId: string
}

export type SenderClient = {
  sendEmail: (
    params: {
      from: string
      to: string
      subject: string
      text: string
      cc?: string[]
      bcc?: string[]
    },
    context: EmailContext,
  ) => Promise<SMTPTransport.SentMessageInfo | undefined>
}

export type EmailEvaluateResult = {
  score: number
  details: {
    subject: string
    satisfied: boolean
    explanation: string
  }[]
}

/**
 * 从获取顺序上：RAPID > BIOGRAPHY > NANO > REVEAL_BUTTON
 */
export enum EmailSourceType {
  TEAM_VERIFIED = 'team_verified', // 经过审核确认的邮箱地址
  NANO_EMAIL_BUTTON = 'nano_email_button', // 从 nano 得到的官方邮箱
  BIO_EMAIL = 'bio_email', // 从简介中解析得到
  RAPID_EMAIL_BUTTON = 'rapid_email_button', // TT 和 Ins 是从移动端的按钮得到的官方邮箱，我们从 rapidapi 三方读取
  REVEAL_BUTTON = 'reveal_button', // YOUTUBE 前端人机验证得到之后会主动上报邮箱地址
  FRONT_BIO_EMAIL = 'front_bio_email', // 前端简介爬到主动上报的
  NANO_UNCLEAR = 'nano_unclear', // 从 nano 得到的邮箱（无详细来源）
  LINK_INFO = 'link_info', // 从外链中得到的
  USER_SUBMIT = 'user_submit', // 前端用户提交的邮箱，不可信
}

export const VERIFIED_EMAIL_SOURCES = [
  EmailSourceType.NANO_EMAIL_BUTTON,
  EmailSourceType.RAPID_EMAIL_BUTTON,
  EmailSourceType.REVEAL_BUTTON,
  EmailSourceType.BIO_EMAIL,
  EmailSourceType.FRONT_BIO_EMAIL,
  EmailSourceType.TEAM_VERIFIED,
]
