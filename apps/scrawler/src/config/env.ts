import {
  EvaluateEmailSystemPrompt,
  PersonalizationPromptBasic,
} from '@/services/aiTools/prompt/email.prompt'
import 'dotenv/config'
import { AIModelCodes } from './models'
import { DEFAULT_ALLOWED_EMAILS } from './whiteList'

function strToBoolean(env: string | undefined, defaultValue?: boolean): boolean {
  if (!env) {
    return defaultValue ?? false
  }
  return ['true', '1', 'yes', 'on'].includes(env.toLowerCase())
}

export const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379'
export const US_REDIS_URL = process.env.US_REDIS_URL || 'redis://localhost:6379'
export const REDIS_KEY_PREFIX = process.env.REDIS_KEY_PREFIX || 'easykol:dev:'
export const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY

export const OPENAI_API_KEY = process.env.OPENAI_API_KEY

export const TT_RAPID_API_KEY = process.env.TT_RAPID_API_KEY || '6b4a4e812aacc28f9c33e36896266249'
export const Y2B_RAPID_API_KEY =
  process.env.Y2B_RAPID_API_KEY || '845bc700d3msh6568bef37dc45c4p1ee085jsn98e798ce9542'
export const Y2B_RAPID_API_KEY_V2 = process.env.Y2B_RAPID_API_KEY_V2 || ''

export const SUPABASE_URL = process.env.SUPABASE_URL
export const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY
export const SUPABASE_SERVER_ROLE_KEY = process.env.SUPABASE_SERVER_ROLE_KEY
export const SUPABASE_JWT_SECRET = process.env.SUPABASE_JWT_SECRET || ''

export const NAINF_KEY = process.env.NAINF_KEY

export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID
export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET

export const OSS_ACCESS_KEY_ID = process.env.OSS_ACCESS_KEY_ID
export const OSS_ACCESS_KEY_SECRET = process.env.OSS_ACCESS_KEY_SECRET
export const OSS_BUCKET = process.env.OSS_BUCKET
export const OSS_REGION = process.env.OSS_REGION
export const OSS_ENDPOINT = process.env.OSS_ENDPOINT

export const HOST = process.env.HOST || 'http://localhost:3000'

// Slack API 相关配置
export const SLACK_BOT_TOKEN = process.env.SLACK_BOT_TOKEN
export const SLACK_NOTIFY_USERS_JSON =
  process.env.SLACK_NOTIFY_USERS_JSON || '[{"id":"U07F03DMRLN","name":"李颖"}]'
export const EASYKOL_WARNING_CHANNEL = process.env.EASYKOL_WARNING_CHANNEL || 'easykol-研发'
export const API_QUOTA_WARNING_THRESHOLD = +(process.env.API_QUOTA_WARNING_THRESHOLD || '50000')

export const MILVUS_URL = process.env.MILVUS_URL || 'http://localhost:19530'
export const MILVUS_PASSWORD = process.env.MILVUS_PASSWORD
export const MILVUS_USERNAME = process.env.MILVUS_USERNAME

export const NODE_ENV = process.env.NODE_ENV || 'development'

export const PROXY_HOST = process.env.PROXY_HOST
export const PROXY_PORT = process.env.PROXY_PORT || '42000'
export const PROXY_USERNAME = process.env.PROXY_USERNAME
export const PROXY_PASSWORD = process.env.PROXY_PASSWORD

// 热配置
export const BATCH_FETCH_TASK_COUNT = process.env.BATCH_FETCH_TASK_COUNT || '10'
export const EMBEDDING_TASK_TIMEOUT = process.env.EMBEDDING_TASK_TIMEOUT || '600_000'
export const EMBEDDING_RETRY_ATTEMPTS = process.env.EMBEDDING_RETRY_ATTEMPTS || '3'
export const CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS =
  process.env.CONCURRENCY_FOR_FETCHING_RELATED_CHANNELS || '100'
export const CONCURRENCY_FOR_FETCHING_CHANNEL_INFORMATION =
  process.env.CONCURRENCY_FOR_FETCHING_CHANNEL_INFORMATION || '100'

// 任务调度器
export const TASK_SCHEDULER_INTERVAL = process.env.TASK_SCHEDULER_INTERVAL || '*/3 * * * *'
export const TASK_SCHEDULER_BATCH_SIZE = process.env.TASK_SCHEDULER_BATCH_SIZE || '4'
export const TASK_SCHEDULER_ENABLED = process.env.TASK_SCHEDULER_ENABLED || 'true'

//# currency

export const EMBEDDING_QUEUE_TASK_CONCURRENCY = process.env.EMBEDDING_QUEUE_TASK_CONCURRENCY || '5'

// 爬取的最大数量
export const TIKTOK_MAX_CONCURRENT_REQUESTS = process.env.TIKTOK_MAX_CONCURRENT_REQUESTS || '10'
export const TIKTOK_MAX_HASHTAGS_TO_PROCESS = process.env.TIKTOK_MAX_HASHTAGS_TO_PROCESS || '10'
export const TIKTOK_COMBINED_HASHTAGS_TO_PROCESS =
  process.env.TIKTOK_COMBINED_HASHTAGS_TO_PROCESS || '6'

// milvus collection name
export const EASYKOL_MILVUS_COLLECTION_NAME =
  process.env.EASYKOL_MILVUS_COLLECTION_NAME || 'easykol_influencer_embedding'

export const USER_VIDEO_LIMIT = parseInt(process.env.USER_VIDEO_LIMIT || '10')
export const RECOMMAND_VIDEO_LIMIT = parseInt(process.env.RECOMMAND_VIDEO_LIMIT || '20')
export const MAX_SOURCE_AUTHOR_VIDEOS = process.env.MAX_SOURCE_AUTHOR_VIDEOS || '30'
export const TIKTOK_MAX_CONCURRENCY = process.env.TIKTOK_MAX_CONCURRENCY || '300'
export const OPENAI_MODEL = process.env.OPENAI_MODEL || 'gpt-4o-mini'
// 默认模型兜底顺序
export const DEFAULT_MODEL_FAILOVER_ORDER = (process.env.DEFAULT_MODEL_FAILOVER_ORDER?.split(
  ',',
) || ['gemini-2.0-flash', 'gpt-4o-mini', 'doubao-seed-1-6-flash-250615']) as AIModelCodes[]
export const TIKTOK_VIDEO_BATCH_SIZE = process.env.TIKTOK_VIDEO_BATCH_SIZE || '100'

// tiktok分数转换系数
export const TIKTOK_SIMILAR_SCORE_ADJUSTMENT =
  process.env.TIKTOK_SIMILAR_SCORE_ADJUSTMENT || '0.938'
export const INSTAGRAM_MIN_SIMILAR_SCORE = process.env.INSTAGRAM_MIN_SIMILAR_SCORE || '0.45'

// milvus的search参数限制
export const MILVUS_LIMIT = process.env.MILVUS_LIMIT || '500'
export const MILVUS_TOPK = process.env.MILVUS_TOPK || '500'
// milvus tiktok milvus search topk and limit
export const TIKTOK_MILVUS_SEARCH_TOPK = process.env.TIKTOK_MILVUS_SEARCH_TOPK || '100'
export const TIKTOK_MILVUS_SEARCH_LIMIT = process.env.TIKTOK_MILVUS_SEARCH_LIMIT || '100'
// youtube milvus search topk and limit
export const YOUTUBE_MILVUS_SEARCH_TOPK = process.env.YOUTUBE_MILVUS_SEARCH_TOPK || '100'
export const YOUTUBE_MILVUS_SEARCH_LIMIT = process.env.YOUTUBE_MILVUS_SEARCH_LIMIT || '100'

// 对应tiktok三种爬取方式控制开关
export const TIKTOK_USE_SEARCH_WORDS = process.env.TIKTOK_USE_SEARCH_WORDS || 'true'
export const TIKTOK_USE_HASHTAGS = process.env.TIKTOK_USE_HASHTAGS || 'false'
export const TIKTOK_USE_TAG_VIDEOS = process.env.TIKTOK_USE_TAG_VIDEOS || 'true'
// 对应tiktok三种方式爬取视频的数量
export const TIKTOK_MAX_HASHTAG_VIDEOS = process.env.TIKTOK_MAX_HASHTAG_VIDEOS || '30'
export const TIKTOK_SEARCH_WORDS_VIDEOS_COUNT = process.env.TIKTOK_SEARCH_WORDS_VIDEOS_COUNT || '30'
export const TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT =
  process.env.TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT || '30'

export const EMBEDDING_CONCURRENCY = process.env.EMBEDDING_CONCURRENCY || '50'

export const TT_RAPID_API_KEY_USER_POST =
  process.env.TT_RAPID_API_KEY_USER_POST || '6b4a4e812aacc28f9c33e36896266249'
export const MILVUS_THRESHOLD = process.env.MILVUS_THRESHOLD || '0.5'
export const TIKTOK_MAX_VIDEOS_FOR_AVERAGE = process.env.TIKTOK_MAX_VIDEOS_FOR_AVERAGE || '10'

// INSTAGRAM 相关系数
export const INSTAGRAM_POST_CONCURRENCY = process.env.INSTAGRAM_POST_CONCURRENCY || '10'
export const TIKTOK_API_BASE_URL =
  process.env.TIKTOK_API_BASE_URL || 'https://api-node-us.tikwm.com'

export const TIKTOK_KEYWORDS_SEARCH_HASHTAG_VIDEOS_COUNT =
  process.env.TIKTOK_KEYWORDS_SEARCH_HASHTAG_VIDEOS_COUNT || '300'
export const TIKTOK_KEYWORDS_SEARCH_WORDS_VIDEOS_COUNT =
  process.env.TIKTOK_KEYWORDS_SEARCH_WORDS_VIDEOS_COUNT || '300'

export const DEBUG_TOKEN = process.env.DEBUG_TOKEN || 'easykol123'
/** features */
// Slack email for non-production environments
export const SLACK_ALERT_EMAIL = '<EMAIL>'
export const FEATURE_ENABLE_POLISH_EMAIL =
  process.env.FEATURE_ENABLE_POLISH_EMAIL?.toLowerCase() == 'true' || false
export const TIKTOK_SEARCH_SORT_TYPE = process.env.TIKOTK_SEARCH_SORT_TYPE || '1'
export const FEATURE_ERROR_ON_MISMATCH_REGION =
  process.env.FEATURE_ERROR_ON_MISMATCH_REGION == 'true' || true
export const FEATURE_YOUTUBE_API_DETECT = strToBoolean(process.env.FEATURE_YOUTUBE_API_DETECT, true)
export const FEATURE_YOUTUBE_API_DETECT_INTERVALMS = parseInt(
  process.env.FEATURE_YOUTUBE_API_DETECT_INTERVALMS || '10000',
)
export const FEATURE_INSTAGRAM_API_DETECT = strToBoolean(
  process.env.FEATURE_INSTAGRAM_API_DETECT,
  true,
)
export const FEATURE_PROXY_ON = strToBoolean(process.env.FEATURE_PROXY_ON, false)
export const FEATURE_INSTAGRAM_API_DETECT_INTERVALMS = parseInt(
  process.env.FEATURE_INSTAGRAM_API_DETECT_INTERVALMS || '20000',
)

/*****************
 *    Prompts    *
 *****************/
export const EMAIL_EVALUATION_PROMPT =
  process.env.EMAIL_EVALUATION_PROMPT || EvaluateEmailSystemPrompt()

export const EMAIL_PERSONALIZATION_BASIC_PROMPT =
  process.env.EMAIL_PERSONALIZATION_BASIC || PersonalizationPromptBasic()
export const TIKTOK_SINGLE_VECTOR_SEARCH_RESULT_LIMIT =
  process.env.TIKTOK_SINGLE_VECTOR_SEARCH_RESULT_LIMIT || '300'
export const TIKTOK_MAX_COMMENT_COUNT = process.env.TIKTOK_MAX_COMMENT_COUNT || '1000'
export const TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COUNT =
  process.env.TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COUNT || '10'
export const TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COMMENTS_COUNT =
  process.env.TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COMMENTS_COUNT || '1000'

export const YOUTUBE_USER_VIDEO_COUNT = +(process.env.YOUTUBE_USER_VIDEO_COUNT || '10')
export const YOUTUBE_RELATED_VIDEO_COUNT = +(process.env.YOUTUBE_RELATED_VIDEO_COUNT || '10')

/**
 * whitelist for common fields
 */
export const WHITELIST_EMAILS =
  process.env.WHITELIST_EMAILS?.split(',') || DEFAULT_ALLOWED_EMAILS?.split(',')

// bullmq 限流器
export const YOUTUBE_QUEUE_TASK_LIMIT = process.env.YOUTUBE_QUEUE_TASK_LIMIT || '40'
export const YOUTUBE_QUEUE_TASK_DURATION = process.env.YOUTUBE_QUEUE_TASK_DURATION || '60000'
export const INSTAGRAM_QUEUE_TASK_LIMIT = process.env.INSTAGRAM_QUEUE_TASK_LIMIT || '20'
export const INSTAGRAM_QUEUE_TASK_DURATION = process.env.INSTAGRAM_QUEUE_TASK_DURATION || '60000'
export const TIKTOK_QUEUE_TASK_LIMIT = process.env.TIKTOK_QUEUE_TASK_LIMIT || '20'
export const TIKTOK_QUEUE_TASK_DURATION = process.env.TIKTOK_QUEUE_TASK_DURATION || '60000'
export const EASYKOL_TRACK_QUEUE_TASK_LIMIT = process.env.EASYKOL_TRACK_QUEUE_TASK_LIMIT || '10'
export const EASYKOL_TRACK_QUEUE_TASK_DURATION =
  process.env.EASYKOL_TRACK_QUEUE_TASK_DURATION || '60000'
export const TWITTER_QUEUE_TASK_LIMIT = process.env.TWITTER_QUEUE_TASK_LIMIT || '30'
export const TWITTER_QUEUE_TASK_DURATION = process.env.TWITTER_QUEUE_TASK_DURATION || '60000'

// 三种队列最大能入队列的数量
export const YOUTUBE_CONCURRENCY = process.env.YOUTUBE_CONCURRENCY || '50'
export const TIKTOK_CONCURRENCY = process.env.TIKTOK_CONCURRENCY || '50'
export const INSTAGRAM_CONCURRENCY = process.env.INSTAGRAM_CONCURRENCY || '20'
export const EASYKOL_TRACK_CONCURRENCY = process.env.EASYKOL_TRACK_CONCURRENCY || '5'
export const TWITTER_CONCURRENCY = process.env.TWITTER_CONCURRENCY || '30'

// 联合搜索时，milvus的uniqueId批量处理大小
export const MILVUS_UNIQUEID_BATCH_SIZE = process.env.MILVUS_UNIQUEID_BATCH_SIZE || '20'

// tt默认的lastPublishedDays
export const YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS =
  process.env.YOUTUBE_DEFAULT_LAST_PUBLISHED_DAYS || '30'
export const TIKTOK_DEFAULT_LAST_PUBLISHED_DAYS =
  process.env.TIKTOK_DEFAULT_LAST_PUBLISHED_DAYS || '30'
export const INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS =
  process.env.INSTAGRAM_DEFAULT_LAST_PUBLISHED_DAYS || '30'
export const TWITTER_DEFAULT_LAST_PUBLISHED_DAYS =
  process.env.TWITTER_DEFAULT_LAST_PUBLISHED_DAYS || '30'

export const FREE_DEFAULT_ACCOUNT_QUOTA = process.env.FREE_DEFAULT_ACCOUNT_QUOTA || '30'

export const CORS_ALLOWED_ORIGINS = process.env.CORS_ALLOWED_ORIGINS?.split(',') || [
  'https://www.youtube.com',
  'http://localhost:3000',
  'http://localhost:3001',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
]
// 付费用户超级喜欢次数限制
export const SUPERLIKE_LIMIT = process.env.SUPERLIKE_LIMIT || '10'
// 付费用户超级喜欢时间窗口
export const SUPERLIKE_WINDOW_SIZE = process.env.SUPERLIKE_WINDOW_SIZE || '60'

// 最小可用版本号
export const EASYKOL_MIN_ACCESSIBLE_VERSION = process.env.EASYKOL_MIN_ACCESSIBLE_VERSION || '2.2.11'

// 免费用户配额策略
export const FREE_QUOTA_FIRST_DAY = process.env.FREE_QUOTA_FIRST_DAY || '100'
export const FREE_QUOTA_FIRST_WEEK = process.env.FREE_QUOTA_FIRST_WEEK || '20'
export const FREE_QUOTA_FIRST_MONTH = process.env.FREE_QUOTA_FIRST_MONTH || '9'
export const FREE_QUOTA_REGULAR = process.env.FREE_QUOTA_REGULAR || '1'
export const FREE_QUOTA_FIRST_WEEK_DAYS = process.env.FREE_QUOTA_FIRST_WEEK_DAYS || '7'
export const FREE_QUOTA_AFTER_FIRST_MONTH_DAYS =
  process.env.FREE_QUOTA_AFTER_FIRST_MONTH_DAYS || '30'

// 是否使用库存
export const TT_SIMILAR_QUERY_USE_STOCK = process.env.TT_SIMILAR_QUERY_USE_STOCK || 'true'
export const INSTAGRAM_SECOND_ROUND_LIMIT = process.env.INSTAGRAM_SECOND_ROUND_LIMIT || '3'

// google service account email
export const GOOGLE_SERVICE_ACCOUNT_EMAIL = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL || ''
// google service account private key
export const GOOGLE_PRIVATE_KEY = process.env.GOOGLE_PRIVATE_KEY || ''
export const GOOGLE_PROJECT_ID = process.env.GOOGLE_PROJECT_ID || ''

// 投放的视频评论的数量
export const TIKTOK_VIDEO_MAX_COMMENT_COUNT = process.env.TIKTOK_VIDEO_MAX_COMMENT_COUNT || '500'

export const YOUTUBE_EMBEDDING_TYPE = process.env.YOUTUBE_EMBEDDING_TYPE || 'milvus'
export const INSTAGRAM_EMBEDDING_TYPE = process.env.INSTAGRAM_EMBEDDING_TYPE || 'ai'
export const TIKTOK_EMBEDDING_TYPE = process.env.TIKTOK_EMBEDDING_TYPE || 'milvus'

export const YOUTUBE_SEARCH_VIDEO_PAGE_COUNT = Math.min(
  10,
  Math.max(1, +(process.env.YOUTUBE_SEARCH_VIDEO_PAGE_COUNT || '5')),
)

export const FEATURE_EXCLUDE_LINK =
  (process.env.FEATURE_EXCLUDE_LINK || 'false').toLowerCase() === 'true'
export const PRICE_FOR_EXCLUDE_LINK = Number(process.env.PRICE_FOR_EXCLUDE_LINK || '0.2')
export const MIN_COST_FOR_EXCLUDE_LINK = +(process.env.MIN_COST_FOR_EXCLUDE_LINK || '1')

export const RANKING_LLM_NAME = process.env.RANKING_LLM_NAME || 'gemini-2.0-flash-search'

export const COMMENTS_PORTAIT_LLM_NAME = process.env.COMMENTS_PORTAIT_LLM_NAME || 'gemini-2.0-flash'

export const DEFAULT_WELCOME_PAGE_URL =
  process.env.DEFAULT_WELCOME_PAGE_URL || 'https://easykol.com'

export const YOUTUBE_EMBEDDING_MIN_SCORE = Math.max(
  0,
  Math.min(0.99, +(process.env.YOUTUBE_EMBEDDING_MIN_SCORE || 0.5)),
)

export const FEATURE_DEVICE_BAN = process.env.FEATURE_DEVICE_BAN || 'false'

export const INSTAGRAM_API_CONCURRENCY = +(process.env.INSTAGRAM_API_CONCURRENCY || '10')
// google map api key
export const GOOGLE_MAP_API_KEY =
  process.env.GOOGLE_MAP_API_KEY || 'AIzaSyCQoByW-mfFTV7GlmLngnCOM2i6L5Zm6j4'

export const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY || ''

export const INS_ROUND_USER_COUNT = process.env.INS_ROUND_USER_COUNT || '80'

//douyin
export const DOUYIN_V1_TOKEN = process.env.DOUYIN_V1_TOKEN || 'mnKOuRUF'
export const DOUYIN_V1_HOST = process.env.DOUYIN_V1_HOST || '*************:8008'

// xhs
export const XHS_V1_HOST = process.env.XHS_V1_HOST || '*************:8008'
export const XHS_V1_TOKEN = process.env.XHS_V1_TOKEN || 'mnKOuRUF'

// instagram bearer token
export const INS_RAPID_AUTH_TOKEN = process.env.INS_RAPID_AUTH_TOKEN
export const INS_RAPID_API_HOST = process.env.INS_RAPID_API_HOST || 'api.socialapi.io/ig'
// instagram rapid api key
export const IG_RAPID_API_HOST =
  process.env.IG_RAPID_API_HOST || 'instagram-scraper-20252.p.rapidapi.com'
export const IG_RAPID_API_KEY = process.env.IG_RAPID_API_KEY
// Instagram API认证模式: bearer(Bearer令牌) 或 rapidapi(RapidAPI Key)
export const IG_AUTH_MODE = process.env.IG_AUTH_MODE || 'rapid_api_key'
export const INS_RAPID_API_V3_CONCURRENCY = process.env.INS_RAPID_API_V3_CONCURRENCY || '100'

export const YTB_MODE_DEFAULT = process.env.YTB_MODE_DEFAULT || '1'
export const TT_MODE_DEFAULT = process.env.TT_MODE_DEFAULT || '1'
export const INS_MODE_DEFAULT = process.env.INS_MODE_DEFAULT || '2'
export const TWITTER_MODE_DEFAULT = process.env.TWITTER_MODE_DEFAULT || '2'

export const PLATFORM_DAILY_LIMIT = parseInt(process.env.PLATFORM_DAILY_LIMIT || '10000', 10)

export const INS_VISIUAL_SIMILARITY_BATCH_SIZE =
  process.env.INS_VISIUAL_SIMILARITY_BATCH_SIZE || '10'
export const INS_VISIUAL_SIMILARITY_CONCURRENCY =
  process.env.INS_VISIUAL_SIMILARITY_CONCURRENCY || '25'

export const PROJECT_THREE_ELEMENTS_TYPE = process.env.PROJECT_THREE_ELEMENTS_TYPE || 'default'

export const FILTER_OFFICIAL_ACCOUNT = process.env.FILTER_OFFICIAL_ACCOUNT || 'false'

export const EXTERNAL_LINK_MAX_RAPID_API_REQUEST = 10

// 长任务队列配置
export const LONG_CRAWLER_TASK_QUEUE_TASK_LIMIT =
  process.env.LONG_CRAWLER_TASK_QUEUE_TASK_LIMIT || '20'
export const LONG_CRAWLER_TASK_QUEUE_TASK_DURATION =
  process.env.LONG_CRAWLER_TASK_QUEUE_TASK_DURATION || '180000'
export const LONG_CRAWLER_TASK_CONCURRENCY = process.env.LONG_CRAWLER_TASK_CONCURRENCY || '20'
export const LONG_CRAWLER_INS_BATCH_SIZE = process.env.LONG_CRAWLER_INS_BATCH_SIZE || '1'

export const FEISHU_SHORTCUT_PUBLIC_KEY = process.env.FEISHU_SHORTCUT_PUBLIC_KEY

export const YOUTUBE_FETCH_CHANNEL_CONCURRENCY =
  process.env.YOUTUBE_FETCH_CHANNEL_CONCURRENCY || '10'

// Aihubmix API配置
export const AIHUBMIX_MONITORING_API_KEY = process.env.AIHUBMIX_MONITORING_API_KEY || ''

export const TWITTER241_API_KEY = process.env.TWITTER241_API_KEY || ''
export const TWITTER_SIMILAR_FOLLOWING_COUNT = process.env.TWITTER_SIMILAR_FOLLOWING_COUNT || '300'
// Cloudflare Worker 图片代理配置
export const CLOUDFLARE_WORKER_URL =
  process.env.CLOUDFLARE_WORKER_URL || 'https://frosty-bird-6fcf.lijianmin.workers.dev'

// Cloudflare R2 存储配置
export const CLOUDFLARE_R2_ACCOUNT_ID = process.env.CLOUDFLARE_R2_ACCOUNT_ID!
export const CLOUDFLARE_R2_ACCESS_KEY_ID = process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!
export const CLOUDFLARE_R2_SECRET_ACCESS_KEY = process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!
export const CLOUDFLARE_R2_ENDPOINT = process.env.CLOUDFLARE_R2_ENDPOINT!
export const CLOUDFLARE_R2_BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
export const CLOUDFLARE_R2_PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// jina v4 milvus collection name
export const EASYKOL_JINA_V4_MILVUS_COLLECTION_NAME =
  process.env.EASYKOL_JINA_V4_MILVUS_COLLECTION_NAME || 'easykol_jina_v4_beta'

// jina api key
export const JINA_API_KEY = process.env.JINA_API_KEY || ''

// instagram dimension batch size
export const INS_DIMENSION_BATCH_SIZE = process.env.INS_DIMENSION_BATCH_SIZE || '10'
export const INS_DIMENSION_BATCH_CONCURRENCY = process.env.INS_DIMENSION_BATCH_CONCURRENCY || '10'

// instagram jina embedding batch size
export const INS_JINA_EMBEDDING_BATCH_SIZE = process.env.INS_JINA_EMBEDDING_BATCH_SIZE || '10'
export const INS_JINA_EMBEDDING_BATCH_CONCURRENCY =
  process.env.INS_JINA_EMBEDDING_BATCH_CONCURRENCY || '20'

export const LONG_CRAWLER_YTB_BATCH_SIZE = process.env.LONG_CRAWLER_YTB_BATCH_SIZE || '1'
