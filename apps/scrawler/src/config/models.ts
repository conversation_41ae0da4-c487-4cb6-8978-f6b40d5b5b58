import { AIModelNames } from '@/api/openai'

export type AIModelCodes =
  | 'gpt-3.5-turbo'
  | 'gpt-4o'
  | 'gpt-4o-mini'
  | 'gemini-2.0-flash'
  | 'gemini-2.0-flash-search'
  | 'gemini-2.0-flash-exp-search'
  | 'gemini-2.5-flash'
  | 'gemini-2.5-flash-lite'
  | 'doubao-seed-1-6-250615'
  | 'doubao-seed-1-6-flash-250615'

export const AIModelMap: Record<AIModelCodes, AIModelNames> = {
  'gpt-3.5-turbo': {
    aihubmix: 'gpt-3.5-turbo',
    azureOpenai: undefined,
    openRouterApi: 'openai/gpt-3.5-turbo-0613',
  },
  'gpt-4o': {
    aihubmix: 'gpt-4o',
    azureOpenai: undefined,
    openRouterApi: 'openai/gpt-4o-2024-11-20',
  },
  'gpt-4o-mini': {
    aihubmix: 'gpt-4o-mini',
    azureOpenai: undefined,
    openRouterApi: 'openai/gpt-4o-mini',
  },
  'gemini-2.0-flash': {
    aihubmix: 'gemini-2.0-flash',
    azureOpenai: undefined,
    openRouterApi: 'google/gemini-2.0-flash-001',
  },
  'gemini-2.0-flash-search': {
    aihubmix: 'gemini-2.0-flash-search',
    azureOpenai: undefined,
    openRouterApi: undefined,
  },
  'gemini-2.0-flash-exp-search': {
    aihubmix: 'gemini-2.0-flash-exp-search',
    azureOpenai: undefined,
    openRouterApi: undefined,
  },
  'gemini-2.5-flash': {
    aihubmix: 'gemini-2.5-flash',
    azureOpenai: undefined,
    openRouterApi: 'google/gemini-2.5-flash',
  },
  'gemini-2.5-flash-lite': {
    aihubmix: 'gemini-2.5-flash-lite-preview-06-17',
    azureOpenai: undefined,
    openRouterApi: 'google/gemini-2.5-flash-lite-preview-06-17',
  },
  'doubao-seed-1-6-250615': {
    aihubmix: 'doubao-seed-1-6-250615',
    azureOpenai: undefined,
    openRouterApi: undefined,
  },
  'doubao-seed-1-6-flash-250615': {
    aihubmix: 'doubao-seed-1-6-flash-250615',
    azureOpenai: undefined,
    openRouterApi: undefined,
  },
}
