import { CellDataType } from '@/utils/excel'

// 基础导出列定义
export const baseKolColumns = [
  { header: 'nickname', key: 'kolTitle', width: 20, hyperlink: 'url' },
  { header: '@username', key: 'platformAccount', width: 20 },
  { header: 'Region', key: 'country', width: 15 },
  { header: 'Platform', key: 'platform', width: 15 },
  {
    header: 'Followers',
    key: 'numericSubscriberCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  {
    header: 'Avg. Views',
    key: 'videosAverageViewCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
]

// 发布统计列定义
export const publicationStatsColumns = [
  { header: 'Last Post', key: 'lastPublishedTime', width: 20 },
  {
    header: 'Posts (7d)>',
    key: 'weeklyPostCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  {
    header: 'Posts (30d)>',
    key: 'monthlyPostCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
]

// 邮件相关列定义
export const emailColumns = [
  { header: 'Email', key: 'kolEmail', width: 25 },
  { header: 'Email Send Status', key: 'emailRecord', width: 20 },
]

// 收藏博主导出列定义（包含SuperLike标识和收藏时间）
export const likesKolColumns = [
  { header: 'Likes Time', key: 'createdAt', width: 20 },
  ...baseKolColumns,
  { header: 'is SuperLike', key: 'isSuperlike', width: 15 },
  { header: 'Tags', key: 'tags', width: 30 },
  ...emailColumns,
  { header: 'YouTube Description Email Button', key: 'officialEmailButton', width: 20 },
  ...publicationStatsColumns,
  { header: 'URL', key: 'url', width: 25 },
]

// 相似博主导出列定义（TikTok）
export const similarTikTokColumns = [
  ...baseKolColumns,
  { header: 'Similarity score', key: 'score', width: 20 },
  { header: 'Tags', key: 'tags', width: 30 },
  ...emailColumns,
  ...publicationStatsColumns,
  { header: 'URL', key: 'url', width: 25 },
]

// 相似博主导出列定义（YouTube）
export const similarYouTubeColumns = [
  { header: 'nickname', key: 'kolTitle', width: 20, hyperlink: 'url' },
  { header: '@username', key: 'handle', width: 20 },
  { header: 'Region', key: 'country', width: 15 },
  { header: 'Platform', key: 'platform', width: 15 },
  {
    header: 'Followers',
    key: 'numericSubscriberCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  {
    header: 'Avg. Views',
    key: 'videosAverageViewCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  { header: 'Similarity score', key: 'score', width: 20 },
  { header: 'Tags', key: 'tags', width: 30 },
  ...emailColumns,
  {
    header: 'YouTube Description Email Button',
    key: 'officialEmailButton',
    width: 20,
  },
  ...publicationStatsColumns,
  { header: 'URL', key: 'url', width: 25 },
]

// 相似博主导出列定义（Instagram）
export const similarInstagramColumns = [
  ...baseKolColumns,
  {
    header: 'Avg. Likes',
    key: 'videosAverageViewCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  { header: 'Similarity score', key: 'score', width: 20 },
  { header: 'Tags', key: 'tags', width: 30 },
  ...emailColumns,
  ...publicationStatsColumns,
  { header: 'URL', key: 'url', width: 25 },
]

// twitter
export const similarTwitterColumns = [
  ...baseKolColumns,
  {
    header: 'Avg. Engagement',
    key: 'videosAverageViewCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  { header: 'Similarity score', key: 'score', width: 20 },
  { header: 'Tags', key: 'tags', width: 30 },
  ...emailColumns,
  ...publicationStatsColumns,
  { header: 'URL', key: 'url', width: 25 },
]

export const commonDebugColumns = [{ header: 'source', key: 'commonFields', width: 20 }]

export const audienceAnalysisAuthorsColumns = [
  { header: 'Username', key: 'username', width: 25, hyperlink: 'link' },
  { header: 'Platform', key: 'platform', width: 15 },
  { header: 'Male', key: 'malePercentage', width: 15 },
  { header: 'Female', key: 'femalePercentage', width: 15 },
  { header: 'Under 18', key: 'under18Percentage', width: 15 },
  { header: '18-25', key: 'age18to25Percentage', width: 15 },
  { header: '25-45', key: 'age25to45Percentage', width: 15 },
  { header: 'Above 45', key: 'above45Percentage', width: 15 },
  { header: '#1 Country', key: 'country1', width: 25 },
  { header: '#2 Country', key: 'country2', width: 25 },
  { header: '#3 Country', key: 'country3', width: 25 },
  { header: '#4 Country', key: 'country4', width: 25 },
  { header: '#5 Country', key: 'country5', width: 25 },
  { header: 'Market Level', key: 'marketLevel', width: 30 },
  { header: 'Created At', key: 'createdAt', width: 20 },
]

export const audienceAnalysisPostsColumns = [
  { header: 'Post Link', key: 'postLink', width: 40 },
  { header: 'Platform', key: 'platform', width: 15 },
  { header: 'Male', key: 'malePercentage', width: 15 },
  { header: 'Female', key: 'femalePercentage', width: 15 },
  { header: 'Under 18', key: 'under18Percentage', width: 15 },
  { header: '18-25', key: 'age18to25Percentage', width: 15 },
  { header: '25-45', key: 'age25to45Percentage', width: 15 },
  { header: 'Above 45', key: 'above45Percentage', width: 15 },
  { header: '#1 Country', key: 'country1', width: 25 },
  { header: '#2 Country', key: 'country2', width: 25 },
  { header: '#3 Country', key: 'country3', width: 25 },
  { header: '#4 Country', key: 'country4', width: 25 },
  { header: '#5 Country', key: 'country5', width: 25 },
  { header: 'Market Level', key: 'marketLevel', width: 30 },
  { header: 'Created At', key: 'createdAt', width: 20 },
]

// 假受众分析详细数据列配置
export const audienceFakeDetailColumns = [
  { header: 'Username', key: 'username', width: 20, hyperlink: 'profileUrl' },
  { header: 'Full Name', key: 'fullName', width: 25 },
  {
    header: 'Result',
    key: 'result',
    width: 15,
    cellStyle: (value: string) => {
      switch (value) {
        case 'Real People':
          return {
            fill: {
              type: 'pattern' as const,
              pattern: 'solid' as const,
              fgColor: { argb: 'FFC6EFCE' },
            },
          } // 浅绿色
        case 'KOL/KOC':
          return {
            fill: {
              type: 'pattern' as const,
              pattern: 'solid' as const,
              fgColor: { argb: 'FFFFEB9C' },
            },
          } // 浅黄色
        case 'Fake Accounts':
          return {
            fill: {
              type: 'pattern' as const,
              pattern: 'solid' as const,
              fgColor: { argb: 'FFFFC7CE' },
            },
          } // 浅红色
        default:
          return {}
      }
    },
  },
  { header: 'Reason', key: 'reason', width: 60 },
  { header: 'Followers', key: 'followerCount', width: 12, dataType: 'number' as CellDataType },
  { header: 'Following', key: 'followingCount', width: 12, dataType: 'number' as CellDataType },
  { header: 'Biography', key: 'biography', width: 30 },
  { header: 'Is_Private', key: 'isPrivate', width: 10 },
  { header: 'Is_Verified', key: 'isVerified', width: 10 },
  { header: 'Has_Anonymous_Profile_Picture', key: 'hasAnonymousProfilePicture', width: 20 },
]

// 定义Excel列
export const LongCrawlerExportColumns = [
  { header: 'nickname', key: 'kolTitle', width: 20, hyperlink: 'url' },
  { header: '@username', key: 'platformAccount', width: 20 },
  { header: 'Region', key: 'country', width: 15 },
  { header: 'Platform', key: 'platform', width: 15 },
  {
    header: 'Followers',
    key: 'numericSubscriberCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  {
    header: 'Avg. Likes',
    key: 'videosAverageViewCount',
    width: 15,
    dataType: 'number' as CellDataType,
    format: '0',
  },
  { header: 'Email', key: 'kolEmail', width: 25 },
  { header: 'URL', key: 'url', width: 25 },
]
