import {
  PRICE_ID_A_BASIC,
  PRICE_ID_A_PRO,
  PRICE_ID_B_BASIC,
  PRICE_ID_B_PRO,
  PRICE_ID_C_BASIC,
  PRICE_ID_C_PRO,
} from '@/config/stripe/stripe'
export interface PaymentPlan {
  id: string
  priceId: string
  scheme: 'A' | 'B' | 'C'
  type: 'BASIC' | 'PRO'
  amount: number
  currency: 'CNY' | 'USD'
  paymentMethods: string[]
  isSubscription: boolean
  description: string
  validityPeriod: number // 有效期数值
  validityUnit: 'day' | 'month' | 'year' // 有效期单位
  quotaAmount: number // 套餐对应的账户配额
}

// 支付方案配置
export const PAYMENT_PLANS: Record<string, PaymentPlan> = {
  // A方案：国内用户，人民币定价，一次性购买
  A_BASIC: {
    id: 'A_BASIC',
    priceId: PRICE_ID_A_BASIC || '',
    scheme: 'A',
    type: 'BASIC',
    amount: 300,
    currency: 'CNY',
    paymentMethods: ['wechat_pay', 'alipay', 'card'],
    isSubscription: false,
    description: 'Basic - Domestic One-time (￥300)',
    validityPeriod: 1,
    validityUnit: 'month',
    quotaAmount: 500,
  },
  A_PRO: {
    id: 'A_PRO',
    priceId: PRICE_ID_A_PRO || '',
    scheme: 'A',
    type: 'PRO',
    amount: 600,
    currency: 'CNY',
    paymentMethods: ['wechat_pay', 'alipay', 'card'],
    isSubscription: false,
    description: 'Pro - Domestic One-time (￥600)',
    validityPeriod: 1,
    validityUnit: 'month',
    quotaAmount: 1500,
  },

  // B方案：国外用户，美元定价，连续订阅
  B_BASIC: {
    id: 'B_BASIC',
    priceId: PRICE_ID_B_BASIC || '',
    scheme: 'B',
    type: 'BASIC',
    amount: 50,
    currency: 'USD',
    paymentMethods: ['card'],
    isSubscription: true,
    description: 'Basic - International ($50/mo)',
    validityPeriod: 1,
    validityUnit: 'month',
    quotaAmount: 500,
  },
  B_PRO: {
    id: 'B_PRO',
    priceId: PRICE_ID_B_PRO || '',
    scheme: 'B',
    type: 'PRO',
    amount: 100,
    currency: 'USD',
    paymentMethods: ['card'],
    isSubscription: true,
    description: 'Pro - International ($100/mo)',
    validityPeriod: 1,
    validityUnit: 'month',
    quotaAmount: 1500,
  },

  // C方案：现有方案，逐步废弃
  C_BASIC: {
    id: 'C_BASIC',
    priceId: PRICE_ID_C_BASIC || '',
    scheme: 'C',
    type: 'BASIC',
    amount: 300,
    currency: 'CNY',
    paymentMethods: ['card'],
    isSubscription: true,
    description: 'Basic - Legacy Subscription (￥300/mo)',
    validityPeriod: 1,
    validityUnit: 'month',
    quotaAmount: 500,
  },
  C_PRO: {
    id: 'C_PRO',
    priceId: PRICE_ID_C_PRO || '',
    scheme: 'C',
    type: 'PRO',
    amount: 600,
    currency: 'CNY',
    paymentMethods: ['card'],
    isSubscription: true,
    description: 'Pro -  Legacy Subscription (￥600/mo)',
    validityPeriod: 1,
    validityUnit: 'month',
    quotaAmount: 1500,
  },
}

// get payment plan by priceId
export function getPaymentPlanByPriceId(priceId: string): PaymentPlan | null {
  return Object.values(PAYMENT_PLANS).find((plan) => plan.priceId === priceId) || null
}

// get payment plan by scheme and type
export function getPaymentPlan(scheme: 'A' | 'B' | 'C', type: 'BASIC' | 'PRO'): PaymentPlan | null {
  const planId = `${scheme}_${type}`
  return PAYMENT_PLANS[planId] || null
}

// 获取配额金额
export function getQuotaAmountByPlanType(planType: string): number {
  // 默认配额值
  const DEFAULT_QUOTAS = {
    BASIC: 500,
    PRO: 1500,
  }

  // 查找配置中对应类型的第一个方案
  const plan = Object.values(PAYMENT_PLANS).find((plan) => plan.type === planType)

  // 如果找到了方案配置，返回配置的配额
  // 否则返回默认配额
  return plan ? plan.quotaAmount : DEFAULT_QUOTAS[planType as 'BASIC' | 'PRO'] || 500
}

// 根据方案和类型获取金额
export function getAmountBySchemeAndType(scheme: 'A' | 'B' | 'C', type: 'BASIC' | 'PRO'): string {
  const planId = `${scheme}_${type}`
  const plan = PAYMENT_PLANS[planId]

  if (!plan) {
    throw new Error(`Plan ${planId} not found`)
  }

  return `${plan.scheme === 'B' ? '$' : '¥'}${plan.amount}`
}

// Payment scheme name mapping
export const SCHEME_NAMES: Record<string, string> = {
  A: 'Domestic One-time',
  B: 'International Subscription',
  C: 'Legacy Subscription',
}

// check if language is simplified chinese
export function isSimplifiedChinese(language: string): boolean {
  return language.toLowerCase().includes('zh-cn') || language.toLowerCase() === 'zh'
}

// map stripe subscription status to prisma status
export function mapStripeStatusToPrismaStatus(
  status: string,
):
  | 'ACTIVE'
  | 'INACTIVE'
  | 'PAST_DUE'
  | 'CANCELED'
  | 'TRIALING'
  | 'INCOMPLETE'
  | 'INCOMPLETE_EXPIRED'
  | 'UNPAID'
  | 'PAUSED' {
  const statusMap: Record<
    string,
    | 'ACTIVE'
    | 'INACTIVE'
    | 'PAST_DUE'
    | 'CANCELED'
    | 'TRIALING'
    | 'INCOMPLETE'
    | 'INCOMPLETE_EXPIRED'
    | 'UNPAID'
    | 'PAUSED'
  > = {
    active: 'ACTIVE',
    past_due: 'PAST_DUE',
    canceled: 'CANCELED',
    unpaid: 'UNPAID',
    trialing: 'TRIALING',
    incomplete: 'INCOMPLETE',
    incomplete_expired: 'INCOMPLETE_EXPIRED',
    paused: 'PAUSED',
  }

  return statusMap[status] || 'INACTIVE'
}
