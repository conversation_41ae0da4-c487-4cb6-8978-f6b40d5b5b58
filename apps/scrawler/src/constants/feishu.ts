export enum FeishuPlatform {
  TIKTOK = 'TikTok',
  YOUTUBE = 'YouTube',
  INSTAGRAM = 'Instagram',
}

export enum FeishuLinkType {
  Kol = '主页',
  Video = '单帖',
}

export type FeishuTrackCommonRes = {
  id: string // 唯一ID
  kolUrl: string // 主页或单帖网址
  displayName: string // 昵称
  accountName: string // @帐号
  platform: string // 平台
  linkType: string // 主页OR单帖
  message: string
  fetchTime: number // 抓取时间
  useQuota: number // 使用额度
  apiKey: string // 使用的apiKey
}

export type FeishuTrackVideoRes = {
  videoViewsCount?: number // 单帖浏览量
  videoLikesCount?: number // 单帖点赞数
  videoCommentsCount?: number // 单帖评论数
  videoSavedCount?: number // 单帖收藏数
  videoER?: number // 单帖互动率
  videoTitle?: string // 单帖标题
  videoDescription?: string // 单帖描述
  videoPublishedTime?: number // 单帖发布时间
}

export const emptyVideoRes: FeishuTrackVideoRes = {
  videoViewsCount: undefined,
  videoLikesCount: undefined,
  videoCommentsCount: undefined,
  videoSavedCount: undefined,
  videoER: undefined,
  videoTitle: undefined,
  videoDescription: undefined,
  videoPublishedTime: undefined,
}

export type FeishuTrackKolRes = {
  country?: string // 国家
  email?: string // 邮箱
  subscriberCount?: number // 粉丝数
  lastUpdatedTime?: number // 最近更新时间
  medianViewsCount?: number // 中位浏览量（近10条视频）
  medianLikesCount?: number // 中位点赞数（近10条视频）
  medianER?: number // 中位互动率（近10条视频）
}
export const emptyKolRes: FeishuTrackKolRes = {
  country: undefined,
  email: undefined,
  subscriberCount: undefined,
  lastUpdatedTime: undefined,
  medianViewsCount: undefined,
  medianLikesCount: undefined,
  medianER: undefined,
}
export type FeishuTrackRes = FeishuTrackCommonRes & FeishuTrackKolRes & FeishuTrackVideoRes

export const Unsupported = (id: string, apiKey: string): FeishuTrackRes => {
  return {
    id: id,
    kolUrl: '',
    displayName: '暂不支持(不计费)',
    accountName: '需要此功能？请联系我们！',
    platform: '',
    linkType: '',
    country: undefined,
    email: undefined,
    subscriberCount: undefined,
    lastUpdatedTime: undefined,
    medianViewsCount: undefined,
    medianLikesCount: undefined,
    medianER: undefined,
    videoViewsCount: undefined,
    videoLikesCount: undefined,
    videoCommentsCount: undefined,
    videoSavedCount: undefined,
    videoER: undefined,
    videoTitle: undefined,
    videoDescription: undefined,
    videoPublishedTime: undefined,
    message: 'unsupported yet',
    fetchTime: Date.now(),
    useQuota: 0,
    apiKey: apiKey,
  }
}

export const CountryNameList = [
  '阿富汗',
  '阿尔巴尼亚',
  '阿尔及利亚',
  '美属萨摩亚',
  '安道尔',
  '安哥拉',
  '安圭拉',
  '南极洲',
  '安提瓜和巴布达',
  '争议地区',
  '阿根廷',
  '亚美尼亚',
  '阿鲁巴',
  '澳大利亚',
  '澳大利亚印度洋领地',
  '奥地利',
  '阿塞拜疆',
  '巴林',
  '孟加拉国',
  '巴巴多斯',
  '白俄罗斯',
  '比利时',
  '伯利兹',
  '贝宁',
  '百慕大',
  '不丹',
  '玻利维亚',
  '波斯尼亚和黑塞哥维那',
  '博茨瓦纳',
  '布韦岛',
  '巴西',
  '英属印度洋领地',
  '英属维尔京群岛',
  '文莱',
  '保加利亚',
  '布基纳法索',
  '布隆迪',
  '柬埔寨',
  '喀麦隆',
  '加拿大',
  '佛得角',
  '开曼群岛',
  '中非共和国',
  '乍得',
  '智利',
  '圣诞岛',
  '克利珀顿岛',
  '哥伦比亚',
  '科摩罗',
  '库克群岛',
  '哥斯达黎加',
  '克罗地亚',
  '古巴',
  '塞浦路斯',
  '捷克共和国',
  '刚果民主共和国',
  '丹麦',
  '吉布提',
  '多米尼克',
  '多米尼加共和国',
  '东帝汶',
  '厄瓜多尔',
  '埃及',
  '萨尔瓦多',
  '赤道几内亚',
  '厄立特里亚',
  '爱沙尼亚',
  '埃塞俄比亚',
  '福克兰群岛',
  '法罗群岛',
  '密克罗尼西亚联邦',
  '斐济',
  '芬兰',
  '法国',
  '法属圭亚那',
  '法属波利尼西亚',
  '法属南部和南极领地',
  '加蓬',
  '格鲁吉亚',
  '德国',
  '加纳',
  '直布罗陀',
  '希腊',
  '格陵兰',
  '格林纳达',
  '瓜德罗普',
  '危地马拉',
  '根西岛',
  '几内亚',
  '几内亚比绍',
  '圭亚那',
  '海地',
  '赫德岛和麦克唐纳群岛',
  '洪都拉斯',
  '匈牙利',
  '冰岛',
  '印度尼西亚',
  '伊朗',
  '伊拉克',
  '爱尔兰',
  '马恩岛',
  '以色列',
  '意大利',
  '科特迪瓦',
  '牙买加',
  '日本',
  '约旦',
  '哈萨克斯坦',
  '肯尼亚',
  '基里巴斯',
  '科威特',
  '吉尔吉斯斯坦',
  '老挝',
  '拉脱维亚',
  '黎巴嫩',
  '莱索托',
  '利比里亚',
  '利比亚',
  '列支敦士登',
  '立陶宛',
  '卢森堡',
  '马达加斯加',
  '马拉维',
  '马来西亚',
  '马尔代夫',
  '马里',
  '马耳他',
  '马绍尔群岛',
  '马提尼克',
  '毛里塔尼亚',
  '毛里求斯',
  '马约特',
  '墨西哥',
  '摩尔多瓦',
  '摩纳哥',
  '蒙古',
  '黑山',
  '蒙特塞拉特',
  '摩洛哥',
  '莫桑比克',
  '缅甸',
  '纳米比亚',
  '瑙鲁',
  '尼泊尔',
  '荷兰',
  '荷属安的列斯',
  '新喀里多尼亚',
  '新西兰',
  '尼加拉瓜',
  '尼日尔',
  '尼日利亚',
  '纽埃',
  '诺福克岛',
  '朝鲜',
  '北马里亚纳群岛',
  '挪威',
  '阿曼',
  '巴基斯坦',
  '帕劳',
  '巴拿马',
  '巴布亚新几内亚',
  '巴拉圭',
  '中国',
  '秘鲁',
  '菲律宾',
  '皮特凯恩群岛',
  '波兰',
  '葡萄牙',
  '爱德华王子岛',
  '波多黎各',
  '卡塔尔',
  '马其顿共和国',
  '刚果共和国',
  '留尼汪',
  '罗马尼亚',
  '俄罗斯',
  '卢旺达',
  '圣赫勒拿',
  '圣基茨和尼维斯',
  '圣卢西亚',
  '圣皮埃尔和密克隆',
  '圣文森特和格林纳丁斯',
  '萨摩亚',
  '圣马力诺',
  '沙特阿拉伯',
  '塞内加尔',
  '塞尔维亚',
  '塞舌尔',
  '塞拉利昂',
  '新加坡',
  '斯洛伐克',
  '斯洛文尼亚',
  '所罗门群岛',
  '索马里',
  '南非',
  '南乔治亚岛和南桑威奇群岛',
  '韩国',
  '西班牙',
  '斯里兰卡',
  '苏丹',
  '苏里南',
  '斯瓦尔巴和扬马延',
  '瑞典',
  '瑞士',
  '叙利亚',
  '圣多美和普林西比',
  '塔吉克斯坦',
  '坦桑尼亚',
  '泰国',
  '巴哈马',
  '冈比亚',
  '多哥',
  '托克劳',
  '汤加',
  '特立尼达和多巴哥',
  '突尼斯',
  '土耳其',
  '土库曼斯坦',
  '特克斯和凯科斯群岛',
  '图瓦卢',
  '乌干达',
  '乌克兰',
  '阿拉伯联合酋长国',
  '英国',
  '美属维尔京群岛',
  '美国',
  '乌拉圭',
  '乌兹别克斯坦',
  '瓦努阿图',
  '梵蒂冈城',
  '委内瑞拉',
  '越南',
  '瓦利斯和富图纳',
  '西撒哈拉',
  '也门',
  '赞比亚',
  '津巴布韦',
  '斯威士兰',
]
