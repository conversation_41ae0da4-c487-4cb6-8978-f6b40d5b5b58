import { RapidApiStatus } from '@/api/@types/rapidapi/RapidApi'
import {
  Channel,
  ChannelShortsResponse,
  ChannelVideo,
  CommentResponse,
  Link,
  RelatedVideo,
  SearchVideoResult,
  Thumbnail,
  VideoDetail,
  YoutubeHashTagBreakRes,
} from '@/api/@types/rapidapi/Youtube'
import RapidApiStatsCollector from '@/api/ApiCallStat'
import { parsePathParams } from '@/api/util'
import { UnimplementedError } from '@/common/errors/baseError'
import { Y2B_RAPID_API_KEY, YOUTUBE_USER_VIDEO_COUNT } from '@/config/env'
import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import { RapidApiStats } from '@/utils/rapidApiStats'
import { parseSubscriberCount } from '@/utils/youtube'
import { KolPlatform } from '@repo/database'
import axios from 'axios'
import axiosRetry from 'axios-retry'
import countries from 'i18n-iso-countries'
import { VideoSearchOptions, YoutubeInterface } from './youtube'

// 配置 axios-retry
const axiosInstance = axios.create()
axiosRetry(axiosInstance, {
  retries: 3,
  retryDelay: (_retryCount) => {
    return 1_000 // 重试延迟1s
  },
})

class YoutubeRapidApiV2 implements YoutubeInterface {
  private static instance: YoutubeRapidApiV2

  private HOST = 'youtube-v2.p.rapidapi.com'
  private API_KEY = Y2B_RAPID_API_KEY

  public static getInstance(): YoutubeRapidApiV2 {
    if (!YoutubeRapidApiV2.instance) {
      YoutubeRapidApiV2.instance = new YoutubeRapidApiV2()
    }
    return YoutubeRapidApiV2.instance
  }

  public async getChannel(channelId: string, collector?: RapidApiStatsCollector): Promise<Channel> {
    if (!channelId.startsWith('UC')) {
      const channelIdAndHandle = await this.getChannelIdAndHandle(channelId)
      channelId = channelIdAndHandle.channelId
    }
    const c: any = await this.get('channel/details?channel_id=' + channelId, collector)
    let country = c.country
    if (c.country && c.country.length > 2) {
      country = countries.getAlpha2Code(c.country, 'en') ?? ''
    }
    const email = extractEmail(c.description)
    const emailSource = email ? EmailSourceType.BIO_EMAIL : undefined
    return {
      channelId: channelId,
      title: c.title,
      description: c.description,
      channelHandle: '', // FIXME: current rapid api don't have handle
      banner: c.banner,
      tvBanner: c.banner,
      mobileBanner: c.banner,
      avatar: c.avatar,
      subscriberCountText: c.subscriber_count,
      subscriberCount: parseSubscriberCount(c.subscriber_count),
      videosCountText: '0',
      videosCount: 0,
      isVerified: c.verified,
      keywords: [],
      isFamilySafe: false,
      availableCountries: [],
      joinedDate: c.creation_date,
      viewCount: c.view_count,
      links: c.links?.map((link: { name: string; endpoint: string }) => ({
        title: link.name,
        link: link.endpoint,
      })) as Link[],
      tabs: [],
      videos: [],
      country: country,
      hasEmail: c.has_business_email,
      email: email,
      emailSource: emailSource,
    } as Channel
  }

  public async getChannelVideos(
    channelId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelVideo[]> {
    if (!channelId.startsWith('UC')) {
      const channelIdAndTitle = await this.getChannelIdAndHandle(channelId, collector)
      channelId = channelIdAndTitle.channelId
    }
    const res = await this.get<{ videos: any[] }>(`channel/videos?channel_id=${channelId}`)
    return res.videos
      .slice(0, YOUTUBE_USER_VIDEO_COUNT)
      .map((v) => this.parseVideo(v) as ChannelVideo)
  }

  public async getVideoRelatedVideos(
    videoId: string,
    count: number,
    collector?: RapidApiStatsCollector,
  ): Promise<RelatedVideo[]> {
    let token = undefined
    const videos: RelatedVideo[] = []
    let times = 1
    let error: any = undefined

    type VideoResponse = {
      number_of_videos: number
      query: string
      country: string
      lang: string
      timezone: string
      continuation_token: string
      videos: ChannelVideo[]
    }

    while (videos.length < count && !error) {
      try {
        const data: VideoResponse = await this.get<VideoResponse>(
          `video/recommendations${token ? '/continuation' : ''}?${parsePathParams({ video_id: videoId, continuation_token: token })}`,
          collector,
        )
        videos.push(...data.videos.map((v: any) => this.parseVideo(v) as RelatedVideo))
        times += 1
        token = data.continuation_token
      } catch (err) {
        error = err
      }
    }
    console.log(`get ${videos.length} videos for ${times} request for video ${videoId}`)
    if (error) {
      console.error(`end with error: `)
      console.error(error?.message ?? JSON.stringify(error))
    }
    return videos
  }

  public async getChannelId(
    channelIdOrName: string,
    collector?: RapidApiStatsCollector,
  ): Promise<string> {
    if (channelIdOrName.startsWith('UC')) {
      return channelIdOrName
    }
    const resp = await this.getChannelIdAndHandle(channelIdOrName, collector)
    return resp.channelId
  }

  private async getChannelIdAndHandle(
    channelHandle: string,
    collector?: RapidApiStatsCollector,
  ): Promise<{ channelId: string; channelName: string }> {
    const res = await this.get<{ channel_id: string; channel_name: string }>(
      `channel/id?channel_name=${encodeURIComponent(channelHandle)}`,
      collector,
    )

    return {
      channelId: res.channel_id,
      channelName: (res.channel_name.startsWith('@') ? '' : '@') + res.channel_name,
    }
  }

  public async searchVideo(
    keyword: string,
    options: VideoSearchOptions,
    collector?: RapidApiStatsCollector,
  ): Promise<SearchVideoResult> {
    throw new UnimplementedError('searchVideo is not implemented in youtubeRapidApiV2')
    // console.log(`search videos for keyword ${keyword} with options: ${JSON.stringify(options)}`)
    // if (!options?.regions?.length) {
    //   options.regions = [undefined]
    // }
    // const videos = (
    //   await Bluebird.map(options.regions, async (region) => {
    //     try {
    //       if (options?.pages && options?.pages > 1) {
    //         return await this.searchVideosForPages(keyword, options.pages, region, collector)
    //       } else {
    //         return (
    //           await this.get<any>(
    //             `search/?query=${encodeURIComponent(keyword)}${region ? `&country=${region.toUpperCase()}` : ''} `,
    //             collector,
    //           )
    //         ).videos
    //       }
    //     } catch (err) {
    //       Sentry.captureException(err)
    //       console.log(`failed to search video for keyword ${keyword} in region ${region}: ${err}`)
    //       return []
    //     }
    //   })
    // ).flat()
    // return videos
    //   .filter((video) => video.type === 'NORMAL')
    //   .map((video) => {
    //     return {
    //       channelTitle: video.author,
    //       channelId: video.channel_id,
    //       authorThumbnail: [],
    //       channelHandle: '',
    //       type: video.type,
    //       videoId: video.video_id,
    //       title: video.title,
    //       lengthText: video.video_length,
    //       viewCount: video.number_of_views,
    //       publishedTimeText: video.published_time,
    //       thumbnail: video.thumbnails,
    //     }
    //   })
  }

  public async getVideo(
    videoId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<VideoDetail | undefined> {
    try {
      const resp = await this.get<any>(`video/details?video_id=${videoId}`, collector)
      return {
        type: 'video',
        videoId: resp.video_id,
        title: resp.title,
        description: resp.description,
        lengthText: resp.video_length,
        viewCount: resp.number_of_views,
        publishedDate: resp.published_time,
        publishedTimeText: '',
        thumbnail: resp.thumbnails as Thumbnail[],
        channelId: resp.channel_id,
        commentCount: '0',
        likeCount: '0',
      } as VideoDetail
    } catch (err) {
      console.log(`[youtube rapidapi V2]failed to get video ${videoId}: ${err}`)
      Sentry.captureException(err)
      return undefined
    }
  }

  public async getVideoComments(
    videoId: string,
    options?: {
      token?: string
      sort_by?: 'newest' | 'top'
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<CommentResponse> {
    try {
      const queryParams: string[] = [`video_id=${videoId}`]

      if (options) {
        if (options.token) queryParams.push(`continuation_token=${options.token}`)
      }

      const queryString = queryParams.join('&')
      const resp = await this.get<{
        video_id: string
        total_number_of_comments: number
        total_number_of_likes: number
        number_of_comments: number
        continuation_token?: string
        comments: Array<{
          id: string
          author_name: string
          author_channel_id: string
          like_count: string
          published_time: string
          text: string
          number_of_replies: string
          thumbnails: Thumbnail[]
        }>
      }>(`video/comments?${queryString}`, collector)

      return {
        commentsCount: String(resp.total_number_of_comments || resp.number_of_comments || 0),
        continuation: resp.continuation_token || '',
        data: (resp.comments || []).map((comment) => ({
          commentId: comment.id,
          authorText: comment.author_name,
          authorChannelId: comment.author_channel_id,
          authorThumbnail: comment.thumbnails || [],
          textDisplay: comment.text,
          publishedTimeText: comment.published_time,
          publishDate: '',
          publishedAt: '',
          likesCount: comment.like_count,
          replyCount: comment.number_of_replies,
          authorIsChannelOwner: false,
          isVerified: false,
          isArtist: false,
          isCreator: false,
        })),
        msg: '',
      }
    } catch (error) {
      console.error(`[ytb] 获取视频 ${videoId} 评论失败:`, error)
      Sentry.captureException(error)
      return {
        commentsCount: '0',
        continuation: '',
        data: [],
        msg: error instanceof Error ? error.message : String(error),
      }
    }
  }

  public async getChannelShorts(
    idOrName: {
      channelId?: string
      forUsername?: string
    },
    options?: {
      sort_by?: string
      token?: string
      geo?: string
      lang?: string
      local?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<ChannelShortsResponse> {
    const channelId =
      idOrName.channelId ||
      (idOrName.forUsername ? await this.getChannelId(idOrName.forUsername, collector) : '')
    if (!channelId) {
      return {
        continuation: '',
        data: [],
        msg: 'Channel ID not found',
      }
    }
    const resp = await this.get<any>(`channel/shorts?channel_id=${channelId}`, collector)
    return {
      continuation: resp.continuation || '',
      data: resp.videos || [],
      msg: resp.msg || 'success',
    }
  }

  private async searchVideosForPages(
    keyword: string,
    pages: number,
    region: string | undefined,
    collector?: RapidApiStatsCollector,
  ): Promise<RelatedVideo[]> {
    const query =
      `query=${encodeURIComponent(keyword)}` + (region ? `&country=${region!.toUpperCase()}` : '')
    let resp = await this.get<any>(`search/?${query}`, collector)
    const videos = resp.videos
    let token = resp.continuation_token
    let times = 1
    while (videos.length < 20 * pages && times < pages * 2) {
      try {
        resp = await this.get<any>(
          `search/continuation?continuation_token=${token}&${query}`,
          collector,
        )
        videos.push(...resp.videos)
        token = resp.continuation_token
      } catch (err) {
        console.log(
          `error when search videos for ${keyword} in ${region} at ${times} times: ${err}`,
        )
        Sentry.captureException(err)
      } finally {
        times += 1
      }
    }
    return videos
  }

  public async getHashtag(
    hashtag: string,
    options?: {
      token?: string
      geo?: string
      lang?: string
    },
    collector?: RapidApiStatsCollector,
  ): Promise<YoutubeHashTagBreakRes> {
    throw new UnimplementedError('getHashtag is not implemented in youtubeRapidApiV2')
  }

  private async get<T>(uri: string, collector?: RapidApiStatsCollector) {
    const url = `https://${this.HOST}/${uri}`
    const endpoint = url.split('?')[0]
    const options = {
      method: 'GET',
      headers: {
        'x-rapidapi-key': this.API_KEY,
        'x-rapidapi-host': this.HOST,
      },
    }
    let response: any
    let statusCode = 0

    try {
      if (collector) {
        response = await collector.collect(axiosInstance.get(url, options), uri.split('?')[0])
      } else {
        response = await axiosInstance.get(url, options)
      }

      statusCode = response.status

      // 处理HTTP错误
      if (response.status != 200) {
        throw new Error(response.statusText)
      }

      // 处理API错误
      if (response.data && response.data.detail) {
        console.error(response.data.detail)
        throw new Error(response.data.detail)
      }

      // 记录成功请求
      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.YOUTUBE, RapidApiStatus.SUCCESS, endpoint, statusCode)
        .catch((err: any) => {
          console.error('Failed to record API stats:', err)
          Sentry.captureException(err)
        })
      return response.data as T
    } catch (err) {
      // 记录错误请求 - 统一错误处理
      console.error(`YouTube API V2错误 (${statusCode}): ${err}`)

      RapidApiStats.getInstance()
        .recordApiCall(KolPlatform.YOUTUBE, RapidApiStatus.ERROR, endpoint, statusCode)
        .catch((recordErr: any) => {
          console.error('Failed to record API error stats:', recordErr)
          Sentry.captureException(recordErr)
        })
      throw err
    }
  }

  private parseVideo(video: any): ChannelVideo | RelatedVideo {
    return {
      type: 'video',
      videoId: video.video_id,
      title: video.title,
      lengthText: video.video_length,
      viewCount: video.number_of_views,
      publishedTimeText: video.published_time,
      publishedAt: video.published_time || video.publishedAt || '',
      thumbnail: video.thumbnails,
      description: '',
      richThumbnail: video.thumbnails,
      channelTitle: video.author,
      channelId: video.channle_id,
    }
  }
}

export default YoutubeRapidApiV2
