import { Provider, ProviderCredential } from '@repo/database'
import _ from 'lodash'

export type PopulatedProvider = {
  key: string
  type: string
  clientId: string
  createdAt: Date
  updatedAt: Date
}

export const populateProvider = (provider: Provider) => {
  return _.pick(provider, ['key', 'type', 'clientId', 'createdAt', 'updatedAt'])
}

export type PopulatedProviderCrential = {
  id: string
  expiresAt: Date
  createdBy: string
  createdAt: Date
  updatedAt: Date
  Provider?: PopulatedProvider
}

export const populateProviderCrential = (
  crential: ProviderCredential & {
    Provider?: Provider
  },
): PopulatedProviderCrential & {
  Provider?: PopulatedProvider
} => {
  const pickedCrential = _.pick(crential, [
    'id',
    'expiresAt',
    'createdBy',
    'createdAt',
    'updatedAt',
    'Provider',
  ]) as PopulatedProviderCrential

  // 如果Provider存在，则调用populateProvider处理
  if (pickedCrential.Provider) {
    pickedCrential.Provider = populateProvider(pickedCrential.Provider as any)
  }

  return pickedCrential
}
