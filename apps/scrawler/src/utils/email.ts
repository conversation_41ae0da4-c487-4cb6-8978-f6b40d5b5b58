import { EmailContext } from '@/types/emailSend'
import Nunjucks from 'nunjucks'
import { VM } from 'vm2'

export function extractEmail(text: string | undefined | null): string | null {
  if (!text) {
    return null
  }
  // 首先匹配可能的邮箱模式
  const matches = text.match(
    /[a-zA-Z0-9][a-zA-Z0-9._%+-]*@([a-zA-Z0-9][a-zA-Z0-9-]*\.){1,}[a-zA-Z]+/,
  )
  if (!matches) return null

  // 获取匹配到的邮箱
  let email = matches[0].trim()
  while (email.startsWith('.') || email.startsWith('-')) {
    email = email.substring(1)
  }
  while (email.endsWith('.') || email.endsWith('-')) {
    email = email.substring(0, email.length - 1)
  }

  return email.trim()
}

export const extractFullEmail = (signature: string | null): string | null => {
  if (!signature) {
    return null
  }

  const emailRegex =
    /^[a-zA-Z0-9\p{L}!#$%&'*+\-/=?^_`{|}~.]+@(?=.{1,254}$)(?:(?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+[A-Za-z]{2,}$/u
  const matches = signature.match(emailRegex)
  return matches ? matches[0] : null
}

export const extractUsernameFromEmail = (email: string) => {
  return email.split('@')[0]
}

/**
 * 在安全的沙箱中对纯文本进行 Nunjucks 变量替换。
 * @param {string} textWithVariables - 用户输入的包含变量的原始文本。
 * @param {object} context - 包含魔法变量和上下文变量的数据。
 * @param {object} [customFilters={}] - (可选) 提供给用户的安全函数/过滤器。
 * @returns {Promise<string>} - 替换变量后的纯文本。
 */
export async function renderTextTemplate(
  textWithVariables: string,
  context: EmailContext,
  customFilters: any = {},
) {
  // 1. 创建 vm2 沙箱实例
  const vm = new VM({
    timeout: 500, // 对于纯文本替换，超时可以设置得更短
    sandbox: {
      Nunjucks,
      templateData: context,
      templateString: textWithVariables,
      customFilters, // 注入自定义过滤器
    },
  })

  try {
    // 2. 在沙箱内部执行渲染
    // Nunjucks 的 autoescape 在这里依然重要，它可以防止变量内容本身包含的
    // 特殊字符（虽然不渲染HTML，但保持数据完整性是好的实践）
    const renderedText = vm.run(
      `
      // 创建一个关闭了自动转义的环境，因为我们处理的是纯文本
      // 如果你希望转义 '\\<', '\\>' 等字符，可以设为 true
      const env = new Nunjucks.Environment(null, { autoescape: false });
      
      // 使用 renderString 进行变量替换
      env.renderString(templateString, templateData);
    `,
    )

    return renderedText
  } catch (error: any) {
    console.error('Secure text template rendering failed:', error.message)
    // 根据错误类型向用户提供反馈
    if (error.name === 'Template render error' || error.name === 'Template-parse-error') {
      throw new Error(`模板变量或语法错误: ${error.message}`)
    }
    throw new Error('变量替换执行失败或超时。')
  }
}
