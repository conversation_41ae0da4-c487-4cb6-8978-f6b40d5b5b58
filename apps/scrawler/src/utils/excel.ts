import ExcelJS from 'exceljs'

// 基于ExcelJS的ValueType枚举值定义数据类型
export type CellDataType = 'string' | 'number' | 'date' | 'boolean'

export interface SheetData {
  name: string // sheet名称
  data: Record<string, any>[] // 数据数组
  columns?: {
    header: string // 表头
    key: string // 对应的数据key
    width?: number // 列宽
    hyperlink?: string // 添加超链接字段配置
    dataType?: CellDataType // 数据类型
    format?: string // 数据格式化，如日期格式、数字格式等
    cellStyle?: (value: any) => Partial<ExcelJS.Style> // 基于单元格值的条件样式
  }[]
  styles?: {
    headerStyle?: Partial<ExcelJS.Style> // 表头样式
    rowStyle?: Partial<ExcelJS.Style> // 数据行样式
  }
}

// 传入几个数组导出几个sheet
export async function exportToExcel(sheets: SheetData[]): Promise<Buffer> {
  const workbook = new ExcelJS.Workbook()
  // 默认样式
  const defaultHeaderStyle: Partial<ExcelJS.Style> = {
    font: { bold: true, size: 12 },
    alignment: { vertical: 'middle', horizontal: 'center' },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    },
  }

  const defaultRowStyle: Partial<ExcelJS.Style> = {
    alignment: { vertical: 'middle', horizontal: 'left' },
  }

  const highlightFill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'ffbd00' },
  } as ExcelJS.FillPattern

  for (const sheetData of sheets) {
    const worksheet = workbook.addWorksheet(sheetData.name)

    // 如果没有提供columns配置，自动从数据生成
    if (!sheetData.columns && sheetData.data.length > 0) {
      sheetData.columns = Object.keys(sheetData.data[0]).map((key) => ({
        header: key,
        key: key,
        width: 15,
      }))
    }

    // 列默认宽度
    if (sheetData.columns) {
      worksheet.columns = sheetData.columns.map((col) => ({
        ...col,
        width: col.width || 20,
      }))
    }

    // 支持超链接
    if (sheetData.columns) {
      const hyperlinkColumns = sheetData.columns
        .filter((col) => col.hyperlink)
        .map((col) => ({
          key: col.key,
          linkKey: col.hyperlink as string,
        }))

      // 添加数据并处理超链接和数据类型
      sheetData.data.forEach((row, rowIndex) => {
        const currentRow = worksheet.addRow(row)

        // 处理 superlike 高亮
        const length = sheetData.columns?.length
        if (row?.isSuperlike && length) {
          currentRow.eachCell((cell, num) => {
            cell.fill = highlightFill
          })
        }
        // 处理每个带超链接的列
        hyperlinkColumns.forEach(({ key, linkKey }) => {
          const columnNumber = worksheet.getColumn(key)?.number
          if (columnNumber) {
            const cell = currentRow.getCell(columnNumber)
            const linkValue = row[linkKey]
            const textValue = row[key]

            if (linkValue) {
              cell.value = {
                text: textValue || '',
                hyperlink: linkValue,
                tooltip: linkValue,
              }
              cell.font = {
                ...cell.font,
                color: { argb: 'FF0000FF' }, // 蓝色
                underline: true,
              }
            }
          }
        })

        // 处理数据类型和条件样式
        sheetData.columns?.forEach((column) => {
          const columnNumber = worksheet.getColumn(column.key)?.number
          if (columnNumber) {
            const cell = currentRow.getCell(columnNumber)
            const value = row[column.key]

            // 处理条件样式
            if (column.cellStyle && value !== null && value !== undefined) {
              const style = column.cellStyle(value)
              Object.assign(cell, style)
            }

            // 处理数据类型
            if (column.dataType || column.format) {
              // 跳过空值或超链接列
              if (
                value === null ||
                value === undefined ||
                hyperlinkColumns.some((col) => col.key === column.key)
              ) {
                return
              }

              // 根据指定的数据类型处理值
              switch (column.dataType) {
                case 'number':
                  // 确保数值类型正确
                  cell.value = typeof value === 'number' ? value : Number(value) || 0
                  cell.numFmt = column.format || '0' // 默认整数格式
                  break
                case 'date':
                  // 处理日期类型
                  if (value instanceof Date) {
                    cell.value = value
                  } else if (typeof value === 'string' || typeof value === 'number') {
                    const dateValue = new Date(value)
                    if (!isNaN(dateValue.getTime())) {
                      cell.value = dateValue
                    }
                  }
                  cell.numFmt = column.format || 'yyyy-mm-dd' // 默认日期格式
                  break
                case 'boolean':
                  // 处理布尔类型
                  cell.value = Boolean(value)
                  break
                case 'string':
                default:
                  // 字符串类型，保持原样
                  cell.value = String(value)
                  break
              }

              // 如果只指定了格式但没有指定类型
              if (!column.dataType && column.format) {
                cell.numFmt = column.format
              }
            }
          }
        })
      })
    } else {
      worksheet.addRows(sheetData.data)
    }

    // 样式
    const headerStyle = {
      ...defaultHeaderStyle,
      ...sheetData.styles?.headerStyle,
    }
    const rowStyle = {
      ...defaultRowStyle,
      ...sheetData.styles?.rowStyle,
    }

    // 表头
    worksheet.getRow(1).eachCell((cell: ExcelJS.Cell) => {
      Object.assign(cell, headerStyle)
    })

    // 数据行
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      worksheet.getRow(rowNumber).eachCell((cell: ExcelJS.Cell) => {
        Object.assign(cell, rowStyle)
      })
    }

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      if (column.number) {
        const maxLength = worksheet
          .getColumn(column.number)
          .values.filter((value: any) => value !== null)
          .reduce((max: number, value: any) => Math.max(max, String(value).length), 0)
        column.width = Math.min(Math.max(maxLength + 2, 10), 50)
      }
    })

    // 冻结第一行（表头行）
    worksheet.views = [
      {
        state: 'frozen',
        ySplit: 1, // 冻结第一行
      },
    ]
  }

  return workbook.xlsx.writeBuffer() as Promise<Buffer>
}
