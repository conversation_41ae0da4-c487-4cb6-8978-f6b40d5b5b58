import { KolPlatform, ProjectKolAttitude } from '@repo/database'
import { Heap } from 'heap-js'

// 用户来源类型
export type UserSource = 'seed' | 'ai_discovered' | 'user_manual' | 'algorithm'

// 用户类型
export type UserType = 'perfect' | 'content-ok'

export type QueueUser = InstagramPriorityUser | YoutubePriorityUser

// 新的优先级策略
const PRIORITY_STRATEGY = {
  // 基础态度优先级
  [ProjectKolAttitude.NORATE]: 0,
  [ProjectKolAttitude.DISLIKE]: 1,
  [ProjectKolAttitude.LIKE]: 10, // 基础值，会根据用户类型调整
  [ProjectKolAttitude.SUPERLIKE]: 100,

  USER_TYPE_MULTIPLIER: {
    perfect: 5, // like perfect: 10 * 5 = 50
    'content-ok': 1, // like content-ok: 10 * 1 = 10
  },

  SEED_PRIORITY: 200,
}

// 计算用户优先级
export function calculateUserPriority(
  attitude: ProjectKolAttitude,
  source: UserSource,
  userType?: UserType,
): number {
  if (source === 'seed') {
    return PRIORITY_STRATEGY.SEED_PRIORITY
  }

  let basePriority = PRIORITY_STRATEGY[attitude]

  if (attitude === ProjectKolAttitude.LIKE && userType) {
    basePriority *= PRIORITY_STRATEGY.USER_TYPE_MULTIPLIER[userType]
  }

  return basePriority
}

// 简洁的优先级用户接口
interface BasePriorityUser {
  username: string // 平台用户名/ID
  attitude: ProjectKolAttitude // 态度评分
  priority: number // 数值优先级
  addedAt: number // 添加时间
  source: UserSource // 用户来源
  reason?: string // AI 给出的原因（可选）

  // 可选的扩展数据（按需添加）
  metadata?: Record<string, any> // 完全自由的元数据
}

// Instagram 特定用户接口（暂时与基类相同，按需扩展）
interface InstagramPriorityUser extends BasePriorityUser {
  userType: UserType
}

// Instagram 特定用户接口（暂时与基类相同，按需扩展）
interface YoutubePriorityUser extends BasePriorityUser {
  videoIds: string[]
  userType: UserType
}

// 抽象基类
abstract class BasePriorityQueue<T extends BasePriorityUser> {
  protected heap: Heap<T>
  protected userMap: Map<string, T> = new Map()
  constructor() {
    this.heap = new Heap<T>((a, b) => {
      // 优先级高的在前
      if (a.priority !== b.priority) {
        return b.priority - a.priority
      }
      // 相同优先级，时间早的在前
      return a.addedAt - b.addedAt
    })
  }

  // 通用方法 - 直接接受用户对象
  enqueue(user: T): void {
    const userKey = this.getUserKey(user.username)

    if (this.userMap.has(userKey)) {
      const existing = this.userMap.get(userKey)!
      if (user.priority > existing.priority) {
        this.updateUser(user)
      }
      return
    }

    this.heap.push(user)
    this.userMap.set(userKey, user)
  }

  dequeue(): T | null {
    const user = this.heap.pop()
    if (user) {
      this.userMap.delete(this.getUserKey(user.username))
    }
    return user || null
  }

  dequeueBatch(count: number): T[] {
    const result: T[] = []
    for (let i = 0; i < count && this.size() > 0; i++) {
      const user = this.dequeue()
      if (user) result.push(user)
    }
    return result
  }

  contains(username: string): boolean {
    return this.userMap.has(this.getUserKey(username))
  }

  getUser(username: string): T | null {
    return this.userMap.get(this.getUserKey(username)) || null
  }

  size(): number {
    return this.heap.size()
  }

  isEmpty(): boolean {
    return this.heap.isEmpty()
  }

  clear(): void {
    this.heap.clear()
    this.userMap.clear()
  }

  // 抽象方法 - 子类实现
  protected abstract validatePlatformData(data: any): boolean

  // 通用辅助方法
  protected getUserKey(username: string): string {
    return username // 简化为直接使用用户名作为 key
  }

  // 更新用户 - 接受完整的用户对象
  updateUser(updatedUser: T): boolean {
    const userKey = this.getUserKey(updatedUser.username)
    if (!this.userMap.has(userKey)) return false

    // 重建堆
    const allUsers = this.heap.toArray()
    this.heap.clear()
    this.userMap.clear()

    allUsers.forEach((user) => {
      if (this.getUserKey(user.username) === userKey) {
        // 使用新的用户对象替换旧的
        this.heap.push(updatedUser)
        this.userMap.set(userKey, updatedUser)
      } else {
        this.heap.push(user)
        this.userMap.set(this.getUserKey(user.username), user)
      }
    })

    return true
  }

  // 序列化
  toJSON(): any {
    return {
      users: this.heap.toArray(),
      timestamp: Date.now(),
    }
  }

  // 统计信息
  getStats(): {
    total: number
    byAttitude: Record<string, number>
    bySource: Record<string, number>
    topUsers: T[]
  } {
    const users = this.heap.toArray()
    const byAttitude: Record<string, number> = {}
    const bySource: Record<string, number> = {}

    users.forEach((user) => {
      byAttitude[user.attitude] = (byAttitude[user.attitude] || 0) + 1
      bySource[user.source] = (bySource[user.source] || 0) + 1
    })

    return {
      total: users.length,
      byAttitude,
      bySource,
      topUsers: users.slice(0, 10),
    }
  }
}

// Instagram 优先级队列实现
class InstagramPriorityQueue extends BasePriorityQueue<InstagramPriorityUser> {
  constructor() {
    super()
  }

  protected validatePlatformData(data: any): boolean {
    // Instagram 特定验证逻辑
    return typeof data.username === 'string' && data.username.length > 0
  }

  // Instagram 特定方法 - 接受 InstagramPriorityUser 对象
  enqueueWithInstagramData(user: InstagramPriorityUser): void {
    this.enqueue(user)
  }

  static fromJSON(data: any): InstagramPriorityQueue {
    const queue = new InstagramPriorityQueue()
    if (data && data.users && Array.isArray(data.users)) {
      data.users.forEach((user: InstagramPriorityUser) => {
        queue.enqueue(user)
      })
    }
    return queue
  }
}

// YouTube 优先级队列实现
class YoutubePriorityQueue extends BasePriorityQueue<YoutubePriorityUser> {
  constructor() {
    super()
  }

  protected validatePlatformData(data: any): boolean {
    // YouTube 特定验证逻辑
    return (
      typeof data.username === 'string' && data.username.length > 0 && Array.isArray(data.videoIds)
    )
  }

  // YouTube 特定方法 - 接受 YoutubePriorityUser 对象
  enqueueWithYoutubeData(user: YoutubePriorityUser): void {
    this.enqueue(user)
  }

  static fromJSON(data: any): YoutubePriorityQueue {
    const queue = new YoutubePriorityQueue()
    if (data && data.users && Array.isArray(data.users)) {
      data.users.forEach((user: YoutubePriorityUser) => {
        queue.enqueue(user)
      })
    }
    return queue
  }
}

// 注册机制的工厂模式
type QueueConstructor<T extends BasePriorityUser> = new () => BasePriorityQueue<T>
type QueueFromJSON<T extends BasePriorityUser> = (data: any) => BasePriorityQueue<T>

class PriorityQueueFactory {
  private static registry = new Map<
    KolPlatform,
    {
      constructor: QueueConstructor<any>
      fromJSON: QueueFromJSON<any>
    }
  >()

  // 注册平台队列
  static register<T extends BasePriorityUser>(
    platform: KolPlatform,
    constructor: QueueConstructor<T>,
    fromJSON: QueueFromJSON<T>,
  ): void {
    this.registry.set(platform, { constructor, fromJSON })
  }

  // 创建队列
  static createQueue(platform: KolPlatform): BasePriorityQueue<any> {
    const entry = this.registry.get(platform)
    if (!entry) {
      throw new Error(
        `Platform ${platform} is not registered. Available platforms: ${Array.from(this.registry.keys()).join(', ')}`,
      )
    }
    return new entry.constructor()
  }

  // 从 JSON 恢复队列
  static fromJSON(data: any, platform?: KolPlatform): BasePriorityQueue<any> {
    if (!data) {
      throw new Error('Invalid queue data')
    }

    // 优先使用数据中的平台信息，其次使用传入的平台参数
    const targetPlatform = data.platform || platform
    if (!targetPlatform) {
      throw new Error(
        'Platform information is required. Either provide platform parameter or include platform in data.',
      )
    }

    const entry = this.registry.get(targetPlatform)
    if (!entry) {
      throw new Error(
        `Platform ${targetPlatform} is not registered. Available platforms: ${Array.from(this.registry.keys()).join(', ')}`,
      )
    }

    return entry.fromJSON(data)
  }

  static getRegisteredPlatforms(): KolPlatform[] {
    return Array.from(this.registry.keys())
  }

  static isRegistered(platform: KolPlatform): boolean {
    return this.registry.has(platform)
  }
}

// 自动注册 Instagram 队列
PriorityQueueFactory.register(
  KolPlatform.INSTAGRAM,
  InstagramPriorityQueue,
  InstagramPriorityQueue.fromJSON.bind(InstagramPriorityQueue),
)

// 自动注册 YouTube 队列
PriorityQueueFactory.register(
  KolPlatform.YOUTUBE,
  YoutubePriorityQueue,
  YoutubePriorityQueue.fromJSON.bind(YoutubePriorityQueue),
)

export {
  BasePriorityQueue,
  InstagramPriorityQueue,
  PriorityQueueFactory,
  YoutubePriorityQueue,
  type BasePriorityUser,
  type InstagramPriorityUser,
  type YoutubePriorityUser,
}
