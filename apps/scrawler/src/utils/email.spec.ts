import { EmailContext } from '@/types/emailSend'
import { prisma } from '@repo/database'
import Blue<PERSON> from 'bluebird'
import * as fs from 'fs/promises'
import { describe, expect, it } from 'vitest'
import { extractEmail, renderTextTemplate } from './email'

describe('should test email ', () => {
  it('should return email', async () => {
    const raw = '<EMAIL>'
    console.log(extractEmail(raw))
  })

  it('should test all email cases', async () => {
    expect(extractEmail('---<EMAIL>---')).eq('<EMAIL>')
    expect(extractEmail('....<EMAIL>...')).eq('<EMAIL>')
    expect(extractEmail('Email:<EMAIL>')).eq('<EMAIL>')
    expect(extractEmail('<EMAIL>')).eq('<EMAIL>')
    expect(extractEmail('<EMAIL>')).eq(null)
    expect(extractEmail('<EMAIL>')).eq('<EMAIL>')
    expect(extractEmail('test@gmai_l.com')).eq(null)
    expect(extractEmail('test@gmail_.com')).eq(null)
    expect(extractEmail('test@gmail.com_')).eq('<EMAIL>')
    expect(extractEmail('💌<EMAIL>')).eq('<EMAIL>')
  })
  it(
    'should find out all email not match the full version of regex: ',
    async () => {
      const logfile = 'after.txt'
      const processEmail = function (email: string): string {
        email = email.trim()
        for (let i = 0; i < 5; i++) {
          email = email.replace('--', '-')
        }
        if (email.startsWith('-')) {
          email = email.substring(1)
        }
        return email
      }
      const batchSize = 5000
      let times = 0
      let users = await prisma.kolInfo.findMany({
        where: {
          email: {
            not: null,
            notIn: [''],
          },
          emailSource: {
            not: null,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: batchSize,
        skip: 0,
        select: {
          email: true,
          platformAccount: true,
          platform: true,
          emailSource: true,
        },
      })
      let failedCount = 0
      let successCount = 0
      console.log(`first batch: ${users.length}`)
      while (users.length > 0) {
        times += 1
        await Bluebird.map(
          users,
          async (user) => {
            const realEmail = extractEmail(user.email)
            if (user.email?.length && user.email?.length != realEmail?.length) {
              if (realEmail?.length) {
                const msg = `[success]update email from ${user.email} to ${realEmail}`
                console.log(msg)
                fs.appendFile(logfile, msg + '\n')
                await prisma.kolInfo.update({
                  where: {
                    platform_platformAccount: {
                      platform: user.platform,
                      platformAccount: user.platformAccount!,
                    },
                  },
                  data: {
                    email: realEmail,
                  },
                })
                successCount++
              } else {
                const msg = `${user.platform} account ${user.platformAccount} has invalid email: ${user.email}, valid is ${realEmail}, from ${user.emailSource}`
                console.log(msg)
                await fs.appendFile(logfile, msg + '\n')
                await prisma.kolInfo.update({
                  where: {
                    platform_platformAccount: {
                      platform: user.platform,
                      platformAccount: user.platformAccount!,
                    },
                  },
                  data: {
                    email: '',
                  },
                })
                failedCount++
              }
            } else {
              successCount++
            }
          },
          { concurrency: 200 },
        )
        users = await prisma.kolInfo.findMany({
          where: {
            email: {
              not: null,
              notIn: [''],
            },
            emailSource: {
              not: null,
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          take: batchSize,
          skip: batchSize * times,
          select: {
            email: true,
            platformAccount: true,
            platform: true,
            emailSource: true,
          },
        })
        console.log(`get ${times} batch size ${users.length}`)
      }
      console.log(`there are ${failedCount} emails will be cleared, ${successCount} remain.`)
    },
    60 * 60 * 1000,
  )
})

describe('Advanced Filter Testing', () => {
  it('should handle non-string input gracefully when passed to a filter', async () => {
    const template = 'Result: "{{ value }}"'
    // context 的 value 不是字符串
    const context: EmailContext = { nickname: '12345' }
    const result = await renderTextTemplate(template, context)
    expect(result).toBe('Result: ""')
  })

  it('should handle null and undefined input for filters', async () => {
    const template = 'Nickname: "{{ nickname }}"'
    const context: EmailContext = {} // nickname 是 undefined

    const result = await renderTextTemplate(template, context)
    expect(result).toBe('Nickname: ""')
  })

  it('should handle value for context', async () => {
    const template = 'Nickname: "{{ nickname }}"'
    const context: EmailContext = {
      nickname: '12345',
    } // nickname 是 undefined

    const result = await renderTextTemplate(template, context)
    expect(result).toBe('Nickname: "12345"')
  })
})
