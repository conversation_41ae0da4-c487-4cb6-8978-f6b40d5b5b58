import { CORS_ALLOWED_ORIGINS } from '@/config/env'
import { initStripeConfig } from '@/config/stripe/stripe'
import { swaggerOptions, swaggerUiOptions } from '@/config/swagger'
import fastifyCors from '@fastify/cors'
import fastifyHelmet from '@fastify/helmet'
import fastifySwagger from '@fastify/swagger'
import fastifySwaggerUi from '@fastify/swagger-ui'
import { prisma } from '@repo/database'
import Fastify from 'fastify'
import fastifyGracefulShutdown from 'fastify-graceful-shutdown'
import { AsyncLocalStorage } from 'node:async_hooks'
import { v4 as uuidv4 } from 'uuid'
import { successResponse } from './common/response/response'
import { validateR2Config } from './infras/cloudflareR2'
import { redis } from './infras/redis'
import Sentry from './infras/sentry'
import errorHandler from './middlewares/errorHandler'
import router from './routes/index'
import { loadWorldGeoJSON } from './services/insInfo.service'
import { initJinaV4Collection } from './services/jinaMilvus.service'
declare module 'fastify' {
  interface FastifyRequest {
    rawBody?: string
  }
}

// AsyncLocalStorage实例
export const als = new AsyncLocalStorage<Map<string, any>>()

const envToLogger = {
  development: {
    level: 'info',
    redact: {
      paths: ['req.headers.authorization', 'req.body.password', 'req.body.token'],
      censor: '[HIDDEN]',
    },
    serializers: {
      req(request: any) {
        return {
          method: request.method,
          url: request.url,
          query: request.query,
          params: request.params,
          body: request.body,
          headers: {
            'user-agent': request.headers['user-agent'],
            authorization: request.headers.authorization ? '(present)' : '(not present)',
            'content-type': request.headers['content-type'],
            referer: request.headers.referer,
          },
        }
      },
      res(reply: any) {
        return {
          statusCode: reply.statusCode,
          responseTime: reply.elapsedTime,
        }
      },
    },
    mixin() {
      const store = als.getStore()
      if (store) {
        return {
          requestId: store.get('requestId'),
        }
      }
      return {}
    },
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'yyyy-mm-dd HH:MM:ss.l',
        ignore: 'pid,hostname',
        messageFormat: '[{level}] {time} | {requestId} | {msg} | {reqInfo}',
      },
    },
  },
  production: {
    level: 'info',
    redact: {
      paths: ['req.headers.authorization', 'req.body.password', 'req.body.token'],
      censor: '[HIDDEN]',
    },
    serializers: {
      req(request: any) {
        return {
          method: request.method,
          url: request.url,
          query: request.query,
          params: request.params,
          body: request.body,
          headers: {
            'user-agent': request.headers['user-agent'],
            authorization: request.headers.authorization ? '(present)' : '(not present)',
            'content-type': request.headers['content-type'],
            referer: request.headers.referer,
          },
        }
      },
      res(reply: any) {
        return {
          statusCode: reply.statusCode,
          responseTime: reply.elapsedTime,
        }
      },
    },
    mixin() {
      const store = als.getStore()
      if (store) {
        return {
          requestId: store.get('requestId'),
        }
      }
      return {}
    },
  },
  test: false,
}

export const fastify = Fastify({
  logger: envToLogger[process.env.NODE_ENV as keyof typeof envToLogger] ?? true,
  bodyLimit: 20 * 1024 * 1024, // 20MB
  disableRequestLogging: true, // disable default logging
  genReqId: (_req) => {
    // generate request id
    const timestamp = new Date().getTime()
    return `${timestamp}-${uuidv4().substring(0, 8)}`
  },
  ajv: {
    customOptions: {
      removeAdditional: false,
      coerceTypes: true,
      useDefaults: true,
      allErrors: false,
      strict: false,
      validateSchema: false,
      validateFormats: false,
    },
  },
})

fastify.setSerializerCompiler((_options) => {
  return function (data) {
    return JSON.stringify(data, (_key, value) => {
      // 如果值的类型是 bigint，则将其转换为字符串
      if (typeof value === 'bigint') {
        return Number(value)
      }
      return value // 其他类型照常处理
    })
  }
})
// add onRequest hook to set ALS
fastify.addHook('onRequest', (request, _reply, done) => {
  als.run(new Map(), () => {
    const store = als.getStore()
    if (store) {
      store.set('requestId', request.id)
    }

    // add request start log
    request.log.info({
      msg: 'request received',
      method: request.method,
      url: request.url,
      requestId: request.id,
      reqId: request.id,
    })

    done()
  })
})
// add onResponse hook to log request and response info
fastify.addHook('onResponse', (request, reply, done) => {
  const reqInfo = `${request.method} ${request.url} → ${reply.statusCode} (${reply.elapsedTime.toFixed(2)}ms)`
  // 将请求和响应信息合并到一条日志中
  request.log.info({
    msg: 'request',
    req: request,
    res: reply,
    reqInfo: reqInfo,
    route: request.routeOptions?.url || request.url,
    statusCode: reply.statusCode,
    responseTime: reply.elapsedTime,
    params: request.params || {},
    query: request.query || {},
    error: reply.statusCode >= 400 ? reply.raw.statusMessage || 'Error' : undefined,
  })

  done()
})

fastify.removeContentTypeParser('application/json')
fastify.addContentTypeParser(
  'application/json',
  { parseAs: 'string' },
  (req, body: string, done) => {
    // 只处理webhook路由
    if (req.url === '/api/subscription/webhook') {
      req.rawBody = body
      try {
        const json = JSON.parse(body)
        done(null, json)
      } catch (err) {
        done(err as Error)
      }
    } else {
      // 其他路由使用默认解析
      try {
        const json = JSON.parse(body)
        done(null, json)
      } catch (err) {
        done(err as Error)
      }
    }
  },
)
fastify.addHook('onError', (request, reply, error, done) => {
  if (request.url.includes('/api/subscription/webhook')) {
    fastify.log.error('Stripe webhook处理出错:', error)
    Sentry.captureException(error)
    // 对于Stripe webhook请求，即使出错也返回200，以防止Stripe重试
    if (!reply.sent) {
      reply.code(200).send(successResponse({ received: true }))
    }
  }
  done()
})

// Register security headers - 注册安全头插件，防护常见Web攻击
fastify.register(fastifyHelmet, {
  // Content Security Policy - 内容安全策略，防止XSS攻击
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
})

// Register Swagger only in dev env
if (process.env.NODE_ENV !== 'production') {
  fastify.register(fastifySwagger, swaggerOptions)
  fastify.register(fastifySwaggerUi, swaggerUiOptions)
}

// Register the CORS plugin
fastify.register(fastifyCors, {
  origin: CORS_ALLOWED_ORIGINS,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'Accept',
    'Origin',
    'X-Requested-With',
    'easykolversion',
  ],
  credentials: true,
  exposedHeaders: ['set-cookie', 'Content-Range'],
  maxAge: 86400, // 预检请求缓存24小时
})

// register graceful shutdown
fastify.register(fastifyGracefulShutdown)

// register error handler
fastify.register(errorHandler)

// register router
fastify.register(router)

// Route to get a task by ID
fastify.get('/health', {
  logLevel: 'silent',
  handler: async (_request, reply) => {
    return reply.send({
      status: 'ok',
    })
  },
})

Sentry.setupFastifyErrorHandler(fastify)

fastify.after(() => {
  fastify.gracefulShutdown(async (signal: string) => {
    fastify.log.info('Received signal to shutdown: %s', signal)
    fastify.log.info('Starting graceful shutdown cleanup...')

    try {
      // 关闭Redis连接
      try {
        await redis.quit()
        fastify.log.info('Redis connection closed')
      } catch (err) {
        fastify.log.error('Error closing Redis connection:', err)
      }

      // 关闭Prisma连接
      try {
        await prisma.$disconnect()
        fastify.log.info('Prisma connection closed')
      } catch (err) {
        fastify.log.error('Error closing Prisma connection:', err)
      }

      fastify.log.info('Graceful shutdown cleanup completed')
    } catch (err) {
      fastify.log.error('Error during graceful shutdown:', err)
      throw err
    }
  })
})

// Start the server
export const server = async () => {
  try {
    // 验证 Cloudflare R2 配置
    validateR2Config()

    initStripeConfig()
    await initJinaV4Collection()
    loadWorldGeoJSON()
      .then(() => {
        console.log('预加载世界地图GeoJSON数据成功')
      })
      .catch((err) => {
        console.error('预加载世界地图GeoJSON数据失败', err)
        Sentry.captureException(err)
      })
    await fastify.listen({ port: 3000, host: '0.0.0.0' })
    console.log(`Server listening on http://0.0.0.0:3000`)

    // handle crons
    switch (process.env.CRONJOB) {
      case 'SYNC_RAPIDAPI_STATS':
        import('./crons/sync-rapidapi-stats')
        break
      default:
        break
    }

    if (process.env.WORKER === 'true') {
      console.log('worker start')
      import('./consumers/worker.consumer').catch(console.error)
    }

    // easykol auto update track
    if (process.env.TRACK === 'true') {
      console.log('strategy processor start')
      import('./track').then(({ processStrategies }) => {
        processStrategies().catch((err) => {
          console.error('strategy processor error: ', err)
          Sentry.captureException(err)
        })
      })
    }
  } catch (err) {
    console.error(err)
    Sentry.captureException(err)
    process.exit(1)
  }
}
