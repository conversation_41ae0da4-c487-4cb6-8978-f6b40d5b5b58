import { QuotaType } from '@repo/database'
export enum QuotaCost {
  SIMILAR_SEARCH = 10,
  AI_SEARCH = 10,
  TT_FOLLOWERS_SIMILAR = 10,
  TT_FOLLOWING_LIST = 10,
  TT_HASH_TAG_BREAK = 10,
  TT_SEARCH_KEYWORDS = 10,
  TT_SEARCH_INPUT_BREAK = 10,
  CARD_QUERY = 1,
  CARD_QUERY_PAID_AND_ENTERPRISE = 0,
  CARD_QUERY_CHECK = 1,
  FAILED_TASK = 10,
  FAILED_SEARCH = 10,
  TIKTOK_AUTHOR_COMMENTS_ANALYSIS = 5,
  EASYKOL_DATA_TRACK_URL = 1,
  EXCLUDE_LIST = 1,
  CARD_QUERY_HAS_SUBSCRIPTION = 0, // 信息卡包月用户的卡片消耗额度
  CARD_QUERY_HAS_VISITED_IN_LAST_WEEK = 0, // 信息卡包月用户的卡片消耗额度
  YOUTUBE_AUTHOR_COMMENTS_ANALYSIS = 10,
  INS_AUTHOR_COMMENTS_ANALYSIS = 10,
  AUDIENCE_ANALYSIS = 20,
  AUDIENCE_ANALYSIS_HAS_VISITED_IN_LAST_WEEK = 0,
  INS_HASH_TAG_BREAK = 10,
  INS_FOLLOWING_LIST = 10,
  TT_BGM_BREAK = 10,
  TT_WEB_LIST = 10, // 100 urls=>10quota
  INS_WEB_LIST = 10, // 100 urls=>10quota
  INS_TAGGED_BREAK = 10,
  YOUTUBE_HASHTAG_BREAK = 10,
  YOUTUBE_SEARCH_INPUT_BREAK = 10,
  INFOCARD_SUBSCRIPTION = 500,
  POST_AUDIENCE = 20,
  LONG_CRAWLER = 10, // 长时间爬取任务
  AUDIENCE_FAKE = 20, // 假受众分析
}

export const quotaCostMap: Partial<Record<QuotaType, number>> = {
  [QuotaType.SIMILAR_SEARCH]: QuotaCost.SIMILAR_SEARCH,
  [QuotaType.AI_SEARCH]: QuotaCost.AI_SEARCH,
  [QuotaType.CARD_QUERY]: QuotaCost.CARD_QUERY,
  [QuotaType.TIKTOK_AUTHOR_COMMENTS_ANALYSIS]: QuotaCost.TIKTOK_AUTHOR_COMMENTS_ANALYSIS,
  [QuotaType.YOUTUBE_AUTHOR_COMMENTS_ANALYSIS]: QuotaCost.YOUTUBE_AUTHOR_COMMENTS_ANALYSIS,
  [QuotaType.INS_AUTHOR_COMMENTS_ANALYSIS]: QuotaCost.INS_AUTHOR_COMMENTS_ANALYSIS,
  [QuotaType.AUDIENCE_ANALYSIS]: QuotaCost.AUDIENCE_ANALYSIS,
  [QuotaType.TT_FOLLOWERS_SIMILAR]: QuotaCost.TT_FOLLOWERS_SIMILAR,
  [QuotaType.TT_FOLLOWING_LIST]: QuotaCost.TT_FOLLOWING_LIST,
  [QuotaType.TT_HASH_TAG_BREAK]: QuotaCost.TT_HASH_TAG_BREAK,
  [QuotaType.TT_SEARCH_INPUT_BREAK]: QuotaCost.TT_SEARCH_INPUT_BREAK,
  [QuotaType.INS_HASH_TAG_BREAK]: QuotaCost.INS_HASH_TAG_BREAK,
  [QuotaType.INS_FOLLOWING_LIST]: QuotaCost.INS_FOLLOWING_LIST,
  [QuotaType.TT_BGM_BREAK]: QuotaCost.TT_BGM_BREAK,
  [QuotaType.INS_TAGGED_BREAK]: QuotaCost.INS_TAGGED_BREAK,
  [QuotaType.YOUTUBE_HASHTAG_BREAK]: QuotaCost.YOUTUBE_HASHTAG_BREAK,
  [QuotaType.YOUTUBE_SEARCH_INPUT_BREAK]: QuotaCost.YOUTUBE_SEARCH_INPUT_BREAK,
  [QuotaType.POST_AUDIENCE]: QuotaCost.POST_AUDIENCE,
  [QuotaType.LONG_CRAWLER]: QuotaCost.LONG_CRAWLER,
  [QuotaType.AUDIENCE_FAKE]: QuotaCost.AUDIENCE_FAKE,
}
