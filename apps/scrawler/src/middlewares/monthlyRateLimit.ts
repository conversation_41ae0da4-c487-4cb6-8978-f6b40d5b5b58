import { StatusCodes as ErrorCodes, throwError } from '@/common/errors/statusCodes'
import { EASYKOL_WARNING_CHANNEL, REDIS_KEY_PREFIX } from '@/config/env'
import { SlackClient } from '@/infras/monitoring/slackClient'
import { redis } from '@/infras/redis'
import { MemberStatus, QuotaType, prisma } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'

const MONTHLY_LIMIT = 15000
const ENDPOINT_KEY = 'info_platform'

const getMonthlyRateLimitKey = (userId: string, endpoint: string): string => {
  const now = new Date()
  const yearMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`
  return `${REDIS_KEY_PREFIX}monthly_rate_limit:${endpoint}:${yearMonth}:${userId}`
}

const getSecondsUntilEndOfMonth = (): number => {
  const now = new Date()
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1, 0, 0, 0, 0)
  return Math.floor((nextMonth.getTime() - now.getTime()) / 1000)
}

/**
 * 月度访问限制中间件
 * 限制用户每月访问特定接口的次数，超过限制后封禁账号
 */
interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string
    email?: string
  }
}

export const monthlyRateLimit = (limit: number = MONTHLY_LIMIT) => {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const { user } = request as AuthenticatedRequest

    if (!user?.id) {
      throwError(ErrorCodes.UNAUTHORIZED, 'unauthorized access')
    }

    const membership = await prisma.userMembership.findUniqueOrThrow({
      where: { userId: user.id },
      select: { status: true },
    })

    if (membership?.status === MemberStatus.SUSPENDED) {
      throwError(ErrorCodes.FORBIDDEN, 'your account is suspended, please contact admin to unlock')
    }

    const redisKey = getMonthlyRateLimitKey(user.id, ENDPOINT_KEY)

    const count = await redis.incr(redisKey)

    if (count === 1) {
      const expireSeconds = getSecondsUntilEndOfMonth()
      await redis.expire(redisKey, expireSeconds)
      console.log(
        `[Monthly Rate Limit] init user ${user.id} monthly rate limit, will expire at ${expireSeconds} seconds`,
      )
    }

    if (count % 1000 === 0 || count >= limit - 100) {
      console.log(
        `[Monthly Rate Limit] user ${user.id} has accessed ${ENDPOINT_KEY} ${count}/${limit} times this month`,
      )
    }

    if (count > limit) {
      console.warn(
        `[Monthly Rate Limit] user ${user.id} has exceeded the monthly limit ${count}/${limit}, suspending account`,
      )

      await prisma.$transaction(async (tx) => {
        const userMembership = await tx.userMembership.update({
          where: { userId: user.id },
          data: {
            status: MemberStatus.SUSPENDED,
            updatedAt: new Date(),
          },
        })

        await tx.quotaLog.create({
          data: {
            userId: user.id,
            membershipId: userMembership.id,
            usage: 0,
            type: QuotaType.CARD_QUERY,
            description: 'ACCOUNT_SUSPENDED_MONTHLY_RATE_LIMIT',
            metadata: {
              reason: 'monthly_rate_limit_exceeded',
              endpoint: ENDPOINT_KEY,
              accessCount: count,
              limit: limit,
              timestamp: new Date().toISOString(),
            },
            createdBy: user.id,
          },
        })

        return userMembership
      })

      // 发送Slack通知
      try {
        await SlackClient.getInstance().sendMessage(
          EASYKOL_WARNING_CHANNEL,
          `🚨 *账户封禁通知*\n` +
            `用户ID: ${user.id}\n` +
            `邮箱: ${user?.email || '未知'}\n` +
            `原因: 超过月度访问限制\n` +
            `访问次数: ${count}/${limit}\n` +
            `接口: ${ENDPOINT_KEY}\n` +
            `时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`,
          { notify_channel: true },
        )
      } catch (slackError) {
        console.error('[Monthly Rate Limit] Failed to send Slack notification:', slackError)
      }

      throwError(
        ErrorCodes.FORBIDDEN,
        `you have exceeded the monthly limit (${limit} times), your account has been suspended. please contact admin to unlock`,
        {
          code: 'RATE_LIMIT_EXCEEDED_ACCOUNT_SUSPENDED',
          limit,
          count,
        },
      )
    }

    reply.header('X-Monthly-Rate-Limit', limit.toString())
    reply.header('X-Monthly-Rate-Limit-Remaining', (limit - count).toString())
    reply.header(
      'X-Monthly-Rate-Limit-Reset',
      new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString(),
    )
  }
}

export const getMonthlyUsageStats = async (userId: string, endpoint: string = ENDPOINT_KEY) => {
  const redisKey = getMonthlyRateLimitKey(userId, endpoint)
  const count = await redis.get(redisKey)
  const ttl = await redis.ttl(redisKey)

  return {
    endpoint,
    count: count ? parseInt(count) : 0,
    limit: MONTHLY_LIMIT,
    remaining: MONTHLY_LIMIT - (count ? parseInt(count) : 0),
    resetAt:
      ttl > 0
        ? new Date(Date.now() + ttl * 1000)
        : new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1),
  }
}
