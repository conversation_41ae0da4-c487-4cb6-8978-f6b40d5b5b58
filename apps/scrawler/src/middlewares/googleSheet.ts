import { handleUnknownError } from '@/common/errorHandler'
import { serverErrorResponse } from '@/common/response/response'
import { initUserGoogleSheet, userGoogleSheetHasInited } from '@/services/userGoogleSheet.service'
import { FastifyReply, FastifyRequest } from 'fastify'

export const checkGoogleSheet = {
  preHandler: async (request: FastifyRequest, reply: FastifyReply) => {
    const user = (request as any).user
    if (!user) {
      return reply.status(401).send({ error: 'Unauthorized', errCode: 'NEED_AUTH' })
    }
    try {
      if (!(await userGoogleSheetHasInited(user))) {
        await initUserGoogleSheet(user)
      }
    } catch (error) {
      console.error('Failed to check Google Sheet:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  },
}
