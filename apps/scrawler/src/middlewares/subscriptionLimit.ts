import { errorResponse, StatusCodes } from '@/common/response/response'
import { MemberType, prisma } from '@repo/database'
import { FastifyReply, FastifyRequest } from 'fastify'

/**
 * 订阅限制中间件 - 防止企业用户订阅个人计划
 */
export async function subscriptionLimit(request: FastifyRequest, reply: FastifyReply) {
  const user = (request as any).user

  if (!user || !user.id) {
    return reply
      .status(401)
      .send(errorResponse(StatusCodes.UNAUTHORIZED, 'Unauthorized', 'no permission'))
  }

  try {
    const membership = await prisma.userMembership.findUnique({
      where: { userId: user.id },
    })

    // 如果用户是企业用户，则不允许订阅个人计划
    if (
      membership &&
      (membership.type === MemberType.ENTERPRISE ||
        membership.isEnterpriseAdmin ||
        membership.enterpriseId)
    ) {
      return reply
        .status(403)
        .send(
          errorResponse(
            StatusCodes.FORBIDDEN,
            'EnterpriseUserForbidden',
            'enterprise user is not allowed to subscribe to individual plan',
          ),
        )
    }

    return
  } catch (error) {
    console.error('check subscription limit failed:', error)
    return reply
      .status(500)
      .send(
        errorResponse(StatusCodes.SERVER_ERROR, 'ServerError', 'check subscription limit failed'),
      )
  }
}
