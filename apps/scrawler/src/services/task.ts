import TiktokApi from '@/api/tiktok.ts'
import YoutubeApi from '@/api/youtube'
import { StatusCodes as ErrorCodes, throwError } from '@/common/errors/statusCodes'
import { BATCH_FETCH_TASK_COUNT } from '@/config/env.ts'
import { QuotaCost } from '@/enums/QuotaCost'
import { BatchAudienceMode } from '@/enums/TaskMode'
import {
  IEmbeddingTask,
  easykolTrackQueue,
  instagramTaskQueue,
  longCrawlerTaskQueue,
  tiktokTaskQueue,
  twitterTaskQueue,
  youtubeTaskQueue,
} from '@/infras/worker/bullmq'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { BatchAudienceTaskResult, BatchAudienceTasksResponse } from '@/types/task'
import { InsLongCrawlerTaskParams } from '@/types/task.ts'
import { TaskParams, TaskParamsService } from '@/types/taskParams'
import { parseUrlUtils } from '@/utils/parseUrl'
import {
  KolPlatform,
  QuotaType,
  SimilarChannelTask,
  SimilarChannelTaskStatus,
  TaskReason,
  TaskType,
  prisma,
} from '@repo/database'
import assert from 'assert'
import Bluebird from 'bluebird'
import Bull from 'bull'
import dayjs from 'dayjs'
import { QueueJobStatus } from '../routes/schemas/task'
import { retryUtil } from '../utils/retry'
import { MembershipService } from './membership.service'
import { getUserGoogleSheet } from './userGoogleSheet.service'

type TaskWithoutCandidate = Omit<SimilarChannelTask, 'candidate'> & { candidate?: never }

class TaskService {
  private static instance: TaskService

  public static getInstance() {
    if (!TaskService.instance) {
      TaskService.instance = new TaskService()
    }
    return TaskService.instance
  }

  // 获取对应任务类型的队列
  private getQueue(taskType: string): any {
    console.log(`[TaskService] 获取任务队列: ${taskType}`)
    if (taskType === TaskType.LONG_CRAWLER) {
      return longCrawlerTaskQueue
    }
    // 预留其他队列的处理位置
    // TODO: 未来添加其他队列的支持
    console.warn(`[TaskService] 任务类型 ${taskType} 暂不支持队列操作，使用长任务队列`)
    return longCrawlerTaskQueue
  }

  /**
   * 根据队列名称获取对应的队列实例
   * @param queueName 队列名称
   * @returns 队列实例
   */
  public getQueueByName(queueName: string): Bull.Queue<IEmbeddingTask> {
    switch (queueName) {
      case 'youtube-task-queue':
        return youtubeTaskQueue
      case 'tiktok-task-queue':
        return tiktokTaskQueue
      case 'instagram-task-queue':
        return instagramTaskQueue
      case 'easykol-track-queue':
        return easykolTrackQueue
      case 'long-crawler-task-queue':
        return longCrawlerTaskQueue
      default:
        throw new Error(`未知队列名称: ${queueName}`)
    }
  }

  /**
   * 获取队列中指定状态的任务
   * @param queueName 队列名称
   * @param status 任务状态（active, completed, failed, delayed, waiting）
   * @returns 任务列表
   */
  public async getQueueTasksByStatus(queueName: string, status?: QueueJobStatus): Promise<any[]> {
    try {
      const queue = this.getQueueByName(queueName)

      let tasks: Bull.Job<any>[] = []

      // 根据状态获取任务
      switch (status) {
        case QueueJobStatus.ACTIVE:
          tasks = await queue.getActive()
          break
        case QueueJobStatus.COMPLETED:
          tasks = await queue.getCompleted()
          break
        case QueueJobStatus.FAILED:
          tasks = await queue.getFailed()
          break
        case QueueJobStatus.DELAYED:
          tasks = await queue.getDelayed()
          break
        case QueueJobStatus.WAITING:
          tasks = await queue.getWaiting()
          break
        default:
          // 如果未指定状态，则获取所有状态的任务
          const [active, completed, failed, delayed, waiting] = await Promise.all([
            queue.getActive(),
            queue.getCompleted(0, 100), // 限制返回的已完成任务数量
            queue.getFailed(0, 100), // 限制返回的失败任务数量
            queue.getDelayed(),
            queue.getWaiting(),
          ])
          tasks = [...active, ...completed, ...failed, ...delayed, ...waiting]
          break
      }

      // 格式化任务数据
      const formattedTasks = await Promise.all(
        tasks.map(async (job) => {
          try {
            const progress = await job.progress()

            // 获取任务状态
            let jobStatus = status
            if (!jobStatus) {
              if (job.processedOn && job.finishedOn) {
                jobStatus = job.failedReason ? QueueJobStatus.FAILED : QueueJobStatus.COMPLETED
              } else if (job.processedOn) {
                jobStatus = QueueJobStatus.ACTIVE
              } else if (job.opts?.delay && new Date(job.opts.delay) > new Date()) {
                jobStatus = QueueJobStatus.DELAYED
              } else {
                jobStatus = QueueJobStatus.WAITING
              }
            }

            return {
              id: job.id,
              taskId: job.data?.id, // 实际任务ID
              type: job.data?.type,
              platform: job.data?.platform,
              progress: progress || 0,
              status: jobStatus,
              createdAt: job.data?.createdAt,
              processedOn: job.processedOn ? new Date(job.processedOn) : null,
              finishedOn: job.finishedOn ? new Date(job.finishedOn) : null,
              attemptsMade: job.attemptsMade,
              failedReason: job.failedReason,
              data: job.data,
              timestamp: job.timestamp ? new Date(job.timestamp) : null,
            }
          } catch (error) {
            console.error(`[TaskService] 格式化任务 ${job.id} 数据失败:`, error)
            return {
              id: job.id,
              error: '格式化任务数据失败',
            }
          }
        }),
      )

      return formattedTasks
    } catch (error) {
      console.error(`[TaskService] 获取队列 ${queueName} ${status || '所有'} 任务失败:`, error)
      throw error
    }
  }

  public async createTask(
    projectId: string,
    params: object,
    userId: string,
    reason: string,
    type: TaskType,
    meta?: Record<string, any>,
  ): Promise<SimilarChannelTask> {
    // pending task
    const task = await prisma.similarChannelTask.create({
      data: {
        projectId: projectId,
        status: SimilarChannelTaskStatus.PENDING,
        params: params,
        createdBy: userId,
        reason: (reason as TaskReason) || TaskReason.SEARCH,
        type: type,
      },
    })

    // add into queue
    await this.addTaskToQueue(task)

    return task
  }

  /**
   * 添加任务到对应的队列
   * @param task 已创建的任务
   * @param params 任务参数
   * @param userId 创建者ID
   */
  public async addTaskToQueue(task: SimilarChannelTask): Promise<void> {
    try {
      if (!task) {
        throw new Error('任务不存在')
      }

      if (task.status !== SimilarChannelTaskStatus.PENDING) {
        console.warn(`[TaskService] 任务 ${task.id} is in processing`)
        return
      }

      const params = task.params as TaskParams
      // processing
      await prisma.similarChannelTask.update({
        where: { id: task.id },
        data: { status: SimilarChannelTaskStatus.PROCESSING },
      })

      const jobData = {
        id: task.id,
        params,
        type: task.type,
        result: task.result,
        platform: 'unknown',
        reason: task.reason,
        createdAt: task.createdAt,
        createdBy: task.createdBy,
      }
      if (task.type === TaskType.EASYKOL_TRACK) {
        easykolTrackQueue.add(jobData as IEmbeddingTask)
        return
      } else {
        const platform = TaskParamsService.getPlatform(params as TaskParams)
        jobData.platform = platform

        switch (platform) {
          case 'youtube':
            youtubeTaskQueue.add(jobData as IEmbeddingTask)
            break
          case 'tiktok':
            tiktokTaskQueue.add(jobData as IEmbeddingTask)
            break
          case 'instagram':
            instagramTaskQueue.add(jobData as IEmbeddingTask)
            break
          case 'twitter':
            twitterTaskQueue.add(jobData as IEmbeddingTask)
            break
          default:
            console.warn(`未知平台: ${platform}, 任务ID: ${task.id}`)
            // 回滚任务状态
            await prisma.similarChannelTask.update({
              where: { id: task.id },
              data: { status: SimilarChannelTaskStatus.PENDING },
            })
            break
        }
      }
    } catch (error) {
      console.error(`添加任务 ${task.id} 到队列失败:`, error)
      await prisma.similarChannelTask.update({
        where: { id: task.id },
        data: { status: SimilarChannelTaskStatus.FAILED },
      })
      throw error
    }
  }

  /**
   * 批量创建受众分析任务
   * - audience 模式：每5个一批，每批间隔1分钟
   * - post audience 模式：每5个一批，每批间隔30秒
   */
  public async createBatchAudienceTasks(
    links: string[],
    mode: BatchAudienceMode,
    userId: string,
  ): Promise<BatchAudienceTasksResponse> {
    const results: BatchAudienceTaskResult[] = []
    const taskIds: string[] = []

    const batchSize = 5
    const delayBetweenBatches = mode === 1 ? 60_000 : 30_000 // audience: 60s, post: 30s
    const defaultProjectId = `batch_ + ${userId} + ${Date.now()}`

    // batch record
    const batchRecord = await prisma.batchTaskRecord.create({
      data: {
        userId,
        taskType:
          mode === BatchAudienceMode.AUTHOR ? TaskType.AUDIENCE_ANALYSIS : TaskType.POST_AUDIENCE,
        rawInput: { links, mode },
        tasks: [],
        summary: {
          total: links.length,
          success: 0,
          failed: 0,
          batchSize,
          totalBatches: Math.ceil(links.length / batchSize),
          delayBetweenBatches: delayBetweenBatches / 1000,
          mode: mode,
        },
      },
    })

    // mode=2 时查询用户的 Google Sheet ID
    let userGoogleSheetId: string | null = null
    if (mode === 2) {
      try {
        const userGoogleSheet = await getUserGoogleSheet(userId)
        if (!userGoogleSheet) {
          // 失败时也要更新批次记录
          await prisma.batchTaskRecord.update({
            where: { id: batchRecord.id },
            data: {
              result: {
                error: 'User Google Sheet not found. Please initialize Google Sheet first.',
              },
              summary: {
                total: links.length,
                success: 0,
                failed: links.length,
                batchSize,
                totalBatches: 0,
                delayBetweenBatches: delayBetweenBatches / 1000,
                mode: mode,
              },
            },
          })

          return {
            results: [
              {
                taskId: '',
                link: '',
                status: 'failed',
                error: 'User Google Sheet not found. Please initialize Google Sheet first.',
              },
            ],
            summary: {
              total: links.length,
              success: 0,
              failed: links.length,
              batchSize,
              totalBatches: 0,
              delayBetweenBatches: delayBetweenBatches / 1000,
              mode: mode,
            },
            batchId: batchRecord.id,
          }
        }
        userGoogleSheetId = userGoogleSheet.spreadsheetId
      } catch (error) {
        console.error('Failed to get user Google Sheet:', error)
        // 失败时也要更新批次记录
        await prisma.batchTaskRecord.update({
          where: { id: batchRecord.id },
          data: {
            result: {
              error: 'Failed to retrieve user Google Sheet',
            },
            summary: {
              total: links.length,
              success: 0,
              failed: links.length,
              batchSize,
              totalBatches: 0,
              delayBetweenBatches: delayBetweenBatches / 1000,
              mode: mode,
            },
          },
        })

        return {
          results: [
            {
              taskId: '',
              link: '',
              status: 'failed',
              error: 'Failed to retrieve user Google Sheet',
            },
          ],
          summary: {
            total: links.length,
            success: 0,
            failed: links.length,
            batchSize,
            totalBatches: 0,
            delayBetweenBatches: delayBetweenBatches / 1000,
            mode: mode,
          },
          batchId: batchRecord.id,
        }
      }
    }

    const preparedTasks: Array<{
      link: string
      platform: KolPlatform
      batchIndex: number
      delay: number
      data: any
    }> = []

    if (mode === 1) {
      // Mode 1: Audience Analysis - 提取用户名
      const processedUsernames = new Set<string>()

      await Bluebird.map(
        links,
        async (link, i) => {
          const batchIndex = Math.floor(i / batchSize)
          const delay = batchIndex * delayBetweenBatches

          try {
            const platform = parseUrlUtils.getPlatformFromUrl(link)
            if (!platform) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: 'Unable to determine platform from URL',
              })
              return
            }

            const username = await parseUrlUtils.extractUsernameFromLink(link, platform)
            if (!username) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: 'Unable to extract username from URL',
              })
              return
            }

            // check author name
            const validatedUsername = await this.validateUsername(username, platform)
            if (!validatedUsername) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: `User ${username} not found on ${platform}`,
              })
              return
            }

            // 去重逻辑：检查是否已经处理过相同的 username+platform 组合
            const userPlatformKey = `${validatedUsername}@${platform}`
            if (processedUsernames.has(userPlatformKey)) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: `Duplicate username ${validatedUsername} on ${platform} - task already exists`,
              })
              return
            }
            processedUsernames.add(userPlatformKey)

            preparedTasks.push({
              link,
              platform,
              batchIndex,
              delay,
              data: { username: validatedUsername },
            })
          } catch (error) {
            console.error(`Failed to prepare audience task for link ${link}:`, error)
            results.push({
              taskId: '',
              link,
              status: 'failed',
              error: error instanceof Error ? error.message : String(error),
            })
          }
        },
        { concurrency: 10 },
      )

      // 批量创建 Audience Analysis 任务
      for (const prepTask of preparedTasks) {
        try {
          const params = {
            projectId: defaultProjectId,
            platform: prepTask.platform,
            source: prepTask.data.username,
          }

          const task = await prisma.similarChannelTask.create({
            data: {
              projectId: defaultProjectId,
              status: SimilarChannelTaskStatus.PROCESSING,
              params: params,
              createdBy: userId,
              reason: TaskReason.AUDIENCE_ANALYSIS,
              type: TaskType.AUDIENCE_ANALYSIS,
            },
          })

          taskIds.push(task.id)

          const jobData = {
            id: task.id,
            params,
            result: {},
            type: TaskType.AUDIENCE_ANALYSIS,
            platform: prepTask.platform.toLowerCase(),
            reason: TaskReason.AUDIENCE_ANALYSIS,
            createdAt: task.createdAt,
            createdBy: userId,
          }

          const jobOptions = {
            delay: prepTask.delay,
            priority: 5,
            attempts: 1,
            backoff: { type: 'exponential' as const, delay: 1000 },
          }

          const queue = this.getQueueForPlatform(prepTask.platform)
          await queue.add(jobData as IEmbeddingTask, jobOptions)

          results.push({
            taskId: task.id,
            link: prepTask.link,
            status: 'created',
            delay: prepTask.delay,
            batchIndex: prepTask.batchIndex,
            username: prepTask.data.username,
          })
        } catch (error) {
          console.error(`Failed to create audience task for link ${prepTask.link}:`, error)
          results.push({
            taskId: '',
            link: prepTask.link,
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
          })
        }
      }
    } else {
      // Mode 2: Post Audience - 提取视频ID
      const processedVideoIds = new Set<string>()

      await Bluebird.map(
        links,
        async (link, i) => {
          const batchIndex = Math.floor(i / batchSize)
          const delay = batchIndex * delayBetweenBatches

          try {
            const platform = parseUrlUtils.getPlatformFromUrl(link)
            if (!platform) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: 'Unable to determine platform from URL',
              })
              return
            }

            const videoId = parseUrlUtils.extractVideoId(link)
            if (!videoId) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: 'Unable to extract video ID from URL',
              })
              return
            }

            // check video is exists
            const videoExists = await this.validateVideoExists(videoId, platform)
            if (!videoExists) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: `Video ${videoId} not found on ${platform}`,
              })
              return
            }

            // 去重逻辑：检查是否已经处理过相同的 videoId+platform 组合
            const videoPlatformKey = `${videoId}@${platform}`
            if (processedVideoIds.has(videoPlatformKey)) {
              results.push({
                taskId: '',
                link,
                status: 'failed',
                error: `Duplicate video ${videoId} on ${platform} - task already exists`,
              })
              return
            }
            processedVideoIds.add(videoPlatformKey)

            preparedTasks.push({
              link,
              platform,
              batchIndex,
              delay,
              data: { videoId, postLink: link },
            })
          } catch (error) {
            console.error(`Failed to prepare post audience task for link ${link}:`, error)
            results.push({
              taskId: '',
              link,
              status: 'failed',
              error: error instanceof Error ? error.message : String(error),
            })
          }
        },
        { concurrency: 10 },
      )

      for (const prepTask of preparedTasks) {
        try {
          const params = {
            publicationId: defaultProjectId,
            googleSheetId: userGoogleSheetId,
            platform: prepTask.platform,
            postLink: prepTask.data.postLink,
            videoId: prepTask.data.videoId,
          }

          const task = await prisma.similarChannelTask.create({
            data: {
              projectId: defaultProjectId,
              status: SimilarChannelTaskStatus.PROCESSING,
              params: params,
              createdBy: userId,
              reason: TaskReason.POST_AUDIENCE,
              type: TaskType.POST_AUDIENCE,
            },
          })

          taskIds.push(task.id)

          const jobData = {
            id: task.id,
            params,
            result: {},
            type: TaskType.POST_AUDIENCE,
            platform: prepTask.platform.toLowerCase(),
            reason: TaskReason.POST_AUDIENCE,
            createdAt: task.createdAt,
            createdBy: userId,
          }

          // （带延迟）
          const jobOptions = {
            delay: prepTask.delay,
            priority: 5,
            attempts: 1,
            backoff: { type: 'exponential' as const, delay: 1000 },
          }

          const queue = this.getQueueForPlatform(prepTask.platform)
          await queue.add(jobData as IEmbeddingTask, jobOptions)

          results.push({
            taskId: task.id,
            link: prepTask.link,
            status: 'created',
            delay: prepTask.delay,
            batchIndex: prepTask.batchIndex,
            videoId: prepTask.data.videoId,
          })
        } catch (error) {
          console.error(`Failed to create post audience task for link ${prepTask.link}:`, error)
          results.push({
            taskId: '',
            link: prepTask.link,
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
          })
        }
      }
    }

    const successCount = results.filter((r) => r.status === 'created').length
    const failedCount = results.filter((r) => r.status === 'failed').length
    const totalBatches = Math.ceil(links.length / batchSize)

    // 更新批次记录
    const updatedBatchRecord = await prisma.batchTaskRecord.update({
      where: { id: batchRecord.id },
      data: {
        tasks: taskIds,
        result: { results },
        summary: {
          total: links.length,
          success: successCount,
          failed: failedCount,
          batchSize,
          totalBatches,
          delayBetweenBatches: delayBetweenBatches / 1000,
          mode: mode,
        },
      },
    })

    return {
      results,
      summary: {
        total: links.length,
        success: successCount,
        failed: failedCount,
        batchSize,
        totalBatches,
        delayBetweenBatches: delayBetweenBatches / 1000, // 转换为秒
        mode: mode,
      },
      batchId: updatedBatchRecord.id, // 返回批次ID
    }
  }

  /**
   * 获取平台对应的队列
   */
  private getQueueForPlatform(platform: KolPlatform): Bull.Queue<IEmbeddingTask> {
    switch (platform) {
      case KolPlatform.YOUTUBE:
        return youtubeTaskQueue
      case KolPlatform.TIKTOK:
        return tiktokTaskQueue
      case KolPlatform.INSTAGRAM:
        return instagramTaskQueue
      case KolPlatform.TWITTER:
        return twitterTaskQueue
      default:
        throw new Error(`Unsupported platform: ${platform}`)
    }
  }
  /**
   * 验证视频是否存在
   */
  private async validateVideoExists(videoId: string, platform: KolPlatform): Promise<boolean> {
    try {
      switch (platform) {
        case KolPlatform.YOUTUBE: {
          return await retryUtil.retryWithValidator(
            async () => {
              const videoData = await YoutubeApi.getInstance().getVideo(videoId)
              return videoData !== null
            },
            (result) => result === true,
            3,
            500,
            2,
            (result, error, retryCount, nextDelay) => {
              console.warn(
                `YouTube视频验证失败，视频ID: ${videoId}，将在 ${nextDelay}ms 后进行第 ${retryCount}/2 次重试`,
                error || `返回结果: ${result}`,
              )
            },
          )
        }

        case KolPlatform.TIKTOK: {
          return await retryUtil.retryWithValidator(
            async () => {
              const videoData = await TiktokApi.getInstance().getVideoDetail(videoId)
              return videoData !== null
            },
            (result) => result === true,
            3,
            500,
            2,
            (result, error, retryCount, nextDelay) => {
              console.warn(
                `TikTok视频验证失败，视频ID: ${videoId}，将在 ${nextDelay}ms 后进行第 ${retryCount}/2 次重试`,
                error || `返回结果: ${result}`,
              )
            },
          )
        }

        case KolPlatform.INSTAGRAM: {
          return await retryUtil.retryWithValidator(
            async () => {
              const postData = await InstagramRapidApiV3.getInstance().getPost(videoId)
              return postData !== null
            },
            (result) => result === true,
            3,
            500,
            2,
            (result, error, retryCount, nextDelay) => {
              console.warn(
                `Instagram视频验证失败，视频ID: ${videoId}，将在 ${nextDelay}ms 后进行第 ${retryCount}/2 次重试`,
                error || `返回结果: ${result}`,
              )
            },
          )
        }

        default:
          return false
      }
    } catch (error) {
      console.error(`Failed to validate video ${videoId} on ${platform}:`, error)
      return false
    }
  }

  /**
   * 验证用户名是否存在
   */
  private async validateUsername(username: string, platform: KolPlatform): Promise<string | null> {
    try {
      switch (platform) {
        case KolPlatform.TIKTOK: {
          return await retryUtil.retryWithValidator(
            async () => {
              const userDetail = await TiktokApi.getInstance().getUserDetail({
                unique_id: username.toLowerCase(),
              })
              return userDetail?.user?.uniqueId || null
            },
            (result) => result !== null,
            3,
            500,
            2,
            (result, error, retryCount, nextDelay) => {
              console.warn(
                `TikTok用户验证失败，用户名: ${username}，将在 ${nextDelay}ms 后进行第 ${retryCount}/2 次重试`,
                error || `返回结果: ${result}`,
              )
            },
          )
        }

        case KolPlatform.INSTAGRAM: {
          return await retryUtil.retryWithValidator(
            async () => {
              const insUser = await InstagramRapidApiV3.getInstance().getUserWithoutAbout({
                username,
              })
              return insUser?.id ? username : null
            },
            (result) => result !== null,
            3,
            500,
            2,
            (result, error, retryCount, nextDelay) => {
              console.warn(
                `Instagram用户验证失败，用户名: ${username}，将在 ${nextDelay}ms 后进行第 ${retryCount}/2 次重试`,
                error || `返回结果: ${result}`,
              )
            },
          )
        }

        // only ucId
        case KolPlatform.YOUTUBE: {
          return await retryUtil.retryWithValidator(
            async () => {
              const channelData = await YoutubeApi.getInstance().getChannel(username)
              return channelData?.channelId || null
            },
            (result) => result !== null,
            3,
            500,
            2,
            (result, error, retryCount, nextDelay) => {
              console.warn(
                `YouTube频道验证失败，频道ID: ${username}，将在 ${nextDelay}ms 后进行第 ${retryCount}/2 次重试`,
                error || `返回结果: ${result}`,
              )
            },
          )
        }

        default:
          return null
      }
    } catch (error) {
      console.error(`Failed to validate username ${username} on ${platform}:`, error)
      return null
    }
  }

  public async createTrackTask(
    projectId: string,
    params: object,
    userId: string,
    reason: string,
    type: TaskType,
    strategyId?: string,
    status?: SimilarChannelTaskStatus,
    errors?: Record<string, any>,
  ): Promise<SimilarChannelTask> {
    const task = await prisma.similarChannelTask.create({
      data: {
        projectId: projectId,
        params: params,
        createdBy: userId,
        reason: (reason as TaskReason) || TaskReason.SEARCH,
        type: type,
        strategyId: strategyId,
        status: status || SimilarChannelTaskStatus.PENDING,
        ...(errors && { errors }),
      },
    })
    await this.addTaskToQueue(task)

    return task
  }

  public async createLongCrawlerTask(
    projectId: string,
    params: object,
    userId: string,
    reason: string,
    type: TaskType,
    meta: Record<string, any>,
  ): Promise<SimilarChannelTask> {
    const task = await prisma.similarChannelTask.create({
      data: {
        projectId: projectId,
        status: SimilarChannelTaskStatus.PENDING,
        params: params,
        createdBy: userId,
        reason: (reason as TaskReason) || TaskReason.SEARCH,
        type: type,
        meta: meta,
      },
    })

    try {
      // 更新任务状态为处理中
      await prisma.similarChannelTask.update({
        where: { id: task.id },
        data: { status: SimilarChannelTaskStatus.PROCESSING },
      })

      const queue = this.getQueue(type)
      const platform = (params as InsLongCrawlerTaskParams).platform || 'unknown'
      // 使用固定格式的jobId: 任务类型-任务ID
      const jobId = `${type}-${task.id}`

      await queue.add(
        {
          id: task.id,
          params: params,
          type: type,
          platform: platform,
          reason: reason as TaskReason,
          createdAt: task.createdAt,
          createdBy: userId,
        },
        {
          jobId: jobId,
          removeOnComplete: true,
          removeOnFail: false,
        },
      )
      console.log(`[TaskService] 长任务已创建并添加到队列，任务ID: ${task.id}，作业ID: ${jobId}`)
    } catch (queueError) {
      console.error(`[TaskService] 添加任务 ${task.id} 到队列失败:`, queueError)
    }

    return task
  }

  public async getNextTask(): Promise<SimilarChannelTask[]> {
    const oneDayAgo = dayjs().subtract(1, 'day').toDate()
    try {
      const tasks = await prisma.similarChannelTask.findMany({
        where: {
          status: SimilarChannelTaskStatus.PENDING,
          createdAt: { gt: oneDayAgo },
          type: { not: TaskType.LONG_CRAWLER },
        },
        orderBy: { createdAt: 'asc' },
        take: +BATCH_FETCH_TASK_COUNT,
      })

      if (!tasks?.length) {
        return []
      }

      await prisma.similarChannelTask.updateMany({
        where: { id: { in: tasks.map((task) => task.id) } },
        data: { status: SimilarChannelTaskStatus.PROCESSING },
      })

      return tasks
    } catch (error) {
      console.error(`Error receiving tasks: ${error}`)
      throw new Error('An error occurred while receiving tasks.')
    }
  }

  public async finishTask(
    id: string,
    result: Record<string, any>,
    meta?: Record<string, any>,
  ): Promise<SimilarChannelTask> {
    assert(id, new Error('Task ID is required'))

    return await prisma.$transaction(
      async (tx) => {
        const task = await tx.similarChannelTask.findUniqueOrThrow({
          where: { id },
        })

        if (task.status === SimilarChannelTaskStatus.COMPLETED) {
          return task
        }

        const updateData = {
          result: {
            ...result,
          },
          status: SimilarChannelTaskStatus.COMPLETED,
          ...(meta && { meta }),
        }

        return await tx.similarChannelTask.update({
          where: { id },
          data: updateData,
        })
      },
      {
        timeout: 10_000,
      },
    )
  }

  public async getTaskByTaskId(id: string): Promise<SimilarChannelTask> {
    assert(id, new Error('taskId is required'))
    return await prisma.similarChannelTask.findUniqueOrThrow({ where: { id } })
  }

  public async failTask(id: string, error: any): Promise<SimilarChannelTask> {
    try {
      let task: any
      task = await this.getTaskByTaskId(id)

      if (task.isFailed) {
        return task
      }
      const timeCostInMilliseconds = new Date().getTime() - task.createdAt.getTime()
      console.log(`任务 ${id} 处理失败，耗时 ${timeCostInMilliseconds} 毫秒`)

      const result = task.result || {}
      Object.assign(result, { timeCost: timeCostInMilliseconds })

      task = await prisma.similarChannelTask.update({
        where: { id },
        data: { errors: [error], status: SimilarChannelTaskStatus.FAILED, result },
      })
      assert(task.createdBy, new Error('task createdBy is required'))
      // similar task refund quota and platfrom != instagram
      if (!task.isInstagram && task.type === TaskType.SIMILAR) {
        await MembershipService.getInstance().addQuota(
          task.createdBy,
          QuotaType.FAILED_TASK,
          QuotaCost.FAILED_TASK,
          `Refund quota,Task failed: ${task.id},taskType: ${task.type}`,
          task.id,
          task.projectId,
        )
      }

      return task
    } catch (error) {
      console.error(`Error fail task: ${error}`)
      throw new Error('An error occurred while fail the task.')
    }
  }

  // 获取正在进行中的项目相似任务下的所有作者
  public async getOngoingProjectSimilarTasksAuthors(projectId: string): Promise<string[]> {
    try {
      assert(projectId, new Error('projectId is required'))
      const project = await prisma.project.findFirst({
        where: { id: projectId, deletedAt: null },
      })

      assert(project, new Error('project not found'))

      const similarTasks = await prisma.similarChannelTask.findMany({
        where: {
          projectId: projectId,
          status: {
            in: [SimilarChannelTaskStatus.COMPLETED, SimilarChannelTaskStatus.RESULT_READY],
          },
          type: { in: [TaskType.SIMILAR] },
        },
        orderBy: {
          createdAt: 'asc',
        },
      })

      if (!similarTasks?.length) {
        return []
      }

      let index = 0
      for (let i = 0; i < similarTasks.length; i++) {
        if (similarTasks[i].isTerminated) {
          index = i + 1
        }
      }

      const nonTerminatedTasks = similarTasks.slice(index)

      console.log(`获取到未终结的${nonTerminatedTasks.length}个任务`)

      if (!nonTerminatedTasks?.length) {
        return []
      }

      const latestTask = nonTerminatedTasks.sort(
        (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
      )[0]
      let uniqueIds: string[] = []

      switch ((latestTask.params as any).platform) {
        case KolPlatform.TIKTOK:
          uniqueIds = nonTerminatedTasks.map((task) => (task.result as any).uniqueIds).flat()
          break
        // youtube and ins not supported
        default:
          throw new Error('Unspported platform')
      }
      return [...new Set(uniqueIds)]
    } catch (error) {
      console.error(error)
      return []
    }
  }

  /**
   * 控制任务状态（暂停/恢复/完成）
   * @param taskId 任务ID
   * @param action 操作类型：pause(暂停)、resume(恢复)、complete(完成)
   * @param reason 操作原因
   * @returns 更新后的任务
   */
  public async controlTask(
    taskId: string,
    action: 'pause' | 'resume' | 'complete',
    reason?: string,
  ): Promise<SimilarChannelTask> {
    try {
      // 1. 获取任务信息
      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      // 2. 根据操作类型执行不同的逻辑
      switch (action) {
        case 'pause':
          return await this.pauseTask(task, reason)
        case 'resume':
          return await this.resumeTask(task)
        case 'complete':
          return await this.completeTask(task, reason)
        default:
          throw new Error(`不支持的操作类型: ${action}`)
      }
    } catch (error) {
      console.error(`[TaskService] 控制任务 ${taskId} 失败:`, error)
      throw error
    }
  }

  /**
   * 暂停任务
   * @param task 任务对象或任务ID
   * @param reason 暂停原因
   * @returns 更新后的任务
   */
  private async pauseTask(
    task: SimilarChannelTask | string,
    reason?: string,
  ): Promise<SimilarChannelTask> {
    const taskObj =
      typeof task === 'string'
        ? await prisma.similarChannelTask.findUniqueOrThrow({ where: { id: task } })
        : task

    if (
      taskObj.status !== SimilarChannelTaskStatus.PROCESSING &&
      taskObj.status !== SimilarChannelTaskStatus.PENDING
    ) {
      throw new Error(`任务状态为 ${taskObj.status}，不能暂停`)
    }

    const updatedTask = await prisma.similarChannelTask.update({
      where: { id: taskObj.id },
      data: {
        status: SimilarChannelTaskStatus.PAUSING, // 暂停中
      },
    })

    // try {
    //   const queue = this.getQueue(taskObj.type)
    //   const jobId = `${taskObj.type}-${taskObj.id}`
    //   const job = await queue.getJob(jobId)
    //   if (job) {
    //     await job.remove()
    //   }

    // } catch (queueError) {
    //   console.error(`[TaskService] 从队列中移除任务 ${taskObj.id} 失败:`, queueError)
    // }

    console.info(`[TaskService] 任务 ${taskObj.id} 已暂停${reason ? `，原因: ${reason}` : ''}`)
    return updatedTask
  }

  /**
   * 恢复任务
   * @param task 任务对象或任务ID
   * @returns 更新后的任务
   */
  private async resumeTask(task: SimilarChannelTask): Promise<SimilarChannelTask> {
    const taskObj =
      typeof task === 'string'
        ? await prisma.similarChannelTask.findUniqueOrThrow({ where: { id: task } })
        : task

    if (taskObj.status !== SimilarChannelTaskStatus.PAUSED) {
      throw new Error(`任务状态为 ${taskObj.status}，不能恢复`)
    }

    const updatedTask = await prisma.similarChannelTask.update({
      where: { id: taskObj.id },
      data: {
        status: SimilarChannelTaskStatus.PROCESSING,
      },
    })

    try {
      const queue = this.getQueue(taskObj.type)
      const platform = (taskObj.params as any).platform || 'unknown'
      const jobId = `${taskObj.type}-${taskObj.id}`

      await queue.add(
        {
          id: taskObj.id,
          params: taskObj.params,
          result: taskObj.result,
          type: taskObj.type,
          platform: platform,
          reason: taskObj.reason,
          createdAt: taskObj.createdAt,
          createdBy: taskObj.createdBy,
        } as IEmbeddingTask,
        {
          jobId: jobId,
          removeOnComplete: true,
          removeOnFail: false,
        },
      )

      console.log(`[TaskService] 任务 ${taskObj.id} 已重新添加到队列，作业ID: ${jobId}`)
    } catch (queueError) {
      console.error(`[TaskService] 重新添加任务 ${taskObj.id} 到队列失败:`, queueError)
    }

    console.info(`[TaskService] 任务 ${taskObj.id} 已恢复`)
    return updatedTask
  }

  /**
   * 手动完成任务
   * @param task 任务对象或任务ID
   * @param reason 完成原因
   * @returns 更新后的任务
   */
  private async completeTask(
    task: SimilarChannelTask | string,
    reason?: string,
  ): Promise<SimilarChannelTask> {
    const taskObj =
      typeof task === 'string'
        ? await prisma.similarChannelTask.findUniqueOrThrow({ where: { id: task } })
        : task

    // 只允许 pausing、processing、paused 三种状态
    if (
      taskObj.status !== SimilarChannelTaskStatus.PROCESSING &&
      taskObj.status !== SimilarChannelTaskStatus.PAUSED &&
      taskObj.status !== SimilarChannelTaskStatus.PAUSING
    ) {
      throw new Error(`任务状态为 ${taskObj.status}，不能手动完成`)
    }

    const timeCostInMilliseconds = Date.now() - taskObj.createdAt.getTime()
    // paused->completed，pausing/processing->completing
    let nextStatus: SimilarChannelTaskStatus = SimilarChannelTaskStatus.COMPLETING
    if (taskObj.status === SimilarChannelTaskStatus.PAUSED) {
      nextStatus = SimilarChannelTaskStatus.COMPLETED
    }

    const updatedTask = await prisma.similarChannelTask.update({
      where: { id: taskObj.id },
      data: {
        status: nextStatus,
        result: {
          ...((taskObj.result as any) || {}),
          timeCost: timeCostInMilliseconds,
          manuallyCompleted: true,
          manuallyCompletedReason: reason || '用户手动完成',
        },
      },
    })

    console.info(`[TaskService] 任务 ${taskObj.id} 已手动完成${reason ? `，原因: ${reason}` : ''}`)
    return updatedTask
  }

  /**
   * 根据队列名称手动失败所有活动任务
   * @param queueName 队列名称
   * @param reason 失败原因
   * @returns 失败任务的数量和任务ID列表
   */
  public async failAllQueueTasks(
    queueName: string,
    reason: string,
  ): Promise<{ count: number; failedJobIds: string[] }> {
    try {
      const queue = this.getQueueByName(queueName)

      // 获取所有活动中的任务
      const activeTasks = await queue.getActive()
      console.log(`[TaskService] 获取到 ${queueName} 队列中 ${activeTasks.length} 个活动任务`)

      if (!activeTasks.length) {
        return { count: 0, failedJobIds: [] }
      }

      // 记录失败的任务ID
      const failedJobIds: string[] = []

      // 遍历所有活动任务，将其标记为失败
      for (const job of activeTasks) {
        try {
          // 将任务移至失败状态
          await job.moveToFailed(new Error(`手动失败: ${reason || '管理员操作'}`), true)

          // 如果任务关联到数据库中的任务记录，更新数据库状态
          if (job.data && job.data.id) {
            await prisma.similarChannelTask.updateMany({
              where: { id: job.data.id },
              data: {
                status: SimilarChannelTaskStatus.FAILED,
                errors: [{ message: `手动失败: ${reason || '管理员操作'}`, timestamp: new Date() }],
              },
            })
          }

          failedJobIds.push(job.id as string)
          console.log(`[TaskService] 成功将任务 ${job.id} 标记为失败`)
        } catch (jobError) {
          console.error(`[TaskService] 将任务 ${job.id} 标记为失败时出错:`, jobError)
        }
      }

      return {
        count: failedJobIds.length,
        failedJobIds,
      }
    } catch (error) {
      console.error(`[TaskService] 失败所有 ${queueName} 队列任务时出错:`, error)
      throw error
    }
  }

  /**
   * 根据任务ID手动失败特定任务
   * @param queueName 队列名称
   * @param jobId 任务ID
   * @param reason 失败原因
   * @returns 操作结果
   */
  public async failQueueTaskById(
    queueName: string,
    jobId: string,
    reason: string,
  ): Promise<{ success: boolean; message: string }> {
    try {
      const queue = this.getQueueByName(queueName)

      // 获取指定的任务
      const job = await queue.getJob(jobId)

      if (!job) {
        return { success: false, message: `在 ${queueName} 队列中未找到任务 ${jobId}` }
      }

      // 将任务移至失败状态
      await job.moveToFailed(new Error(`手动失败: ${reason || '管理员操作'}`), true)

      // 如果任务关联到数据库中的任务记录，更新数据库状态
      if (job.data && job.data.id) {
        await prisma.similarChannelTask.updateMany({
          where: { id: job.data.id },
          data: {
            status: SimilarChannelTaskStatus.FAILED,
            errors: [{ message: `手动失败: ${reason || '管理员操作'}`, timestamp: new Date() }],
          },
        })
      }

      console.log(`[TaskService] 成功将任务 ${jobId} 标记为失败`)
      return { success: true, message: `成功将任务 ${jobId} 标记为失败` }
    } catch (error) {
      console.error(`[TaskService] 将任务 ${jobId} 标记为失败时出错:`, error)
      return {
        success: false,
        message: `将任务标记为失败时出错: ${error instanceof Error ? error.message : '未知错误'}`,
      }
    }
  }

  /**
   * 恢复已完成的长时间爬取任务（用于更新参数后重新入队）
   * @param task 任务对象或任务ID
   * @returns 更新后的任务
   */
  public async resumeCompletedLongCrawlerTask(
    task: SimilarChannelTask | string,
  ): Promise<SimilarChannelTask> {
    const taskObj =
      typeof task === 'string'
        ? await prisma.similarChannelTask.findUniqueOrThrow({ where: { id: task } })
        : task

    if (taskObj.type !== TaskType.LONG_CRAWLER) {
      throw new Error(`任务类型为 ${taskObj.type}，不是长时间爬取任务`)
    }

    if (taskObj.status !== SimilarChannelTaskStatus.COMPLETED) {
      throw new Error(`任务状态为 ${taskObj.status}，只能恢复已完成的任务`)
    }

    const updatedTask = await prisma.similarChannelTask.update({
      where: { id: taskObj.id },
      data: {
        status: SimilarChannelTaskStatus.PROCESSING,
      },
    })

    try {
      const queue = this.getQueue(taskObj.type)
      const platform = (taskObj.params as any).platform || 'unknown'
      const jobId = `${taskObj.type}-${taskObj.id}`

      await queue.add(
        {
          id: taskObj.id,
          params: taskObj.params,
          result: updatedTask.result,
          type: taskObj.type,
          platform: platform,
          reason: taskObj.reason,
          createdAt: taskObj.createdAt,
          createdBy: taskObj.createdBy,
        } as IEmbeddingTask,
        {
          jobId: jobId,
          removeOnComplete: true,
          removeOnFail: false,
        },
      )
    } catch (queueError) {
      await prisma.similarChannelTask.update({
        where: { id: taskObj.id },
        data: {
          status: SimilarChannelTaskStatus.COMPLETED,
        },
      })
      console.error(`[TaskService] 重新添加任务 ${taskObj.id} 到队列失败:`, queueError)
      throw new Error(`重新添加任务到队列失败: ${queueError}`)
    }

    console.info(`[TaskService] 长时间爬取任务 ${taskObj.id} 已恢复并重新入队`)
    return updatedTask
  }

  /**
   * 更新任务状态
   * @param taskId 任务ID
   * @param status 新的任务状态
   * @returns 更新后的任务
   */
  public async updateTaskStatus(
    taskId: string,
    status: SimilarChannelTaskStatus,
  ): Promise<SimilarChannelTask> {
    try {
      const updatedTask = await prisma.similarChannelTask.update({
        where: { id: taskId },
        data: { status },
      })

      console.log(`[TaskService] 任务 ${taskId} 状态已更新为 ${status}`)
      return updatedTask
    } catch (error) {
      console.error(`[TaskService] 更新任务 ${taskId} 状态失败:`, error)
      throw error
    }
  }

  /**
   * get task details by id
   */
  public async getTaskById(taskId: string): Promise<SimilarChannelTask> {
    const task = await prisma.similarChannelTask.findUnique({
      where: { id: taskId },
    })

    if (!task) {
      throwError(ErrorCodes.NOT_FOUND, `任务 ${taskId} 不存在`)
    }

    return task
  }

  public async getUserBatchTasks(taskIds: string[], userId: string): Promise<SimilarChannelTask[]> {
    const tasks = await prisma.similarChannelTask.findMany({
      where: {
        id: { in: taskIds },
        createdBy: userId,
      },
      orderBy: { createdAt: 'desc' },
    })
    return tasks
  }

  public async findUnterminatedTasksByType(
    projectId: string,
    taskType: TaskType,
  ): Promise<TaskWithoutCandidate[]> {
    const allTasks = (await prisma.similarChannelTask.findMany({
      where: {
        projectId,
        type: taskType,
      },
      select: {
        id: true,
        projectId: true,
        status: true,
        params: true,
        result: true,
        errors: true,
        meta: true,
        createdBy: true,
        createdAt: true,
        updatedAt: true,
        isTerminated: true,
        reason: true,
        type: true,
        strategyId: true,
        // 排除 candidate 字段
      },
      orderBy: {
        createdAt: 'asc',
      },
    })) as unknown as TaskWithoutCandidate[]

    if (allTasks.length === 0) {
      return []
    }

    let lastTerminatedIndex = -1
    for (let i = 0; i < allTasks.length; i++) {
      if (allTasks[i].isTerminated) {
        lastTerminatedIndex = i
      }
    }

    const latestTasks = allTasks.slice(lastTerminatedIndex + 1)

    return latestTasks.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
  }

  /**
   * 查询用户所有项目下未终结的指定类型任务
   * @param userId 用户ID
   * @param taskType 任务类型
   * @returns 未终结的任务列表，按更新时间倒序排列
   */
  public async findUserUnterminatedTasksByType(
    userId: string,
    taskType: TaskType,
  ): Promise<TaskWithoutCandidate[]> {
    // 首先获取用户有权限访问的所有项目
    const userProjects = await prisma.project.findMany({
      where: {
        deletedAt: null,
        OR: [
          {
            ProjectMembership: {
              some: {
                userId: userId,
              },
            },
          },
          // 或者用户创建的任务在这些项目中
          {
            SimilarChannelTask: {
              some: {
                createdBy: userId,
              },
            },
          },
        ],
      },
      select: {
        id: true,
      },
    })

    if (userProjects.length === 0) {
      return []
    }

    const projectIds = userProjects.map((p) => p.id)

    const allTasks = (await prisma.similarChannelTask.findMany({
      where: {
        projectId: { in: projectIds },
        type: taskType,
        createdBy: userId,
      },
      select: {
        id: true,
        projectId: true,
        status: true,
        params: true,
        result: true,
        errors: true,
        meta: true,
        createdBy: true,
        createdAt: true,
        updatedAt: true,
        isTerminated: true,
        reason: true,
        type: true,
        strategyId: true,
        // 排除 candidate 字段
      },
      orderBy: {
        createdAt: 'asc',
      },
    })) as unknown as TaskWithoutCandidate[]

    if (allTasks.length === 0) {
      return []
    }

    const tasksByProject = allTasks.reduce(
      (acc, task) => {
        if (!acc[task.projectId]) {
          acc[task.projectId] = []
        }
        acc[task.projectId].push(task)
        return acc
      },
      {} as Record<string, TaskWithoutCandidate[]>,
    )

    const allUnterminatedTasks = Object.values(tasksByProject)
      .map((projectTasks) => {
        const lastTerminatedIndex = projectTasks.reduce(
          (lastIndex, task, index) => (task.isTerminated ? index : lastIndex),
          -1,
        )
        return projectTasks.slice(lastTerminatedIndex + 1)
      })
      .flat()
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())

    return allUnterminatedTasks
  }
}

export default TaskService
