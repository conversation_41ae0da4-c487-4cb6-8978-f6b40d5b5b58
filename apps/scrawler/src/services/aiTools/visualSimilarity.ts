import { IgPost } from '@/api/@types/rapidapi/Instagram'
import { ChannelVideo } from '@/api/@types/rapidapi/Youtube'
import { chatCompletionWithModelFailover } from '@/api/openai'
import { DEFAULT_MODEL_FAILOVER_ORDER } from '@/config/env'
import Sentry from '@/infras/sentry'
import { ImagePart } from '@/types/images'
import { TtVideoBasicInfo } from '@/types/kol'
import ImageUtil from '@/utils/images'
import { JsonParseUtil } from '@/utils/jsonParse'
import { retryUtil } from '@/utils/retry'
import Bluebird from 'bluebird'
import { ChatCompletionContentPart } from 'openai/resources/index.mjs'
import {
  BloggerDimensionsAnalysisPrompt,
  BloggerDimensionsAnalysisResult,
} from './prompt/dimension'

export interface BloggerDimensionsAnalysisResultWithFlag extends BloggerDimensionsAnalysisResult {
  isAnalyzedSuccessfully: boolean
}

interface VisualSimilarityInput {
  username: string
  posts?: (IgPost | TtVideoBasicInfo | ChannelVideo)[]
}

interface VisualSimilarityInputWithBio extends VisualSimilarityInput {
  id: string
  fullname: string
  biography: string
  url: string
}

export interface VisualSimilarityOutput {
  username: string
  score: number
  reason: string
}

export interface YouTubeChannelSimilarityOutput {
  username: string
  score: number
  reason: string
  videoIds: string[]
}

interface VisualSimilarityOptions {
  banList?: string[]
  allowList?: string[]
  kolDescription?: string
  countries?: string[]
}

// 垂类分析结果
export interface VerticalAnalysisResult {
  success: boolean
  error?: string
  data: VerticalAnalysisData | null
}

// 垂类分析数据
interface VerticalAnalysisData {
  kolDescription: string
  allowList: string[]
  banList: string[]
}

interface UserImageData {
  username: string
  posts: {
    thumbnail: ImagePart
    caption: string
  }[]
}

// 获取用户帖子图片
async function fetchUserImages(
  user: VisualSimilarityInputWithBio | VisualSimilarityInput,
): Promise<UserImageData> {
  const userImages: UserImageData = {
    username: user.username,
    posts: [],
  }

  if (user.posts && user.posts.length > 0) {
    const imageRequests: { url: string; caption: string }[] = []

    for (const post of user.posts.slice(0, 6)) {
      let imageUrl = ''
      let captionText = ''

      if ('thumbnail_url' in post) {
        // Instagram
        imageUrl = post.thumbnail_url || ''
        captionText = (post as IgPost).caption?.text || '无正文'
      } else if ('cover' in post) {
        // TikTok
        imageUrl = post.cover || ''
        captionText = (post as TtVideoBasicInfo).title || '无正文'
      } else if (
        'thumbnail' in post &&
        Array.isArray(post.thumbnail) &&
        post.thumbnail.length > 0
      ) {
        // YouTube
        imageUrl = post.thumbnail[0]?.url || ''
        captionText = (post as ChannelVideo).title || '无正文'
        if ((post as ChannelVideo).description) {
          captionText += ' - ' + (post as ChannelVideo).description
        }
      }

      if (imageUrl) {
        imageRequests.push({ url: imageUrl, caption: captionText })
      }
    }

    const startTime = Date.now()
    const results = await Promise.allSettled(
      imageRequests.map(async ({ url, caption }, index) => {
        const requestStartTime = Date.now()
        try {
          const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('图片获取超时')), 15000)
          })
          const thumbnail = await Promise.race([
            ImageUtil.fetchImageAsBase64(url, 2),
            timeoutPromise,
          ])
          const elapsed = Date.now() - requestStartTime
          if (elapsed > 12000) {
            console.warn(
              `[fetchUserImages] 慢速图片请求 ${index + 1}/${imageRequests.length}: ${elapsed}ms`,
            )
          }

          if (thumbnail.inlineData.data) {
            return { thumbnail, caption }
          }
          return null
        } catch (error: unknown) {
          console.warn(
            `获取图片失败: ${url.slice(0, 50)}..., 错误: ${error instanceof Error ? error.message : String(error)}`,
          )
          return null
        }
      }),
    )
    const totalElapsed = Date.now() - startTime
    const successCount = results.filter((r) => r.status === 'fulfilled' && r.value).length
    console.log(
      `[fetchUserImages] 用户 ${user.username} 图片获取完成: ${successCount}/${imageRequests.length} 成功, 耗时 ${totalElapsed}ms`,
    )
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value) {
        userImages.posts.push(result.value)
      }
    }
  }
  return userImages
}

const SYSTEM_PROMPT_TEMPLATE = (options: VisualSimilarityOptions) => {
  let prompt = `你是一个专业的社交媒体博主分析专家。请根据提供的候选博主信息（包括帖子标题和视觉内容，视觉内容主要是视频封面），评估其是否符合以下要求。

评分标准：
1. 不要给出详细的分数，只需提供二元判断（0或100）。
2. 输出100表示候选博主符合要求
3. 输出0表示候选博主不符合要求
4. 如果账号是品牌或官方账号（Business账户、企业账户、公司账户），直接评分为0，无需进一步评估

【重要提示】
评分必须严格根据以下要素进行判断，这是决定候选博主是否符合要求的唯一标准：`

  // 三要素部分，强调各自的重要性和判断标准
  if (options) {
    if (options.banList && options.banList.length > 0) {
      prompt += `\n\n【禁止类型】- 满足以下任一特征的博主必须评分为0：
${options.banList.join('\n')}

如果候选博主属于上述任何一种类型，无论其他条件如何，都必须给予0分。`
    }

    if (options.allowList && options.allowList.length > 0) {
      prompt += `\n\n【推荐类型】- 满足以下特征的博主应优先评分为100：
${options.allowList.join('\n')}

符合这些类型的博主很可能符合要求，除非同时符合禁止类型。`
    }

    if (options.kolDescription) {
      prompt += `\n\n【核心要求】- 这是最重要的评判标准，必须满足：
${options.kolDescription}

这是优先级最高的判断依据，符合这个描述的博主应评为100分，除非明确属于禁止类型。`
    }
  }

  // 如果没有提供任何三要素
  if (
    !options ||
    ((!options.banList || options.banList.length === 0) &&
      (!options.allowList || options.allowList.length === 0) &&
      !options.kolDescription)
  ) {
    prompt += `\n\n警告：未提供任何评判标准。在缺乏具体要求的情况下，请基于博主内容的质量、专业性和受众吸引力进行评判。`
  } else {
    prompt += `\n\n请严格按照上述要素评估候选博主。优先级从高到低依次是：1)品牌或官方账号检查 2)禁止类型 3)核心要求 4)推荐类型。如果博主是品牌或官方账号，直接评分为0；否则，先考虑禁止类型(若符合则必为0)，再考虑核心要求和推荐类型(若符合则应为100)。`
  }

  prompt += `\n\n请为每个候选博主输出一个二元评分（0或100），评分理由要明确指出与各要素的关系。

【非常重要】你必须严格按照以下JSON格式返回结果，特别注意所有属性名必须使用双引号，不要使用单引号或无引号的属性名：
{
  "scores": [
    {
      "username": "候选博主用户名",
      "score": 0或100的二元值,
      "reason": "总体评分理由，必须明确参考三要素的匹配情况"
    },
    ...
  ]
}

你的返回内容必须是合法的JSON，不要添加其他文本。确保所有属性名都使用双引号。`

  return prompt
}

const YOUTUBE_SYSTEM_PROMPT_TEMPLATE = (kolDescription: string) => {
  const prompt = `你是一个专业的YouTube频道分析专家。请根据提供的YouTube频道信息（包括频道描述、视频标题和视频封面），评估其是否符合以下要求。

评分标准：
1. 不要给出详细的分数，只需提供二元判断（0或100）。
2. 输出100表示频道符合要求
3. 输出0表示频道不符合要求
4. 如果频道是品牌或官方频道（Business频道、企业频道、公司频道），直接评分为0，无需进一步评估

【重要提示】
评分必须严格根据以下核心要求进行判断，这是决定频道是否符合要求的唯一标准：

【核心要求】- 这是最重要的评判标准，必须满足：
${kolDescription}

这是优先级最高的判断依据，符合这个描述的频道应评为100分，除非明确属于品牌或官方频道。

除了整体评分外，你还需要识别出频道中哪些具体的视频符合核心要求。请分析每个视频的标题、描述和封面，判断该视频是否与核心要求匹配。

请为频道输出一个二元评分（0或100），评分理由要明确指出与核心要求的关系，同时列出符合要求的视频ID列表。

【非常重要】你必须严格按照以下JSON格式返回结果，特别注意所有属性名必须使用双引号，不要使用单引号或无引号的属性名：
{
  "username": "频道标识",
  "score": 0或100的二元值,
  "reason": "总体评分理由，必须明确参考核心要求的匹配情况",
  "videoIds": ["符合要求的视频ID1", "符合要求的视频ID2", ...]
}

你的返回内容必须是合法的JSON，不要添加其他文本。确保所有属性名都使用双引号。`

  return prompt
}

async function analysisUserVeticalSimilarity(
  sourceUser: VisualSimilarityInputWithBio,
): Promise<VerticalAnalysisResult> {
  try {
    const userImages = await fetchUserImages(sourceUser)
    if (userImages.posts.length === 0) {
      console.warn(`博主 ${sourceUser.username} 无有效帖子，无法分析垂类`)
      return {
        success: false,
        error: '缺少有效帖子内容，无法进行分析',
        data: null,
      }
    }

    // 检查是否至少有一个帖子有有效图片
    const hasValidImage = userImages.posts.some(
      (post) => post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType,
    )

    if (!hasValidImage) {
      console.warn(`博主 ${sourceUser.username} 无有效图片，无法进行完整分析`)
      return {
        success: false,
        error: '缺少有效图片内容，无法进行完整分析',
        data: null,
      }
    }

    // 构建系统提示词
    const systemPrompt = `你是一个专业的社交媒体博主垂类分析专家。请分析提供的博主信息（包括简介、帖子正文和帖子封面（视觉内容）），并确定该博主的垂类/内容方向。

请根据博主的内容特点，提供以下信息：
1. 博主的垂类/内容方向描述（kolDescription）
2. 与该垂类相关的关键词列表（allowList）
3. 与该垂类不相关的禁用词列表（banList）

【重要提示】
分析要基于博主的帖子内容、视觉风格和主题。描述需要具体且准确，关键词和禁用词应当有针对性。
确定博主的垂类/内容方向时不能出现具体的人名、城市、国家、品牌、公司、产品等，应该使用通用的内容类别和风格描述。

【非常重要】你必须严格按照下面的JSON格式返回结果，特别注意所有属性名必须使用双引号，不要使用单引号或无引号的属性名：
{
  "kolDescription": "用简洁准确的1-2句话描述该博主的核心垂类/内容方向，包含行业领域和内容特点，便于快速匹配相似博主",
  "allowList": ["关键词1", "关键词2", "关键词3"...],
  "banList": ["禁用词1", "禁用词2", "禁用词3"...]
}`

    const messages: any[] = [{ role: 'system', content: systemPrompt }]

    // 构建用户消息内容
    const userMessageContent: ChatCompletionContentPart[] = []

    userMessageContent.push({
      type: 'text',
      text: `博主信息：\n用户名: ${sourceUser.username}\n简介: ${sourceUser.biography || '无简介'}\n\n最近帖子内容:`,
    })

    // 添加博主的帖子内容和图片
    for (const post of userImages.posts) {
      if (post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType) {
        userMessageContent.push({
          type: 'image_url',
          image_url: {
            url: `data:${post.thumbnail.inlineData.mimeType};base64,${post.thumbnail.inlineData.data}`,
          },
        })
      }
      userMessageContent.push({ type: 'text', text: post.caption || '无正文' })
      userMessageContent.push({ type: 'text', text: '\n---\n' }) // 分隔符
    }

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    console.log(
      `调用模型分析博主 ${sourceUser.username} 的垂类，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
    )

    const completion = await chatCompletionWithModelFailover({
      models: DEFAULT_MODEL_FAILOVER_ORDER,
      messages: [
        ...messages,
        {
          role: 'system',
          content:
            '请确保你返回的JSON格式正确，所有属性名必须使用双引号，不能使用单引号或无引号。JSON必须是有效的，不能包含格式错误。',
        },
      ],
      response_format: { type: 'json_object' },
      // order: ['openRouterApi', 'aihubmix'],
    })

    console.log(`OpenAI API 调用完成。消耗的token: ${completion?.usage?.total_tokens}`)

    const responseContent = completion.choices[0]?.message?.content

    if (!responseContent) {
      console.error('OpenAI completion missing content:', JSON.stringify(completion, null, 2))
      throw new Error('No valid content in OpenAI completion response')
    }

    console.log('尝试解析 OpenAI 返回的 JSON...')
    // 输出JSON字符串的前100个字符
    console.log(`JSON字符串前100字符预览: ${responseContent?.substring(0, 100)}...`)

    try {
      // 使用健壮的JSON解析函数替换直接的JSON.parse
      const parsedResult = JsonParseUtil.parseJsonRobustly<any>(responseContent)

      // 验证返回结果包含必要字段
      if (
        !parsedResult.kolDescription ||
        !Array.isArray(parsedResult.allowList) ||
        !Array.isArray(parsedResult.banList)
      ) {
        console.error('解析后的 JSON 缺少必要字段:', parsedResult)
        throw new Error('AI 返回结果格式错误: 缺少必要字段')
      }

      console.log(
        `成功解析垂类分析结果，kolDescription长度: ${parsedResult.kolDescription.length}, allowList数量: ${parsedResult.allowList.length}, banList数量: ${parsedResult.banList.length}`,
      )

      return {
        success: true,
        data: {
          kolDescription: parsedResult.kolDescription,
          allowList: parsedResult.allowList,
          banList: parsedResult.banList,
        },
      }
    } catch (parseError) {
      console.error('AI 返回结果 JSON 解析失败:', parseError, '原始 JSON 字符串:', responseContent)
      return {
        success: false,
        error: 'AI 分析失败 - 无法解析结果',
        data: null,
      }
    }
  } catch (error) {
    console.error('博主垂类分析过程出错:', error)
    return {
      success: false,
      error: '博主垂类分析失败: ' + (error instanceof Error ? error.message : String(error)),
      data: null,
    }
  }
}

async function analyzeVisualSimilarity(
  targetUsers: VisualSimilarityInput[],
  options: VisualSimilarityOptions,
): Promise<VisualSimilarityOutput[]> {
  try {
    const systemInstruction = SYSTEM_PROMPT_TEMPLATE(options)
    const messages: any[] = [{ role: 'system', content: systemInstruction }]

    const userMessageContent: ChatCompletionContentPart[] = []
    userMessageContent.push({ type: 'text', text: `候选博主信息：` })

    const validTargetUsers: VisualSimilarityInput[] = []
    const invalidTargetUsers: VisualSimilarityOutput[] = []

    // 使用Bluebird并发处理获取用户图片数据
    const processResults = await Bluebird.map(
      targetUsers,
      async (targetUser) => {
        try {
          const userImages = await fetchUserImages(targetUser)

          if (userImages.posts.length === 0) {
            console.warn(`候选博主 ${targetUser.username} 无有效帖子，将跳过分析`)
            return {
              user: targetUser,
              userImages: null,
              valid: false,
              reason: '缺少有效帖子内容，无法进行分析',
            }
          }

          const hasValidImage = userImages.posts.some(
            (post) => post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType,
          )

          if (!hasValidImage) {
            console.warn(`候选博主 ${targetUser.username} 无有效图片，将跳过分析`)
            return {
              user: targetUser,
              userImages: null,
              valid: false,
              reason: '缺少有效图片内容，无法进行视觉分析',
            }
          }

          return {
            user: targetUser,
            userImages,
            valid: true,
          }
        } catch (error) {
          console.error(`处理候选博主 ${targetUser.username} 时出错:`, error)
          return {
            user: targetUser,
            userImages: null,
            valid: false,
            reason: `处理异常: ${error instanceof Error ? error.message : String(error)}`,
          }
        }
      },
      { concurrency: 5 },
    )

    for (const result of processResults) {
      if (result.valid && result.userImages) {
        validTargetUsers.push(result.user)
        userMessageContent.push({
          type: 'text',
          text: `\n博主：${result.user.username}`,
        })

        userMessageContent.push({ type: 'text', text: '\n最近帖子：' })

        for (const post of result.userImages.posts) {
          if (post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType) {
            userMessageContent.push({
              type: 'image_url',
              image_url: {
                url: `data:${post.thumbnail.inlineData.mimeType};base64,${post.thumbnail.inlineData.data}`,
              },
            })
          } else {
            console.warn(`候选博主 ${result.user.username} 的帖子缺少有效缩略图数据`)
          }

          userMessageContent.push({ type: 'text', text: post.caption || '无正文' })
        }

        userMessageContent.push({ type: 'text', text: '\n---' })
      } else if (!result.valid) {
        invalidTargetUsers.push({
          username: result.user.username,
          score: 0,
          reason: result.reason || '无效内容',
        })
      }
    }

    if (validTargetUsers.length === 0) {
      console.warn('所有候选博主都缺少有效内容，无法进行分析')
      return targetUsers.map((user) => ({
        username: user.username,
        score: 0,
        reason: '无有效内容进行分析',
      }))
    }

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    console.log(
      `调用模型分析 ${validTargetUsers.length} 个有效候选博主，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
    )
    try {
      const completion = await chatCompletionWithModelFailover({
        models: DEFAULT_MODEL_FAILOVER_ORDER,
        messages: [
          ...messages,
          {
            role: 'system',
            content:
              '请确保你返回的JSON格式正确，所有属性名必须使用双引号，不能使用单引号或无引号。JSON必须是有效的，不能包含格式错误。',
          },
        ],
        response_format: { type: 'json_object' },
        // order: ['openRouterApi', 'aihubmix'],
      })

      console.log(`OpenAI API 调用完成。消耗的token: ${completion?.usage?.total_tokens}`)

      const responseContent = completion.choices[0]?.message?.content

      if (!responseContent) {
        console.error('OpenAI completion missing content:', JSON.stringify(completion, null, 2))
        throw new Error('No valid content in OpenAI completion response')
      }

      console.log('尝试解析 OpenAI 返回的 JSON...')
      // 输出JSON字符串的前100个字符
      console.log(`JSON字符串前100字符预览: ${responseContent?.substring(0, 100)}...`)

      try {
        // 使用健壮的JSON解析函数替换直接的JSON.parse
        const parsedResult = JsonParseUtil.parseJsonRobustly<any>(responseContent)
        if (!parsedResult.scores || !Array.isArray(parsedResult.scores)) {
          console.error("解析后的 JSON 缺少 'scores' 数组:", parsedResult)
          throw new Error('AI 返回结果格式错误: 缺少 scores 数组')
        }

        console.log(`成功解析 ${parsedResult.scores.length} 个评分结果。`)

        const validResults = parsedResult.scores.map((score: any) => {
          if (
            !score ||
            typeof score.username !== 'string' ||
            typeof score.score !== 'number' ||
            typeof score.reason !== 'string'
          ) {
            console.error('AI 返回结果中的 score 对象缺少必要字段或类型错误:', score)
            throw new Error('AI 返回结果中的 score 对象缺少必要字段或类型错误')
          }
          if (score.score < 0 || score.score > 100) {
            console.warn(`AI 返回的分数 ${score.score} 超出 0-100 范围，将修正为 0 或 100`, score)
            score.score = Math.max(0, Math.min(100, score.score))
          }
          console.log(`${score.username} 的评分: ${score.score}理由:${score.reason}`)
          return {
            username: score.username,
            score: score.score,
            reason: score.reason,
          }
        })

        // 创建用户名映射表以便后续查找
        const resultMap = new Map(
          validResults.map((item: VisualSimilarityOutput) => [item.username, item]),
        )

        const results = targetUsers.map((user) => {
          if (resultMap.has(user.username)) {
            return resultMap.get(user.username)!
          }

          const invalidResult = invalidTargetUsers.find((item) => item.username === user.username)
          if (invalidResult) {
            return invalidResult
          }

          return {
            username: user.username,
            score: 0,
            reason: 'AI分析未返回结果',
          }
        })

        return results as VisualSimilarityOutput[]
      } catch (parseError) {
        console.error(
          'AI 返回结果 JSON 解析失败:',
          parseError,
          '原始 JSON 字符串:',
          responseContent,
        )
        return targetUsers.map((user) => ({
          username: user.username,
          score: 0,
          reason: 'AI 分析失败 - 无法解析结果',
        }))
      }
    } catch (apiError) {
      console.error('OpenAI API 调用失败:', apiError)
      throw apiError
    }
  } catch (error) {
    console.error('AI 分析过程出错:', error)
    return targetUsers.map((user) => ({
      username: user.username,
      score: 0,
      reason: 'AI 分析失败',
    }))
  }
}

interface VideoCoverAnalysisResult {
  tags: string[]
  searchTerms: string[]
}
async function analyzeVideoCovers(
  videos: Array<{ cover: string; title: string }>,
): Promise<VideoCoverAnalysisResult> {
  try {
    if (videos.length === 0) {
      return {
        tags: [],
        searchTerms: [],
      }
    }

    const systemPrompt = `你是一位专业的TikTok内容分析专家。你的任务是分析视频封面和标题，生成最相关的内容标签和搜索词。

分析步骤:
1. 首先分析视频正文的语言
2. 根据正文语言确定标签和搜索词的语言
3. 分析视频封面和标题的内容
4. 生成与正文语言一致的标签和搜索词

请遵循以下规则：
1. 综合分析视频封面的视觉元素和视频标题，推断视频的主题和内容类型
2. 生成的标签和搜索词应该具体且相关，避免过于宽泛
3. 不要使用普通的热门标签（如：trending, viral, fyp等）
4. 标签和搜索词应该使用与视频正文完全相同的语言
5. 一共生成15个最符合视频内容的标签和15个搜索词
6. 标签和搜索词应该有一定的搜索量，但也要足够精准

【非常重要】你必须严格按照以下JSON格式返回结果：
{
  "tags": ["标签1", "标签2", "标签3", "标签4", "标签5", "标签6", "标签7", "标签8", "标签9", "标签10", "标签11", "标签12", "标签13", "标签14", "标签15"],
  "searchTerms": ["搜索词1", "搜索词2", "搜索词3", "搜索词4", "搜索词5", "搜索词6", "搜索词7", "搜索词8", "搜索词9", "搜索词10", "搜索词11", "搜索词12", "搜索词13", "搜索词14", "搜索词15"]
}

请确保返回的是单个对象而不是数组，所有属性名和字符串必须使用双引号，保证JSON格式完全正确。`

    const messages: any[] = [{ role: 'system', content: systemPrompt }]

    const userMessageContent: ChatCompletionContentPart[] = [
      {
        type: 'text',
        text: '我会发给你一些视频的封面和标题，请根据这些信息生成标签和搜索词：\n',
      },
    ]

    for (const [index, video] of videos.entries()) {
      if (video.cover) {
        const thumbnail = await ImageUtil.fetchImageAsBase64(video.cover)
        userMessageContent.push({
          type: 'text',
          text: `\n视频 ${index + 1}:\n标题: ${video.title}\n封面图片:`,
        })
        userMessageContent.push({
          type: 'image_url',
          image_url: {
            url: `data:${thumbnail.inlineData.mimeType};base64,${thumbnail.inlineData.data}`,
          },
        })
      }
    }

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    const result = await retryUtil.retry(
      async () => {
        const response = await chatCompletionWithModelFailover({
          models: DEFAULT_MODEL_FAILOVER_ORDER,
          messages: [
            ...messages,
            {
              role: 'system',
              content:
                '请确保你返回的JSON格式正确，必须是单个对象而不是数组，格式为：{"tags":["标签1","标签2","标签3"...],"searchTerms":["搜索词1","搜索词2","搜索词3"...]}',
            },
          ],
          response_format: { type: 'json_object' },
          temperature: 0.3,
          max_tokens: 1000,
        })

        const responseContent = response.choices[0]?.message?.content
        if (!responseContent) {
          throw new Error('OpenAI 返回结果为空')
        }

        const parsedResult = JsonParseUtil.parseJsonRobustly<any>(responseContent)
        if (!parsedResult || typeof parsedResult !== 'object') {
          throw new Error('解析结果不是有效的对象')
        }

        let tags: string[] = []
        let searchTerms: string[] = []

        tags = Array.isArray(parsedResult.tags) ? parsedResult.tags : []
        searchTerms = Array.isArray(parsedResult.searchTerms) ? parsedResult.searchTerms : []

        const validTags = tags
          .filter((tag: any) => typeof tag === 'string' && tag.trim().length > 0)
          .slice(0, 10)
        const validSearchTerms = searchTerms
          .filter((term: any) => typeof term === 'string' && term.trim().length > 0)
          .slice(0, 10)

        return {
          tags: validTags,
          searchTerms: validSearchTerms,
        }
      },
      3, // 重试3次
      2000, // 初始延迟2秒
      2, // 退避系数2
      (error, retryCount, nextDelay) => {
        console.warn(`视频封面分析失败，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试:`, error)
      },
    )

    return result
  } catch (error) {
    console.error('视频封面分析失败:', error)
    return {
      tags: [],
      searchTerms: [],
    }
  }
}

async function generateAuthorKeyWords(
  videos: Array<{ cover: string; title: string }>,
): Promise<string[]> {
  try {
    if (videos.length === 0) {
      throw new Error('没有提供视频数据用于生成关键词')
    }

    // 检查是否有有效的视频封面
    const validVideos = videos.filter((video) => video.cover)
    if (validVideos.length === 0) {
      throw new Error('没有找到有效的视频封面用于视觉分析')
    }

    console.log(`开始基于视觉分析生成关键词，使用 ${validVideos.length} 个视频封面...`)

    // 构建系统提示词
    const systemPrompt = `你是一位专业的社交媒体博主内容分析专家。你的任务是分析视频封面和标题，生成最相关的搜索关键词组合。

分析步骤:
1. 首先分析视频正文的语言
2. 根据正文语言确定标签和搜索词的语言
3. 分析视频封面的视觉元素和主题
4. 结合视频标题，理解内容的核心主题
5. 生成10组高质量的搜索关键词组合

请遵循以下规则：
1. 每组关键词应该是2-5个词的组合，形成有意义的搜索短语
2. 关键词应该与视频内容高度相关，能够帮助找到类似的创作者
3. 不要使用普通的热门标签（如：trending, viral, fyp等）
4. 关键词应该足够具体，能够定位特定的内容类型和风格
5. 考虑视频的视觉风格、主题、情感基调等多个维度
6. 生成的关键词应使用与正文语言一致的语言

【非常重要】你必须严格按照以下JSON格式返回结果：
{
 "keywords":["与正文语言一致的关键词组合1","与正文语言一致的关键词组合2","与正文语言一致的关键词组合3"...]
}
请确保返回的是有效的JSON格式，所有属性名和字符串必须使用双引号。`

    const messages: any[] = [{ role: 'system', content: systemPrompt }]

    // 构建用户消息内容
    const userMessageContent: any[] = []

    userMessageContent.push({
      type: 'text',
      text: `请分析以下视频封面和标题，生成相关的搜索关键词组合：`,
    })

    for (const [index, video] of validVideos.entries()) {
      try {
        const thumbnail = await ImageUtil.fetchImageAsBase64(video.cover)
        userMessageContent.push({
          type: 'text',
          text: `\n视频 ${index + 1}:\n标题: ${video.title || '无标题'}\n`,
        })

        if (thumbnail?.inlineData?.data && thumbnail?.inlineData?.mimeType) {
          userMessageContent.push({
            type: 'image_url',
            image_url: {
              url: `data:${thumbnail.inlineData.mimeType};base64,${thumbnail.inlineData.data}`,
            },
          })
        }
      } catch (error) {
        console.warn(`获取视频封面失败: ${video.cover}`, error)
      }
    }

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    console.log(
      `调用模型分析视频封面生成关键词，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
    )

    const result = await retryUtil.retry(
      async () => {
        const completion = await chatCompletionWithModelFailover({
          models: DEFAULT_MODEL_FAILOVER_ORDER,
          messages: [
            ...messages,
            {
              role: 'system',
              content:
                '请确保你返回的JSON格式正确，所有属性名必须使用双引号，不能使用单引号或无引号。',
            },
          ],
          response_format: { type: 'json_object' },
          temperature: 0.7,
          max_tokens: 2000,
        })

        console.log(
          `OpenAI API 调用完成。消耗的token: ${completion?.usage?.total_tokens || '未知'}`,
        )

        const responseContent = completion.choices[0]?.message?.content

        if (!responseContent) {
          throw new Error('OpenAI返回结果为空')
        }

        const parsedResult = JsonParseUtil.parseJsonRobustly<{ keywords: string[] }>(
          responseContent,
        )
        if (!parsedResult.keywords || !Array.isArray(parsedResult.keywords)) {
          throw new Error('解析后的JSON缺少keywords数组')
        }

        const keywords = parsedResult.keywords.filter(Boolean)
        console.log(`成功生成${keywords.length}个搜索关键词`)

        return keywords
      },
      3, // 重试3次
      2000, // 初始延迟2秒
      2, // 退避系数2
      (error, retryCount, nextDelay) => {
        console.warn(`生成关键词失败，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试:`, error)
      },
    )

    console.log(`获取到关键词:${result}`)

    return result
  } catch (error) {
    console.error('生成作者关键词失败:', error)
    return []
  }
}

async function analysisUserKolDescription(
  sourceUser: VisualSimilarityInputWithBio,
  language: string = 'zh-CN',
): Promise<string> {
  try {
    const userImages = await fetchUserImages(sourceUser)
    if (userImages.posts.length === 0) {
      throw new Error('缺少有效帖子内容，无法进行分析')
    }

    const hasValidImage = userImages.posts.some(
      (post) => post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType,
    )
    if (!hasValidImage) {
      throw new Error('缺少有效图片内容，无法进行完整分析')
    }

    // 构建系统提示词
    const systemPrompt = `你是一位顶级的社交媒体生态分析师，尤其擅长精准识别和剖析各类博主的垂类定位与内容特色。
    你的任务是：基于提供的博主信息（包括但不限于：博主简介、近期帖子正文、帖子封面及视觉元素），深度分析并凝练出该博主的核心垂类、内容风格及其独特性。
    
    【分析框架与指引】
    请在分析时重点思考以下方面，以确保最终描述的专业性和准确性：
    1.  **核心领域 (Domain Focus):** 博主主要聚焦于哪个或哪些具体的行业或兴趣领域？（例如：美妆护肤、时尚穿搭、美食探店与制作、旅行攻略与体验、科技产品测评、健身与健康生活、亲子育儿、知识科普、萌宠日常、情感解读、财经投资等）
    2.  **内容风格 (Content Style):** 博主内容的整体调性与呈现风格是怎样的？（例如：专业严谨、幽默风趣、治愈温馨、Vlog纪实、教程干货、创意脑洞、高级质感、生活化接地气、犀利点评、深度解析等）
    3.  **视觉呈现 (Visual Style):** 从帖子封面和整体视觉内容来看，其视觉风格有何突出特点？（例如：色彩鲜明、简约高级、复古怀旧、清新自然、暗黑系、ins风、国风等）
    4.  **内容特点/独特性 (Key Features / Unique Selling Proposition):** 该博主的内容有哪些显著的、反复出现的特点或元素？（例如：专注于特定细分品类如甜点、独特的拍摄手法、频繁出现特定人物如儿童、强调某种生活方式或氛围如家庭友好等）
    
    【重要提示】
    1.分析要基于博主的帖子内容、视觉风格和主题。描述需要具体且准确且应有针对性。
    2.确定博主的垂类/内容方向时不能出现具体的人名、城市、国家、品牌、公司、产品等，应该使用通用的内容类别和风格描述。
    
    【非常重要】你必须严格按照下面的JSON格式返回结果且输出的语言必须按照${language}，特别注意所有属性名必须使用双引号，不要使用单引号或无引号的属性名：
    {
      "kolDescription": "请以 'Ideal type of creator: ' (注意冒号后换行) 作为固定开头。接着，用一句话概括博主的核心垂类定位并且使用${language}语言,例如:'一位专注于分享家居装饰技巧、生活方式好物、以及美妆护肤经验的博主'"
    }
    `
    const messages: any[] = [{ role: 'system', content: systemPrompt }]

    const userMessageContent: ChatCompletionContentPart[] = []
    userMessageContent.push({
      type: 'text',
      text: `博主信息：\n用户名: ${sourceUser.username}\n简介: ${sourceUser.biography || '无简介'}\n\n最近帖子内容:`,
    })

    for (const post of userImages.posts) {
      if (post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType) {
        userMessageContent.push({
          type: 'image_url',
          image_url: {
            url: `data:${post.thumbnail.inlineData.mimeType};base64,${post.thumbnail.inlineData.data}`,
          },
        })
      }
      userMessageContent.push({ type: 'text', text: post.caption || '无正文' })
      userMessageContent.push({ type: 'text', text: '\n---\n' }) // 分隔符
    }

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    let completion
    try {
      console.log(
        `调用模型分析博主 ${sourceUser.username} 的描述，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
      )
      completion = await chatCompletionWithModelFailover({
        models: DEFAULT_MODEL_FAILOVER_ORDER,
        messages: [
          ...messages,
          {
            role: 'system',
            content:
              '请确保你返回的JSON格式正确，所有属性名必须使用双引号，不能使用单引号或无引号。JSON必须是有效的，不能包含格式错误。',
          },
        ],
        response_format: { type: 'json_object' },
      })
    } catch (error) {
      console.error('analysisUserKolDescription error:', error)
      Sentry.captureException(error)
      throw new Error('analysis User Kol Description error, please try again later')
    }

    const responseContent = completion.choices[0]?.message?.content

    if (!responseContent) {
      throw new Error('No valid content in OpenAI completion response')
    }

    const parsedResult = JsonParseUtil.parseJsonRobustly<{ kolDescription: string }>(
      responseContent,
    )

    if (!parsedResult.kolDescription) {
      throw new Error('AI 返回结果格式错误: 缺少必要字段')
    }

    return parsedResult.kolDescription
  } catch (error) {
    console.error('analysisUserKolDescription error:', error)
    throw new Error('analysis User Kol Description error, please try again later')
  }
}

async function filterOfficialAccount(usernames: string[]): Promise<string[]> {
  try {
    if (!usernames?.length) {
      return []
    }

    console.log(`开始分析 ${usernames.length} 个用户名，识别官方账号和品牌账号...`)

    // 构建系统提示词
    const systemPrompt = `你是一位专业的社交媒体账号分析专家，特别擅长通过用户名识别官方账号和品牌账号。

任务：分析提供的用户名列表，识别其中可能是官方账号、品牌账号、机构账号或非个人创作者账号的用户名。

判断标准：
1. 用户名中包含官方、official、brand、studio、team、agency等明显的官方或品牌标识词
2. 用户名是知名品牌、公司、产品、媒体、机构的名称或缩写
3. 用户名格式符合官方账号的命名规则（如xx_official、xx.brand、xx_studio等）
4. 用户名明显不是个人名字，而是组织、团队或品牌名称
5. 用户名包含inc、ltd、co、media、group等商业实体相关词汇
6. 用户名是明显的产品名称或服务名称

【输出要求】
- 仅返回被识别为官方账号或品牌账号的用户名列表
- 如果无法确定，不要将用户名包含在结果中
- 只分析用户名本身，不要考虑其他因素

【非常重要】你必须严格按照以下JSON格式返回结果：
{
  "officialAccounts": ["官方账号1", "官方账号2", "官方账号3", ...]
}

请确保返回的是有效的JSON格式，所有属性名和字符串必须使用双引号。`

    const messages: any[] = [
      { role: 'system', content: systemPrompt },
      {
        role: 'user',
        content: `请分析以下用户名列表，识别其中的官方账号和品牌账号：\n\n${usernames.join('\n')}`,
      },
      {
        role: 'system',
        content: '请确保你返回的JSON格式正确，所有属性名必须使用双引号，不能使用单引号或无引号。',
      },
    ]

    console.log(
      `调用模型分析用户名识别官方账号，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
    )

    const result = await retryUtil.retry(
      async () => {
        const completion = await chatCompletionWithModelFailover({
          models: DEFAULT_MODEL_FAILOVER_ORDER,
          messages,
          response_format: { type: 'json_object' },
          temperature: 0.3,
          max_tokens: 1000,
        })

        console.log(
          `OpenAI API 调用完成。消耗的token: ${completion?.usage?.total_tokens || '未知'}`,
        )

        const responseContent = completion.choices[0]?.message?.content

        if (!responseContent) {
          throw new Error('OpenAI返回结果为空')
        }

        const parsedResult = JsonParseUtil.parseJsonRobustly<{ officialAccounts: string[] }>(
          responseContent,
        )

        if (!parsedResult.officialAccounts || !Array.isArray(parsedResult.officialAccounts)) {
          throw new Error('解析后的JSON缺少officialAccounts数组')
        }

        const officialAccounts = parsedResult.officialAccounts.filter(Boolean)
        console.log(`成功识别出${officialAccounts.length}个官方/品牌账号`)

        return officialAccounts
      },
      3, // 重试3次
      2000, // 初始延迟2秒
      2, // 退避系数2
      (error, retryCount, nextDelay) => {
        console.warn(`识别官方账号失败，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试:`, error)
      },
    )

    return result
  } catch (error) {
    console.error('识别官方账号失败:', error)
    return []
  }
}

async function analyzeYouTubeChannelSimilarity(
  channel: any, // Channel对象
  kolDescription: string,
): Promise<YouTubeChannelSimilarityOutput> {
  try {
    const systemInstruction = YOUTUBE_SYSTEM_PROMPT_TEMPLATE(kolDescription)
    const messages: any[] = [{ role: 'system', content: systemInstruction }]

    const userMessageContent: ChatCompletionContentPart[] = []
    userMessageContent.push({
      type: 'text',
      text: `YouTube频道信息：\n频道ID: ${channel.channelId}\n频道名称: ${channel.title}\n频道描述: ${channel.description || '无描述'}\n\n前6个视频内容:`,
    })

    // 获取前6个视频
    const videos = channel.videos?.slice(0, 6) || []

    if (videos.length === 0) {
      console.warn(`频道 ${channel.channelId} 没有视频数据`)
      return {
        username: channel.channelId,
        score: 0,
        reason: '频道没有视频数据，无法进行分析',
        videoIds: [],
      }
    }

    // 处理每个视频的信息和封面
    const processResults = await Bluebird.map(
      videos,
      async (video: any) => {
        try {
          let imageUrl = ''
          if (video.thumbnail && Array.isArray(video.thumbnail) && video.thumbnail.length > 0) {
            imageUrl = video.thumbnail[0]?.url || ''
          }

          if (imageUrl) {
            try {
              const thumbnail = await ImageUtil.fetchImageAsBase64(imageUrl, 2)
              if (thumbnail.inlineData.data) {
                return {
                  video,
                  thumbnail,
                  valid: true,
                }
              }
            } catch (error) {
              console.warn(`获取视频 ${video.videoId} 封面失败:`, error)
            }
          }

          return {
            video,
            thumbnail: null,
            valid: false,
          }
        } catch (error) {
          console.error(`处理视频 ${video.videoId} 时出错:`, error)
          return {
            video,
            thumbnail: null,
            valid: false,
          }
        }
      },
      { concurrency: 6 },
    )

    // 使用map并发拼接视频信息到消息内容
    const videoContentParts: ChatCompletionContentPart[] = processResults.flatMap(
      ({ video, thumbnail, valid }) => {
        const parts: ChatCompletionContentPart[] = [
          {
            type: 'text',
            text: `\n视频ID: ${video.videoId}\n标题: ${video.title}\n描述: ${video.description || '无描述'}\n发布时间: ${video.publishedTimeText}\n观看次数: ${video.viewCount}\n`,
          },
        ]

        if (valid && thumbnail) {
          parts.push({
            type: 'image_url',
            image_url: {
              url: `data:${thumbnail.inlineData.mimeType};base64,${thumbnail.inlineData.data}`,
            },
          })
        } else {
          parts.push({
            type: 'text',
            text: '(无法获取视频封面)',
          })
        }

        parts.push({ type: 'text', text: '\n---\n' })
        return parts
      },
    )

    userMessageContent.push(...videoContentParts)

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    console.log(
      `调用模型分析YouTube频道 ${channel.channelId}，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
    )

    const completion = await chatCompletionWithModelFailover({
      models: DEFAULT_MODEL_FAILOVER_ORDER,
      messages: [
        ...messages,
        {
          role: 'system',
          content:
            '请确保你返回的JSON格式正确，所有属性名必须使用双引号，不能使用单引号或无引号。JSON必须是有效的，不能包含格式错误。',
        },
      ],
      response_format: { type: 'json_object' },
    })

    console.log(`OpenAI API 调用完成。消耗的token: ${completion?.usage?.total_tokens}`)

    const responseContent = completion.choices[0]?.message?.content

    if (!responseContent) {
      console.error('OpenAI completion missing content:', JSON.stringify(completion, null, 2))
      throw new Error('No valid content in OpenAI completion response')
    }

    console.log('尝试解析 OpenAI 返回的 JSON...')
    console.log(`JSON字符串前100字符预览: ${responseContent?.substring(0, 100)}...`)

    try {
      const parsedResult = JsonParseUtil.parseJsonRobustly<any>(responseContent)

      if (
        !parsedResult.username ||
        typeof parsedResult.score !== 'number' ||
        !parsedResult.reason ||
        !Array.isArray(parsedResult.videoIds)
      ) {
        console.error('解析后的 JSON 缺少必要字段:', parsedResult)
        throw new Error('AI 返回结果格式错误: 缺少必要字段')
      }

      // 验证分数范围
      if (parsedResult.score < 0 || parsedResult.score > 100) {
        console.warn(`AI 返回的分数 ${parsedResult.score} 超出 0-100 范围，将修正为 0 或 100`)
        parsedResult.score = Math.max(0, Math.min(100, parsedResult.score))
      }

      console.log(
        `YouTube频道 ${channel.channelId} 的评分: ${parsedResult.score}, 理由: ${parsedResult.reason}, 符合要求的视频数: ${parsedResult.videoIds.length}`,
      )

      return {
        username: parsedResult.username,
        score: parsedResult.score,
        reason: parsedResult.reason,
        videoIds: parsedResult.videoIds,
      }
    } catch (parseError) {
      console.error('AI 返回结果 JSON 解析失败:', parseError, '原始 JSON 字符串:', responseContent)
      return {
        username: channel.channelId,
        score: 0,
        reason: 'AI 分析失败 - 无法解析结果',
        videoIds: [],
      }
    }
  } catch (error) {
    console.error('YouTube频道AI分析过程出错:', error)
    return {
      username: channel.channelId,
      score: 0,
      reason: 'AI 分析失败',
      videoIds: [],
    }
  }
}

interface BatchBloggerDimensionsAnalysisResult {
  bloggers: BloggerDimensionsAnalysisResult[]
}

async function analyzeBloggerDimensionsBatch(
  users: VisualSimilarityInputWithBio[],
): Promise<BloggerDimensionsAnalysisResultWithFlag[]> {
  if (users.length === 0) {
    return []
  }

  try {
    const userImagePromises = users.map(async (user) => {
      try {
        const userImages = await fetchUserImages(user)
        return { user, userImages, valid: userImages.posts.length > 0 }
      } catch (error) {
        console.error(`获取博主 ${user.username} 的图片数据失败:`, error)
        return { user, userImages: null, valid: false }
      }
    })

    const usersWithImages = await Promise.allSettled(userImagePromises)

    const validUsers = usersWithImages
      .filter(
        (
          result,
        ): result is PromiseFulfilledResult<{
          user: VisualSimilarityInputWithBio
          userImages: UserImageData | null
          valid: boolean
        }> => result.status === 'fulfilled' && result.value.valid,
      )
      .map((result) => result.value)
      .filter(
        (item) =>
          item.userImages &&
          item.userImages.posts.some(
            (post: { thumbnail?: ImagePart; caption: string }) =>
              post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType,
          ),
      )

    if (validUsers.length === 0) {
      console.warn('批量分析中没有有效的博主数据')
      return []
    }

    const systemPrompt = BloggerDimensionsAnalysisPrompt()
    const messages: any[] = [{ role: 'system', content: systemPrompt }]
    const userMessageContent: ChatCompletionContentPart[] = []
    userMessageContent.push({
      type: 'text',
      text: `请分析以下${validUsers.length}位博主的核心定位和视频内容，返回JSON格式。\n\n`,
    })

    for (const { user, userImages } of validUsers) {
      if (!userImages) continue
      userMessageContent.push({
        type: 'text',
        text:
          `\n===== 博主 ${user.username} =====\n` +
          (user.biography ? `简介: ${user.biography}\n` : '') +
          `最近帖子内容:\n`,
      })
      for (const post of userImages.posts) {
        if (post.thumbnail?.inlineData?.data && post.thumbnail?.inlineData?.mimeType) {
          userMessageContent.push({
            type: 'image_url',
            image_url: {
              url: `data:${post.thumbnail.inlineData.mimeType};base64,${post.thumbnail.inlineData.data}`,
            },
          })
        }
        userMessageContent.push({ type: 'text', text: `帖子正文: ${post.caption || '无正文'}` })
        userMessageContent.push({ type: 'text', text: '\n---\n' })
      }
    }

    messages.push({
      role: 'user',
      content: userMessageContent,
    })

    console.log(
      `调用模型批量分析 ${validUsers.length} 位博主的核心定位和视频内容，尝试顺序：${DEFAULT_MODEL_FAILOVER_ORDER.join(' -> ')}...`,
    )

    const finalResults = await retryUtil.retry(
      async () => {
        const completion = await chatCompletionWithModelFailover({
          models: DEFAULT_MODEL_FAILOVER_ORDER,
          messages: [
            ...messages,
            {
              role: 'system',
              content:
                '【JSON格式要求】\n' +
                '1. 返回有效的JSON对象\n' +
                '2. 所有属性名和字符串值使用双引号\n' +
                '4. 所有输出内容必须与博主帖子正文语言一致\n\n' +
                '请严格按照以下JSON结构返回：\n' +
                '{\n' +
                '  "bloggers": [\n' +
                '    {\n' +
                '      "bloggerID": "用户名",\n' +
                '      "corePositioning": "核心定位描述",\n' +
                '      "videoAnalysis": ["视频描述数组"],\n' +
                '      "language": "语言"\n' +
                '    }\n' +
                '  ]\n' +
                '}',
            },
          ],
          response_format: { type: 'json_object' },
          temperature: 0.2,
          max_tokens: 8000,
        })

        console.log(`OpenAI API 批量调用完成。消耗的token: ${completion?.usage?.total_tokens}`)

        const responseContent = completion.choices[0]?.message?.content

        if (!responseContent) {
          console.error('OpenAI completion missing content:', JSON.stringify(completion, null, 2))
          throw new Error('No valid content in OpenAI completion response')
        }

        const parsedResult =
          JsonParseUtil.parseJsonRobustly<BatchBloggerDimensionsAnalysisResult>(responseContent)

        if (!parsedResult.bloggers || !Array.isArray(parsedResult.bloggers)) {
          console.error('解析后的 JSON 缺少 bloggers 数组:', parsedResult)
          throw new Error('AI 返回结果格式错误: 缺少 bloggers 数组')
        }

        const validResults = parsedResult.bloggers
          .map((blogger) => {
            if (!blogger.bloggerID || !blogger.corePositioning) {
              console.warn(`博主 ${blogger.bloggerID || '未知'} 的分析结果缺少必要字段`)
              return null
            }

            return blogger
          })
          .filter(Boolean) as BloggerDimensionsAnalysisResult[]

        console.log(`批量分析完成，成功分析 ${validResults.length}/${users.length} 位博主`)

        const resultMap = new Map(
          validResults.map((result) => [
            result.bloggerID,
            {
              ...result,
              isAnalyzedSuccessfully: true,
            },
          ]),
        )

        const finalResults = users.map((user) => {
          if (resultMap.has(user.username)) {
            const result = resultMap.get(user.username)!
            console.log(`博主 ${user.username} 的核心定位: ${result.corePositioning}`)
            return result
          }
          console.warn(`博主 ${user.username} 分析失败，返回默认值`)
          return {
            bloggerID: user.username,
            corePositioning: '',
            videoAnalysis: [],
            language: '',
            isAnalyzedSuccessfully: false,
          }
        })

        return finalResults
      },
      3, // 重试3次
      3000, // 初始延迟3秒
      2, // 退避系数2
      (error, retryCount, nextDelay) => {
        console.warn(
          `博主批量分析失败（第 ${retryCount} 次重试），将在 ${nextDelay}ms 后重试。错误信息:`,
          error instanceof Error ? error.message : error,
        )
        if (error instanceof Error && error.message.includes('JSON')) {
          console.error('JSON解析错误详情:', error)
        }
      },
    )

    return finalResults
  } catch (error) {
    console.error('博主批量分析过程出错（所有重试都失败）:', error)
    return users.map((user) => ({
      bloggerID: user.username,
      corePositioning: '',
      videoAnalysis: [],
      language: '',
      isAnalyzedSuccessfully: false,
    }))
  }
}

export const analyzeVisualSimilarityService = {
  analyzeVisualSimilarity,
  analyzeYouTubeChannelSimilarity,
  analysisUserVeticalSimilarity,
  analyzeVideoCovers,
  analysisUserKolDescription,
  generateAuthorKeyWords,
  filterOfficialAccount,
  analyzeBloggerDimensionsBatch,
}
