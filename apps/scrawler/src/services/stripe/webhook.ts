import { getPaymentPlanByPriceId, mapStripeStatusToPrismaStatus } from '@/config/stripe/plans'
import { SubscriptionStatus, TransitionStatus, prisma } from '@repo/database'
import dayjs from 'dayjs'
import { Stripe } from 'stripe'
import { stripeClient } from './client'
import { processOneTimeSubscription, processUserMembershipForSubscription } from './membership'
import { immediatelyCancelSubscription } from './subscription'
// handle stripe webhook event
export async function handleWebhookEvent(event: Stripe.Event) {
  try {
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session)
        break

      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded': // only in checkout session pay en
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      default:
        console.log(`Unhandled Stripe Event: ${event.type} [${event.id}]`)
        break
    }
  } catch (error) {
    console.error(
      `Error processing Stripe Event ${event.type} [${event.id}]: ${error instanceof Error ? error.message : String(error)}`,
    )
  }
}

// handle checkout.session.completed event
export async function handleCheckoutSessionCompleted(
  session: Stripe.Checkout.Session,
): Promise<void> {
  if (!session.client_reference_id) {
    console.log('missing userId, cannot handle checkout.session.completed event')
    return
  }

  if (!session.metadata) {
    console.log('missing metadata, cannot handle checkout.session.completed event')
    return
  }

  const userId = session.client_reference_id || session.metadata?.userId

  if (!userId) {
    console.log('missing userId, cannot handle checkout.session.completed event')
    return
  }

  const { priceId } = session.metadata

  if (!priceId) {
    console.log('missing priceId, cannot handle checkout.session.completed event')
    return
  }

  // get config
  const paymentPlan = getPaymentPlanByPriceId(priceId)

  if (!paymentPlan) {
    console.log('missing paymentPlan, cannot handle checkout.session.completed event')
    return
  }

  switch (session.mode) {
    // one time payment
    case 'payment':
      try {
        const paymentId = `payment_${session.payment_intent}`
        const existingPayment = await prisma.stripeSubscription.findUnique({
          where: { stripeSubscriptionId: paymentId },
        })

        if (existingPayment) {
          console.log(
            `payment event ${session.id} has been processed, skip handling. paymentId: ${paymentId}`,
          )
          return
        }

        let actualPaymentMethod = 'unknown'
        if (session.payment_intent) {
          try {
            const paymentIntent = await stripeClient.paymentIntents.retrieve(
              session.payment_intent as string,
            )
            actualPaymentMethod = paymentIntent.payment_method_types?.[0] || 'unknown'
          } catch (error) {
            console.error(`获取支付意向详情失败: ${error}`)
          }
        }

        const periodStart = dayjs.utc().toDate()
        const periodEnd = dayjs
          .utc(periodStart)
          .add(paymentPlan.validityPeriod, paymentPlan.validityUnit)
          .toDate()

        await prisma.stripeSubscription.create({
          data: {
            userId,
            stripeSubscriptionId: paymentId, // paymentId replace with stripeSubscriptionId
            status: SubscriptionStatus.ACTIVE,
            planType: paymentPlan.type,
            priceId: priceId,
            isOneTimePayment: true,
            paymentScheme: paymentPlan.scheme,
            paymentMethod: actualPaymentMethod,
            currency: paymentPlan.currency || session.currency?.toUpperCase(),
            currentPeriodStart: periodStart,
            currentPeriodEnd: periodEnd,
          },
        })

        const result = await processOneTimeSubscription(userId, priceId, 'checkout_completed')

        if (result) {
          console.log(
            `一次性支付完成事件处理成功，用户ID: ${userId}, 产品类型: ${paymentPlan.type}, 金额: ${paymentPlan.amount}`,
          )
        } else {
          console.error(`一次性支付会员处理失败，用户ID: ${userId}, 产品类型: ${paymentPlan.type}`)
        }
      } catch (error) {
        console.error('处理一次性支付完成事件失败:', error)
        throw error
      }
      break

    case 'subscription':
      // subscription payment
      try {
        console.log(`处理订阅结账完成事件，用户ID: ${userId}, 会话ID: ${session.id}`)
        const subscriptionId = session.subscription as string

        if (!subscriptionId) {
          console.log(`订阅结账完成事件缺少subscription ID，用户ID: ${userId}`)
          return
        }

        const existingSubscription = await prisma.stripeSubscription.findUnique({
          where: { stripeSubscriptionId: subscriptionId },
        })

        if (existingSubscription) {
          console.log(`订阅事件 ${session.id} 已被处理过，跳过处理。订阅ID: ${subscriptionId}`)
          return
        }

        const subscription = await stripeClient.subscriptions.retrieve(subscriptionId)

        const subscriptionItem = subscription.items.data[0]
        const startTimestamp = subscriptionItem.current_period_start
        const endTimestamp = subscriptionItem.current_period_end
        const canceledTimestamp = subscription.canceled_at || null

        const periodStart = dayjs.utc(startTimestamp * 1000).toDate()
        const periodEnd = dayjs.utc(endTimestamp * 1000).toDate()
        const canceledAt = canceledTimestamp ? dayjs.utc(canceledTimestamp * 1000).toDate() : null
        const priceId = subscriptionItem.price.id

        console.log(`创建新订阅记录，订阅ID: ${subscriptionId}, 状态: ${subscription.status}`)

        const previousSubscriptions = await prisma.stripeSubscription.findMany({
          where: {
            userId,
            stripeSubscriptionId: {
              not: subscriptionId,
            },
            status: {
              in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PAST_DUE],
            },
          },
        })

        if (previousSubscriptions && previousSubscriptions.length > 0) {
          console.log(`发现 ${previousSubscriptions.length} 个之前的活跃订阅，先取消这些订阅`)

          await Promise.all(
            previousSubscriptions.map(async (prevSub) => {
              try {
                await immediatelyCancelSubscription(prevSub.stripeSubscriptionId)
                console.log(`成功取消之前的订阅: ${prevSub.stripeSubscriptionId}`)
              } catch (error) {
                console.error(`取消之前的订阅失败: ${prevSub.stripeSubscriptionId}`, error)
              }
            }),
          )
        }

        await prisma.stripeSubscription.create({
          data: {
            userId,
            stripeSubscriptionId: subscriptionId,
            status: mapStripeStatusToPrismaStatus(subscription.status),
            planType: paymentPlan.type,
            priceId: priceId,
            paymentScheme: paymentPlan.scheme,
            currency: paymentPlan.currency || session.currency?.toUpperCase(),
            isOneTimePayment: false,
            paymentMethod: 'card', // Stripe订阅默认使用card
            currentPeriodStart: periodStart,
            currentPeriodEnd: periodEnd,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            canceledAt: canceledAt,
          },
        })

        await processUserMembershipForSubscription(
          userId,
          priceId,
          'checkout_completed',
          periodStart,
          periodEnd,
        )
        console.log(`结账完成事件处理完成，用户ID: ${userId}`)
      } catch (error) {
        console.error('处理订阅完成事件失败:', error)
        throw error
      }
      break

    case 'setup':
      console.log(`收到setup模式的结账完成事件，用户ID: ${userId}，目前不处理此类型`)
      break

    default:
      console.log(`未知的结账模式: ${session.mode}，用户ID: ${userId}`)
      break
  }
}

// handle subscription updated event
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  try {
    const customerId = subscription.customer
    if (!customerId) {
      console.error('订阅没有关联的客户ID')
      return
    }

    const customerIdStr = typeof customerId === 'string' ? customerId : customerId.id
    console.log(`处理订阅更新，客户ID: ${customerIdStr}, 订阅ID: ${subscription.id}`)

    await prisma.$transaction(
      async (tx) => {
        const customerRecord = await tx.stripeCustomer.findUnique({
          where: { stripeCustomerId: customerIdStr },
        })

        if (!customerRecord) {
          console.log(`未找到客户记录，客户ID: ${customerIdStr}`)
          return
        }

        const subscriptionItem = subscription.items.data[0]
        const startTimestamp = subscriptionItem.current_period_start
        const endTimestamp = subscriptionItem.current_period_end
        const canceledTimestamp = subscription.canceled_at || null

        const periodStart = dayjs.utc(startTimestamp * 1000).toDate()
        const periodEnd = dayjs.utc(endTimestamp * 1000).toDate()
        const canceledAt = canceledTimestamp ? dayjs.utc(canceledTimestamp * 1000).toDate() : null

        const priceId = subscriptionItem.price.id
        const paymentPlan = getPaymentPlanByPriceId(priceId)
        if (!paymentPlan) {
          console.error(`无法获取有效的支付计划，价格ID: ${priceId}，跳过处理`)
          return
        }

        await tx.stripeSubscription.update({
          where: { stripeSubscriptionId: subscription.id },
          data: {
            status: mapStripeStatusToPrismaStatus(subscription.status),
            planType: paymentPlan.type,
            priceId: priceId,
            currentPeriodStart: periodStart,
            currentPeriodEnd: periodEnd,
            cancelAtPeriodEnd: subscription.cancel_at_period_end || false,
            canceledAt: canceledAt,
            transitionStatus: TransitionStatus.NORMAL,
          },
        })
      },
      { timeout: 10_000 },
    )
  } catch (error) {
    console.error('处理订阅更新失败:', error)
    // 即使处理失败，也要将transitionStatus重置为NORMAL，防止前端一直轮询
    try {
      if (subscription && subscription.id) {
        await prisma.stripeSubscription.updateMany({
          where: {
            stripeSubscriptionId: subscription.id,
            transitionStatus: {
              in: [TransitionStatus.CANCELED_IN_PROGRESS, TransitionStatus.RESTORING],
            },
          },
          data: {
            transitionStatus: TransitionStatus.NORMAL,
          },
        })
        console.log(
          `已重置订阅 ${subscription.id} 的transitionStatus为NORMAL（处理失败后的兜底措施）`,
        )
      }
    } catch (resetError) {
      console.error(`重置订阅状态失败:`, resetError)
    }
    throw error
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log(`处理账单支付成功事件，发票ID: ${invoice.id}，账单原因: ${invoice.billing_reason}`)
  try {
    if (!invoice.customer) {
      console.error('发票没有关联的客户ID')
      return
    }
    const customerId = typeof invoice.customer === 'string' ? invoice.customer : invoice.customer.id

    const customerRecord = await prisma.stripeCustomer.findUnique({
      where: { stripeCustomerId: customerId },
    })

    if (!customerRecord) {
      console.log(`找不到客户记录，客户ID: ${customerId}，跳过处理`)
      return
    }

    switch (invoice.billing_reason) {
      case 'subscription_create':
        // 首次订阅
        console.log('首次订阅，跳过处理')
        break

      case 'subscription_cycle': {
        let subscriptionId: string | undefined = undefined
        // 1. 从invoice.parent.subscription_details.subscription中获取
        if (
          invoice.parent &&
          invoice.parent.subscription_details &&
          invoice.parent.subscription_details.subscription
        ) {
          subscriptionId = invoice.parent.subscription_details.subscription as string
          console.log(`从invoice.parent.subscription_details获取到订阅ID: ${subscriptionId}`)
        }
        // 2. 从invoice.lines.data中获取
        else if (invoice.lines && invoice.lines.data && invoice.lines.data.length > 0) {
          const lineItem = invoice.lines.data[0]
          if (
            lineItem &&
            lineItem.parent &&
            lineItem.parent.subscription_item_details &&
            lineItem.parent.subscription_item_details.subscription
          ) {
            subscriptionId = lineItem.parent.subscription_item_details.subscription as string
            console.log(
              `从invoice.lines.data[0].parent.subscription_item_details获取到订阅ID: ${subscriptionId}`,
            )
          }
        }
        // 3. 从数据库记录中获取
        else {
          const subscription = await prisma.stripeSubscription.findFirst({
            where: {
              userId: customerRecord.userId,
              status: {
                in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.PAST_DUE],
              },
            },
            orderBy: { currentPeriodStart: 'desc' },
          })
          subscriptionId = subscription?.stripeSubscriptionId
          console.log(`从数据库记录获取到订阅ID: ${subscriptionId}`)
        }

        if (!subscriptionId) {
          console.error(`无法获取有效的订阅ID，用户ID: ${customerRecord.userId}，跳过处理`)
          return
        }

        // get stripe subscription info
        const stripeSubscription = await stripeClient.subscriptions.retrieve(subscriptionId)
        if (!stripeSubscription) {
          console.error(`无法获取有效的订阅信息，订阅ID: ${subscriptionId}，跳过处理`)
          return
        }

        // payment plan info
        const subscriptionItem = stripeSubscription.items.data[0]
        const priceId = subscriptionItem.price.id

        if (!priceId) {
          console.error(`无法获取有效的价格ID，跳过处理`)
          return
        }

        const paymentPlan = getPaymentPlanByPriceId(priceId)
        if (!paymentPlan) {
          console.error(`无法获取有效的支付计划，价格ID: ${priceId}，跳过处理`)
          return
        }

        // get subscription period info
        const startTimestamp = subscriptionItem.current_period_start
        const endTimestamp = subscriptionItem.current_period_end
        const periodStart = dayjs.utc(startTimestamp * 1000).toDate()
        const periodEnd = dayjs.utc(endTimestamp * 1000).toDate()

        await processUserMembershipForSubscription(
          customerRecord.userId,
          priceId,
          'invoice_payment_succeeded',
          periodStart,
          periodEnd,
        )
        break
      }
      case 'subscription_update':
        // 升级降级
        console.log('升级降级，跳过处理')
        break

      default:
        console.log('其他情况，跳过处理')
        break
    }

    console.log(`账单支付成功事件处理完成，发票ID: ${invoice.id}`)
  } catch (error) {
    console.error(
      `处理账单支付成功事件失败: ${error instanceof Error ? error.message : String(error)}`,
    )
    console.error(`错误详情: ${JSON.stringify(error)}`)
    throw error
  }
}
