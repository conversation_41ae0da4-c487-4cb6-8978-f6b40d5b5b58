import { getPaymentPlanByPriceId } from '@/config/stripe/plans'
import { SUBSCRIPTION_CANCEL_URL, SUBSCRIPTION_SUCCESS_URL } from '@/config/stripe/stripe'
import { Stripe } from 'stripe'
import { stripeClient } from './client'
import { getOrCreateCustomer } from './customer'

// get checkout session
export async function retrieveCheckoutSession(sessionId: string): Promise<Stripe.Checkout.Session> {
  return stripeClient.checkout.sessions.retrieve(sessionId)
}

// create checkout session
export async function createCheckoutSession(userId: string, email: string, priceId: string) {
  const customerId = await getOrCreateCustomer(userId, email)
  const paymentPlan = getPaymentPlanByPriceId(priceId)

  if (!paymentPlan) {
    throw new Error(`Invalid price ID: ${priceId}`)
  }

  const sessionConfig: Stripe.Checkout.SessionCreateParams = {
    customer: customerId,
    payment_method_types:
      paymentPlan.paymentMethods as Stripe.Checkout.SessionCreateParams.PaymentMethodType[],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: paymentPlan.isSubscription ? 'subscription' : 'payment',
    success_url: `${SUBSCRIPTION_SUCCESS_URL}?session_id={CHECKOUT_SESSION_ID}`,
    cancel_url: `${SUBSCRIPTION_CANCEL_URL}`,
    client_reference_id: userId,
    metadata: {
      userId: userId,
      priceId: priceId,
    },
    payment_intent_data: paymentPlan.isSubscription
      ? undefined
      : {
          metadata: {
            userId: userId,
            priceId: priceId,
          },
        },
    ...(paymentPlan.scheme === 'B' && {
      automatic_tax: {
        enabled: true,
      },
      customer_update: {
        address: 'auto',
      },
    }),
  }

  // Add payment method options for WeChat Pay
  if (paymentPlan.paymentMethods.includes('wechat_pay')) {
    sessionConfig.payment_method_options = {
      wechat_pay: {
        client: 'web',
      },
    }
  }

  const session = await stripeClient.checkout.sessions.create(sessionConfig)
  return session
}
