import { EASYKOL_WARNING_CHANNEL } from '@/config/env'
import { getAmountBySchemeAndType, getPaymentPlanByPriceId } from '@/config/stripe/plans'
import { SlackClient } from '@/infras/monitoring/slackClient'
import { CardSubscriptionStatus, MemberStatus, MemberType, QuotaType, prisma } from '@repo/database'
import dayjs from 'dayjs'
import { MembershipService } from '../membership.service'

/**
 * 处理一次性支付订阅
 * @param userId 用户ID
 * @param priceId 价格ID
 * @param source 触发来源
 * @returns 操作是否成功
 */
export async function processOneTimeSubscription(
  userId: string,
  priceId: string,
  source:
    | 'checkout_completed'
    | 'manual_assignment'
    | 'payment_intent_succeeded' = 'checkout_completed',
): Promise<boolean> {
  try {
    const paymentPlan = getPaymentPlanByPriceId(priceId)

    if (!paymentPlan) {
      console.error(`未找到价格ID为 ${priceId} 的支付方案`)
      return false
    }

    const quotaAmount = paymentPlan.quotaAmount
    const planType = paymentPlan.type
    const scheme = paymentPlan.scheme
    const now = dayjs.utc().toDate()

    try {
      await prisma.$transaction(
        async (tx) => {
          // update membership status 、quota、card subscription status
          const membership = await MembershipService.getInstance().refreshUserMembershipInfo(userId)

          if (!membership) {
            throw new Error(`未找到用户 ${userId} 的会员信息`)
          }

          if (membership.type === MemberType.ENTERPRISE && membership.enterpriseId) {
            console.log(`用户 ${userId} 是企业用户，跳过个人订阅计划处理`)
            return
          }

          let cardEffectiveAt = now
          let cardExpireAt = now
          let accountEffectiveAt = now
          let accountExpireAt = now

          // check if the info card is active
          const isCardActive =
            membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE &&
            membership.cardSubscriptionExpireAt &&
            dayjs(membership.cardSubscriptionExpireAt).isAfter(dayjs(now)) &&
            membership.cardSubscriptionEffectiveAt &&
            dayjs(membership.cardSubscriptionEffectiveAt).isBefore(dayjs(now))

          // set info card effective at and expire at
          if (isCardActive) {
            cardEffectiveAt = membership.cardSubscriptionEffectiveAt || now
            cardExpireAt = membership.cardSubscriptionExpireAt || now

            cardExpireAt = dayjs(cardExpireAt)
              .add(paymentPlan.validityPeriod, paymentPlan.validityUnit)
              .toDate()
          } else {
            cardEffectiveAt = now
            cardExpireAt = dayjs(now)
              .add(paymentPlan.validityPeriod, paymentPlan.validityUnit)
              .toDate()
          }

          if (membership.type === MemberType.PAID) {
            accountEffectiveAt = membership.effectiveAt
            accountExpireAt = membership.expireAt

            accountExpireAt = dayjs(accountExpireAt)
              .add(paymentPlan.validityPeriod, paymentPlan.validityUnit)
              .toDate()
          } else {
            accountEffectiveAt = now
            accountExpireAt = dayjs(now)
              .add(paymentPlan.validityPeriod, paymentPlan.validityUnit)
              .toDate()
          }

          await tx.userMembership.update({
            where: { id: membership.id },
            data: {
              type: MemberType.PAID,
              accountQuota: { increment: quotaAmount }, // increment quota
              cardSubscriptionStatus: CardSubscriptionStatus.ACTIVE,
              cardSubscriptionEffectiveAt: cardEffectiveAt,
              cardSubscriptionExpireAt: cardExpireAt,
              effectiveAt: accountEffectiveAt,
              expireAt: accountExpireAt,
              status: MemberStatus.ACTIVE,
            },
          })

          await tx.quotaLog.create({
            data: {
              userId,
              membershipId: membership.id,
              usage: quotaAmount,
              type: QuotaType.ADMIN,
              description: `EasyKOL one time payment for ${paymentPlan.description}, quota: ${quotaAmount}, validity:${cardEffectiveAt.toISOString()} to ${cardExpireAt.toISOString()}`,
              createdBy: 'system',
              metadata: {
                // schema info
                source,
                planType,
                priceId,
                paymentScheme: paymentPlan.scheme,
                eventTime: now.toISOString(),
                quotaAmount,
                // previous info
                previousCardStatus: membership.cardSubscriptionStatus,
                previousCardEffectiveAt: membership.cardSubscriptionEffectiveAt?.toISOString(),
                previousCardExpireAt: membership.cardSubscriptionExpireAt?.toISOString(),
                previousAccountQuota: membership.accountQuota,
                previousAccountEffectiveAt: membership.effectiveAt?.toISOString(),
                previousAccountExpireAt: membership.expireAt?.toISOString(),
                previousAccountPaidType: membership.type,
              },
            },
          })
        },
        { timeout: 10_000 },
      )

      // send slack notification
      await sendSlackPaymentNotification(userId, planType, scheme, '一次性购买', now).catch(
        (error) => {
          console.error('发送 Slack 付费通知失败:', error)
        },
      )

      return true
    } catch (error) {
      console.error(
        `处理一次性订阅失败: 用户=${userId}, 计划=${planType}, 原因=${error instanceof Error ? error.message : String(error)}`,
      )
      throw error
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error(`处理一次性订阅失败: ${errorMessage}`)

    // 发送 Slack 错误通知
    try {
      await SlackClient.getInstance().sendMessage(
        EASYKOL_WARNING_CHANNEL,
        `❌ *处理一次性订阅失败*\n*用户ID:* ${userId}\n*价格ID:* ${priceId}\n*错误原因:* ${errorMessage}\n*时间:* ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`,
        { notify_channel: true },
      )
    } catch (slackError) {
      console.error('发送 Slack 错误通知失败:', slackError)
    }

    return false
  }
}

/**
 * 更新用户会员配额和信息卡会员
 * @param userId 用户ID
 * @param planType 订阅计划类型
 * @param source 触发来源
 * @param periodStart 订阅周期开始时间
 * @param periodEnd 订阅周期结束时间
 * @returns 操作是否成功
 */
export async function processUserMembershipForSubscription( // 增加支持美元的支付的配额补充逻辑
  userId: string,
  priceId: string,
  source: 'checkout_completed' | 'invoice_payment_succeeded',
  periodStart: Date,
  periodEnd: Date,
): Promise<boolean> {
  // 从配置中获取配额，不再硬编码
  const paymentPlan = getPaymentPlanByPriceId(priceId)
  if (!paymentPlan) {
    throw new Error(`未找到价格ID为 ${priceId} 的支付方案`)
  }

  const quotaAmount = paymentPlan.quotaAmount
  const planType = paymentPlan.type
  const scheme = paymentPlan.scheme
  const subscriptionEvent = source === 'checkout_completed' ? '订阅' : '续费'

  if (!periodStart) {
    throw new Error('订阅周期开始时间不能为空')
  }
  if (!periodEnd) {
    throw new Error('订阅周期结束时间不能为空')
  }

  try {
    await prisma.$transaction(
      async (tx) => {
        const membership = await tx.userMembership.findUniqueOrThrow({
          where: { userId },
        })

        if (membership.type === MemberType.ENTERPRISE && membership.enterpriseId) {
          console.log(`用户 ${userId} 是企业用户，跳过个人订阅计划处理`)
          return
        }

        await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            type: MemberType.PAID,
            accountQuota: { set: quotaAmount },
            usedQuota: { set: 0 },
            cardSubscriptionStatus: CardSubscriptionStatus.ACTIVE,
            cardSubscriptionEffectiveAt: periodStart,
            cardSubscriptionExpireAt: periodEnd,
            effectiveAt: periodStart,
            expireAt: periodEnd,
            status: MemberStatus.ACTIVE,
          },
        })

        // 记录配额日志
        await tx.quotaLog.create({
          data: {
            userId,
            membershipId: membership.id,
            usage: quotaAmount,
            type: QuotaType.ADMIN,
            description: `EasyKOL ${planType} ${subscriptionEvent},配额: ${quotaAmount},会员有效期:${periodStart}至${periodEnd}`,
            createdBy: 'system',
            metadata: {
              source,
              planType,
              subscriptionEvent,
              eventTime: new Date().toISOString(),
              periodStart: periodStart.toISOString(),
              periodEnd: periodEnd.toISOString(),
            },
          },
        })
      },
      { timeout: 10_000 },
    )

    // 事务外异步发送 Slack 通知
    await sendSlackPaymentNotification(
      userId,
      planType,
      scheme,
      subscriptionEvent,
      periodStart,
    ).catch((error) => {
      console.error('发送 Slack 付费通知失败:', error)
    })

    return true
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error(`更新用户会员失败: 用户=${userId}, 计划=${planType}, 原因=${errorMessage}`)
    try {
      await SlackClient.getInstance().sendMessage(
        EASYKOL_WARNING_CHANNEL,
        `❌ *更新用户会员订阅失败*\n*用户ID:* ${userId}\n*价格ID:* ${priceId}\n*计划类型:* ${planType}\n*错误原因:* ${errorMessage}\n*时间:* ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`,
        { notify_channel: true },
      )
    } catch (slackError) {
      console.error('发送 Slack 错误通知失败:', slackError)
    }

    return false
  }
}

/**
 * 发送 Slack 付费通知
 */
async function sendSlackPaymentNotification(
  userId: string,
  planType: 'BASIC' | 'PRO',
  scheme: 'A' | 'B' | 'C',
  subscriptionEvent: string,
  paymentTime: Date,
): Promise<void> {
  try {
    // 获取用户信息
    const user = await prisma.userInfo.findUniqueOrThrow({
      where: { userId },
      select: { email: true },
    })

    // 构建支付通知数据
    const paymentData = {
      email: user.email || '未知邮箱',
      plan: planType,
      amount: getAmountBySchemeAndType(scheme, planType),
      subscriptionType: subscriptionEvent,
      paymentTime: paymentTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      }),
    }

    // 发送 Slack 通知
    await SlackClient.getInstance().sendPaymentNotification(
      EASYKOL_WARNING_CHANNEL,
      paymentData,
      `🎉 用户${user.email} ${subscriptionEvent}成功`,
      { notify_users: true },
    )

    console.log(`用户付费 Slack 通知发送成功: ${user.email}`)
  } catch (error) {
    console.error(
      `发送 Slack 付费通知失败: ${error instanceof Error ? error.message : String(error)}`,
    )
  }
}
