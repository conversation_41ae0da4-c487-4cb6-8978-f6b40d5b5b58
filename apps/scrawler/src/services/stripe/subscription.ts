import { isSimplifiedChinese } from '@/config/stripe/plans'
import { EASYKOL_STRIPE_PROTAL_RETURN_URL } from '@/config/stripe/stripe'
import { prisma, StripeSubscription, SubscriptionStatus } from '@repo/database'
import { Stripe } from 'stripe'
import { stripeClient } from './client'

// get subscription info
export async function getSubscription(subscriptionId: string) {
  if (!subscriptionId) {
    console.error('getSubscription called with no subscriptionId')
    return null
  }
  return stripeClient.subscriptions.retrieve(subscriptionId)
}

// cancel subscription at period end
export async function cancelSubscription(subscriptionId: string) {
  if (!subscriptionId) {
    console.error('cancelSubscription called with no subscriptionId')
    return null
  }
  return stripeClient.subscriptions.update(subscriptionId, {
    cancel_at_period_end: true,
  })
}

// immediately cancel subscription,dont refund
export async function immediatelyCancelSubscription(subscriptionId: string) {
  if (!subscriptionId) {
    console.error('immediatelyCancelSubscription called with no subscriptionId')
    return null
  }
  return stripeClient.subscriptions.cancel(subscriptionId, {
    prorate: false, // dont refund
  })
}

// create customer portal session (for managing subscription)
export async function createPortalSession(customerId: string) {
  if (!customerId) {
    console.error('createPortalSession called with no customerId')
    return null
  }
  const portalOptions: Stripe.BillingPortal.SessionCreateParams = {
    customer: customerId,
  }

  const portalReturnUrl = EASYKOL_STRIPE_PROTAL_RETURN_URL || ''
  if (portalReturnUrl) {
    portalOptions.return_url = portalReturnUrl
  }

  return stripeClient.billingPortal.sessions.create(portalOptions)
}

// find active subscription including one-time payments
export async function findUserActiveSubscription(
  userId: string,
): Promise<StripeSubscription | null> {
  if (!userId) {
    console.error('findUserActiveSubscription called with no userId')
    return null
  }
  return prisma.stripeSubscription.findFirst({
    where: {
      userId,
      OR: [
        {
          AND: [
            { isOneTimePayment: true },
            { status: SubscriptionStatus.ACTIVE },
            { currentPeriodEnd: { gt: new Date() } },
          ],
        },
        {
          AND: [
            { isOneTimePayment: false },
            {
              OR: [{ status: SubscriptionStatus.ACTIVE }, { status: SubscriptionStatus.PAST_DUE }],
            },
          ],
        },
      ],
    },
    orderBy: { createdAt: 'desc' },
  })
}

// a b c all subscription
export async function findUserFirstSubscription(
  userId: string,
): Promise<StripeSubscription | null> {
  if (!userId) {
    console.error('findUserFirstSubscription called with no userId')
    return null
  }
  return prisma.stripeSubscription.findFirst({
    where: {
      userId,
    },
    orderBy: { createdAt: 'asc' },
  })
}

export async function getRecommendedPaymentScheme(
  userId: string,
  browserLanguage: string = 'en',
): Promise<{ recommendedScheme: 'A' | 'B' | 'C'; hasHistory: boolean }> {
  const subscription = await prisma.stripeSubscription.findFirst({
    where: { userId },
    orderBy: { createdAt: 'asc' },
  })

  let recommendedScheme = 'B' as 'A' | 'B' | 'C'

  if (subscription?.paymentScheme) {
    recommendedScheme = subscription.paymentScheme as 'A' | 'B' | 'C'
  } else {
    const language = (browserLanguage || 'en').toLowerCase()
    recommendedScheme = isSimplifiedChinese(language) ? 'A' : 'B'
  }
  return {
    recommendedScheme,
    hasHistory: !!subscription,
  }
}
