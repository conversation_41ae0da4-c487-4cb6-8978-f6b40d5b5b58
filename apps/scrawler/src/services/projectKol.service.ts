import { KolPlatform, ProjectKol, ProjectKolAttitude, prisma } from '@repo/database'
import { PaginationService } from '../utils/pagination'

/**
 * 获取项目ProjectKol列表
 * @param projectId 项目ID
 * @param options 筛选选项
 * @returns 项目ProjectKol列表
 */
async function getProjectKols(
  projectId: string,
  options?: {
    page?: number
    pageSize?: number
    attitude?: ProjectKolAttitude[]
  },
): Promise<{
  data: ProjectKol[]
  total: number
}> {
  const project = await prisma.project.findUnique({
    where: { id: projectId, deletedAt: null },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  const attitudes = options?.attitude || [
    ProjectKolAttitude.LIKE,
    ProjectKolAttitude.DISLIKE,
    ProjectKolAttitude.SUPERLIKE,
  ]

  const { page, pageSize, skip } = PaginationService.handlePagination({
    page: options?.page,
    pageSize: options?.pageSize,
  })

  const [projectKols, total] = await Promise.all([
    prisma.projectKol.findMany({
      where: {
        projectId: project.id,
        attitude: { in: attitudes },
      },
      include: {
        KolInfo: true,
      },
      skip,
      take: pageSize,
      orderBy: {
        createdAt: 'desc',
      },
    }),
    prisma.projectKol.count({
      where: {
        projectId: project.id,
        attitude: { in: attitudes },
      },
    }),
  ])

  return {
    data: projectKols,
    total,
  }
}

/**
 * 获取项目中下所有已经评价过的kol uniqueId
 * @param projectId 项目ID
 * @param options 筛选选项
 * @returns 平台账号ID列表
 */
async function getProjectKolUniques(
  projectId: string,
  options?: {
    attitude?: ProjectKolAttitude[]
    platform?: KolPlatform[]
  },
): Promise<string[]> {
  const project = await prisma.project.findUnique({
    where: { id: projectId, deletedAt: null },
  })

  if (!project) {
    throw new Error('Project not found')
  }

  const attitudes = options?.attitude || [
    ProjectKolAttitude.LIKE,
    ProjectKolAttitude.DISLIKE,
    ProjectKolAttitude.SUPERLIKE,
  ]

  const platforms = options?.platform || [
    KolPlatform.YOUTUBE,
    KolPlatform.TIKTOK,
    KolPlatform.INSTAGRAM,
    KolPlatform.TWITTER,
  ]

  const projectKols = await prisma.projectKol.findMany({
    where: {
      projectId: project.id,
      attitude: { in: attitudes },
      KolInfo: {
        platform: { in: platforms },
      },
    },
    select: {
      KolInfo: {
        select: {
          platformAccount: true,
          platform: true,
        },
      },
    },
  })

  const uniqueIds: string[] = projectKols
    .filter((projectKol) => projectKol.KolInfo !== null)
    .map((projectKol) => projectKol.KolInfo!.platformAccount)
    .filter((id): id is string => id !== null)

  return uniqueIds
}

async function filterUnreadKolIds(projectId: string, kolIds: string[]) {
  if (!kolIds.length) return []

  const readKolIds = await prisma.projectKol.findMany({
    where: {
      projectId,
      kolId: {
        in: kolIds,
      },
      attitude: {
        in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.DISLIKE, ProjectKolAttitude.SUPERLIKE],
      },
    },
    select: {
      kolId: true,
    },
  })

  const readKolIdSet = new Set(readKolIds.map((kol) => kol.kolId))
  return kolIds.filter((id) => !readKolIdSet.has(id))
}

/**
 * 批量标记候选数据的态度
 * @param projectId 项目ID
 * @param kolIds KOL ID列表
 * @param attitude 要设置的态度
 * @param userId 操作用户ID
 * @param similarTaskId 相似任务ID（可选）
 * @returns 操作结果统计
 */
async function batchMarkKolAttitude(
  projectId: string,
  kolIds: string[],
  attitude: ProjectKolAttitude,
  userId: string,
  similarTaskId?: string,
): Promise<{
  updated: number
  created: number
  total: number
}> {
  try {
    if (!kolIds?.length) {
      return { updated: 0, created: 0, total: 0 }
    }

    const validKolIds = kolIds.filter(Boolean)
    if (validKolIds.length === 0) {
      return { updated: 0, created: 0, total: 0 }
    }

    const existingRecords = await prisma.projectKol.findMany({
      where: {
        projectId,
        kolId: { in: validKolIds },
      },
    })

    const existingKolIds = new Set(existingRecords.map((record) => record.kolId))
    const newKolIds = validKolIds.filter((kolId) => !existingKolIds.has(kolId))

    let updatedCount = 0
    let createdCount = 0

    if (existingRecords.length > 0) {
      const updateResult = await prisma.projectKol.updateMany({
        where: {
          projectId,
          kolId: { in: Array.from(existingKolIds) },
        },
        data: {
          attitude,
          rateBy: userId,
          updatedAt: new Date(),
          ...(similarTaskId && { similarTaskId }),
        },
      })
      updatedCount = updateResult.count
    }

    if (newKolIds.length > 0) {
      const newRecords = newKolIds.map((kolId) => {
        const record: any = {
          projectId,
          kolId,
          attitude,
          rateBy: userId,
        }
        if (similarTaskId) {
          record.similarTaskId = similarTaskId
        }
        return record
      })

      await prisma.projectKol.createMany({
        data: newRecords,
      })
      createdCount = newKolIds.length
    }

    return {
      updated: updatedCount,
      created: createdCount,
      total: updatedCount + createdCount,
    }
  } catch (error) {
    console.error('批量标记KOL态度失败:', error)
    throw error
  }
}

export const ProjectKolService = {
  getProjectKols,
  getProjectKolUniques,
  filterUnreadKolIds,
  batchMarkKolAttitude,
}
