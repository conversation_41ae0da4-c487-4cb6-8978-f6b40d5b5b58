/**
 * 这里用来爬取各种外部的外链，获取到想要的信息
 */

import { IgPost } from '@/api/@types/rapidapi/Instagram'
import { TiktokVideoDetail } from '@/api/@types/rapidapi/Tiktok'
import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import RapidApiStatsCollector from '@/api/ApiCallStat'
import { EmailSourceType } from '@/types/email'
import * as UrlService from '@/utils/url'
import { KolInfo } from '@repo/database'

const MIN_NUMBER_LENGTH = 8

export interface ParseableExternalLink {
  parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]>
}

export interface PostExternalLink {
  getPost(): Promise<TiktokVideoDetail | IgPost | VideoDetail | null>
}

export abstract class ExternalLink {
  protected rawLink: string

  protected depth: number

  protected root: string

  public type: LinkType

  protected kol: KolInfo | null

  constructor(url: string, root: string, type: LinkType, depth = 0) {
    this.rawLink = url
    this.depth = depth
    this.type = type
    this.root = root
    this.kol = null
  }

  public getLink(): string {
    return this.rawLink
  }

  public setKol(kol: KolInfo) {
    this.kol = kol
  }

  public getKol(): KolInfo | null {
    return this.kol
  }

  public static async getLinkType(url: string): Promise<LinkType> {
    if (!url?.length) {
      return LinkType.UNKNOWN
    }
    const linkTypeMap: { [key: string]: () => Promise<LinkType> } = {
      linktree: () => Promise.resolve(LinkType.LINKTREE),
      snapchat: () => Promise.resolve(LinkType.SNAPCHAT),
      whatsApp: () => Promise.resolve(LinkType.WHATSAPP),
      youtubeVideo: () => Promise.resolve(LinkType.YOUTUBE_VIDEO),
      youtubeShorts: () => Promise.resolve(LinkType.YOUTUBE_SHORTS),
      tiktokVideo: () => Promise.resolve(LinkType.TIKTOK_VIDEO),
      instagramPost: () => Promise.resolve(LinkType.INSTAGRAM_POST),
      instagramReel: () => Promise.resolve(LinkType.INSTAGRAM_REEL),
      instagram: () => Promise.resolve(LinkType.INSTAGRAM),
      youtube: () => Promise.resolve(LinkType.YOUTUBE),
      tiktok: () => Promise.resolve(LinkType.TIKTOK),
      homepage: () => Promise.resolve(LinkType.HOMEPAGE),
    }

    for (const [key, func] of Object.entries(linkTypeMap)) {
      if (await (UrlService as any)[`is${key.charAt(0).toUpperCase() + key.slice(1)}`](url)) {
        return await func()
      }
    }
    return LinkType.UNKNOWN
  }
}

export enum LinkType {
  SNAPCHAT = 'snapchat',
  LINKTREE = 'linktree',
  WHATSAPP = 'whatsapp',
  INSTAGRAM = 'instagram',
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  HOMEPAGE = 'homepage',
  YOUTUBE_VIDEO = 'youtube_video',
  YOUTUBE_SHORTS = 'youtube_shorts',
  TIKTOK_VIDEO = 'tiktok_video',
  INSTAGRAM_POST = 'instagram_post',
  INSTAGRAM_REEL = 'instagram_reel',
  UNKNOWN = 'unknown',
}

export enum ContactType {
  EMAIL = 'email', // 邮箱地址
  PHONE = 'phone', // whatsapp 号码
  SOCIAL = 'social', // 社媒平台链接
}

export interface ContactItem {
  type: ContactType
  content: string
  url: string
  depth: number
  updatedAt?: number
  root: string
  linkType: LinkType
  emailSource?: EmailSourceType
  platformAccount?: string
}

export const extractPhoneNumber = (text: string | undefined): string[] => {
  if (!text?.length) {
    return []
  }

  // TODO replace the math digits
  const mathDigits = '+𝟿𝟼𝟾 𝟽𝟷𝟷𝟷𝟶𝟺𝟽𝟻'

  text = text.trim()
  const pattern = new RegExp(`[+]?[0-9- \\t]{${MIN_NUMBER_LENGTH},}`, 'g')
  const result = text.match(pattern)
  if (!result) return []
  const pureList: string[] = []
  return Array.from(result).filter((i) => {
    const digitPatttern = new RegExp(`\\d`, 'g')
    const digits = i.match(digitPatttern)
    if (digits) {
      const length = Array.from(digits).length
      const pure = digits.join('')
      if (pureList.includes(pure)) {
        return false
      } else {
        pureList.push(pure)
        return length > MIN_NUMBER_LENGTH
      }
    }
    return false
  })
}
