import RapidApiStatsCollector from '@/api/ApiCallStat'
import Sentry from '@/infras/sentry'
import Logger from '@/utils/logger'
import {
  ContactItem,
  ContactType,
  ExternalLink,
  extractPhoneNumber,
  LinkType,
  ParseableExternalLink,
} from './contact'
import { MAX_LINK_DEPTH } from './contact.utils'
import { HomepageExternalLink } from './homepage'

// WhatsApp 非个人主页地址黑名单
export const whatsappBanList = [
  'whatsapp.com/stayconnected',
  'whatsapp.com/privacy',
  'whatsapp.com/download',
  'whatsapp.com/legal',
  'whatsapp.com/join',
  'whatsapp.com/security',
  'whatsapp.com/about',
  'whatsapp.com/contact',
  'whatsapp.com/community',
  'whatsapp.com/expressyourself',
  'faq.whatsapp.com/',
  'whatsapp.com/android',
  'whatsapp.com/sitemap',
  'business.whatsapp.com/',
  'whatsapp.com/features',
  'whatsapp.com/help',
  'whatsapp.com/support',
  'whatsapp.com/coronavirus',
  'whatsapp.com/business',
]

export class WhatsAppExternalLink extends ExternalLink implements ParseableExternalLink {
  public type: LinkType = LinkType.WHATSAPP

  public async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH || collector?.isExternalLinkExceeded()) {
      return []
    }
    const phoneNumber = extractPhoneNumber(this.rawLink)
    const results: ContactItem[] = []
    if (phoneNumber?.length) {
      phoneNumber.forEach((number) => {
        results.push({
          type: ContactType.PHONE,
          content: number,
          depth: this.depth,
          url: this.rawLink,
          root: this.root,
          linkType: LinkType.WHATSAPP,
        } as ContactItem)
      })
    }

    // 检查是否为非个人主页地址，如果是则跳过页面解析
    const shouldSkipPageParsing = whatsappBanList.some((banPattern) =>
      this.rawLink.toLowerCase().includes(banPattern),
    )

    if (shouldSkipPageParsing) {
      Logger.info(`跳过 WhatsApp 非个人主页地址的页面解析: ${this.rawLink}`)
      return results
    }

    try {
      const pageResult = await new HomepageExternalLink(
        this.rawLink,
        this.root,
        this.type,
        this.depth,
      ).parse(collector)
      if (pageResult.length) {
        Sentry.captureEvent({
          message: 'whatsapp parse page',
          level: 'info',
          extra: {
            url: this.rawLink,
            count: pageResult.length,
          },
          tags: {
            success: true,
          },
        })
        results.push(...pageResult)
      }
    } catch (err) {
      Logger.error(`whatsapp parse error: ${err}`)
      Sentry.captureEvent({
        message: 'whatsapp parse page',
        level: 'info',
        extra: {
          url: this.rawLink,
          reason: err instanceof Error ? err.message : 'unknown',
        },
        tags: {
          success: false,
        },
      })
    }
    return results
  }
}
