import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import YoutubeApi from '@/api/youtube'
import * as UrlService from '@/utils/url'
import { KolPlatform } from '@repo/database'
import { ContactItem, ContactType, ExternalLink, LinkType, PostExternalLink } from './contact'
import { MAX_LINK_DEPTH } from './contact.utils'

export class YoutubeVideoOrShortsExternalLink extends ExternalLink implements PostExternalLink {
  public async getPost(): Promise<VideoDetail | null> {
    let videoId = null
    if (UrlService.isYoutubeShorts(this.rawLink)) {
      videoId = UrlService.getYoutubeShortsId(this.rawLink)
    } else if (UrlService.isYoutubeVideo(this.rawLink)) {
      videoId = UrlService.getYoutubeVideoId(this.rawLink)
    } else {
      return null
    }
    if (!videoId) {
      return null
    }
    const video = await YoutubeApi.getInstance().getVideo(videoId)
    if (!video) {
      return null
    }
    return video
  }

  public async parse(): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH) {
      return []
    }
    const video = await this.getPost()
    if (!video) {
      return []
    }
    return [
      {
        type: ContactType.SOCIAL,
        content: video.channelTitle,
        url: UrlService.getKolLink(KolPlatform.YOUTUBE, video.channelId),
        depth: this.depth,
        root: this.root,
        linkType: LinkType.YOUTUBE,
        platformAccount: video.channelId,
        // emailSource,
        // updatedAt,
      },
    ] as ContactItem[]
  }
}
