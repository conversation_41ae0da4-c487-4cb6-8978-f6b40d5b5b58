import RapidApiStatsCollector from '@/api/ApiCallStat'
import { logTime } from '@/api/util'
import { FEATURE_PROXY_ON } from '@/config/env'
import { brightDataProxy } from '@/infras/proxy'
import Sentry from '@/infras/sentry'
import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import Bluebird from 'bluebird'
import { HTMLElement, parse } from 'node-html-parser'
import { ofetch } from 'ofetch'
import {
  ContactItem,
  ContactType,
  ExternalLink,
  LinkType,
  ParseableExternalLink,
  extractPhoneNumber,
} from './contact'
import { MAX_LINK_DEPTH, newExternalLink } from './contact.utils'
import { DynamicHomepageExternalLink } from './dynamicHomepage'
import { UnknownExternalLink } from './unknown'
import { WhatsAppExternalLink } from './whatsapp'

export const homepageBanList = [
  'linktr.ee/register',
  'linktr.ee/login',
  'link.me/',
  'help.linktr.ee',
]

export class HomepageExternalLink extends ExternalLink implements ParseableExternalLink {
  async parse(collector: RapidApiStatsCollector | undefined): Promise<ContactItem[]> {
    if (this.depth > MAX_LINK_DEPTH || collector?.isExternalLinkExceeded()) {
      return []
    }
    if (this.rawLink.toLowerCase().includes('linkin.bio')) {
      return new DynamicHomepageExternalLink(
        this.rawLink,
        this.root,
        LinkType.HOMEPAGE,
        this.depth,
      ).parse(collector)
    }
    try {
      const raw = await ofetch(this.rawLink, {
        dispatcher: FEATURE_PROXY_ON ? brightDataProxy : undefined,
      })
      const pageData = parse(raw)
      const candidates = extractTextAndLinks(pageData).slice(0, 10) // 一个 homepage 最多保留 10 个外链
      const results: ContactItem[] = []
      await Bluebird.map(
        candidates,
        async (i) => {
          if (i.type == 'link') {
            const href = i.href
            if (homepageBanList.some((i) => href?.toLowerCase().includes(i))) {
              console.log(`link ${href} in ban list`)
              return
            }
            if (!href) {
              return
            }
            const link = await newExternalLink(href, this.root, this.depth + 1)
            const text = i.content
            const hrefEmail = extractEmail(href)
            const hrefNumber = extractPhoneNumber(href)
            const textEmail = extractEmail(text)
            const textNumber = extractPhoneNumber(text)
            if (hrefEmail) {
              results.push({
                type: ContactType.EMAIL,
                content: hrefEmail,
                depth: this.depth,
                url: this.rawLink,
                root: this.root,
                linkType: LinkType.HOMEPAGE,
                emailSource: EmailSourceType.LINK_INFO,
              })
            }
            if (link instanceof WhatsAppExternalLink && hrefNumber?.length) {
              results.push(
                ...hrefNumber.map((i) => {
                  return {
                    type: ContactType.PHONE,
                    content: i,
                    depth: this.depth,
                    url: this.rawLink,
                    root: this.root,
                    linkType: LinkType.HOMEPAGE,
                  }
                }),
              )
            }
            if (!(link instanceof UnknownExternalLink) && 'parse' in link) {
              const parseResult = await logTime(
                (link as ParseableExternalLink).parse(collector),
                `[sj: external-link]${link.getLink()}`,
              )
              if (Array.isArray(parseResult)) {
                results.push(...parseResult)
              }
            }
            if (textEmail) {
              results.push({
                type: ContactType.EMAIL,
                content: textEmail,
                depth: this.depth,
                url: this.rawLink,
                root: this.root,
                linkType: LinkType.HOMEPAGE,
                emailSource: EmailSourceType.LINK_INFO,
              })
            }
            if (textNumber?.length) {
              results.push(
                ...textNumber.map((i) => {
                  return {
                    type: ContactType.PHONE,
                    content: i,
                    depth: this.depth,
                    url: this.rawLink,
                    root: this.root,
                    linkType: LinkType.HOMEPAGE,
                  }
                }),
              )
            }
          } else if (i.type == 'text') {
            const textEmail = extractEmail(i.content)
            const textNumber = extractPhoneNumber(i.content)
            if (textEmail) {
              results.push({
                type: ContactType.EMAIL,
                content: textEmail,
                depth: this.depth,
                url: this.rawLink,
                root: this.root,
                linkType: LinkType.HOMEPAGE,
                emailSource: EmailSourceType.LINK_INFO,
              })
            }
            if (textNumber?.length) {
              results.push(
                ...textNumber.map((i) => {
                  return {
                    type: ContactType.PHONE,
                    content: i,
                    depth: this.depth,
                    url: this.rawLink,
                    root: this.root,
                    linkType: LinkType.HOMEPAGE,
                  }
                }),
              )
            }
          }
        },
        { concurrency: 5 },
      )
      return results
    } catch (err) {
      console.log(`parse url ${this.rawLink} failed: ${err}`)
      Sentry.captureException(err)
      return []
    }
  }
}

function extractTextAndLinks(
  element: HTMLElement,
  options: {
    excludeTags?: string[]
    excludeSelectors?: string[]
    maxDepth?: number
  } = {},
): TextLinkItem[] {
  const {
    excludeTags = ['script', 'style', 'noscript', 'head', 'meta', 'link', 'iframe'],
    // excludeSelectors = ['.hidden', '[style*="display: none"]', '[hidden]'],
    maxDepth = 100,
  } = options

  // 检查元素是否可见
  function isElementVisible(el: HTMLElement): boolean {
    if (!el?.tagName) {
      return true
    }
    // 检查是否在排除标签中
    if (excludeTags.includes(el?.tagName?.toLowerCase())) {
      return false
    }
    return true
  }

  // 主递归函数
  function traverse(el: HTMLElement, currentDepth: number = 0): TextLinkItem[] {
    // 深度限制
    if (currentDepth > maxDepth) {
      return []
    }

    // // 检查元素是否可见
    if (!isElementVisible(el)) {
      return []
    }

    const results: TextLinkItem[] = []

    // 递归处理子节点
    for (const child of el.childNodes) {
      if (child instanceof HTMLElement) {
        results.push(...traverse(child, currentDepth + 1))
      } else if (child.nodeType === 3) {
        // 文本节点
        const text = child.textContent.trim()
        if (text) {
          results.push({
            type: 'text',
            content: text,
          })
        }
      }
    }

    // 处理链接
    if (el?.tagName?.toLowerCase() === 'a') {
      const href = el.getAttribute('href')
      const linkText = el.textContent.trim()

      if (href && linkText) {
        results.push({
          type: 'link',
          content: linkText,
          href: href,
          tagName: el.tagName.toLowerCase(),
        })
      }
    }

    // 处理文本节点
    if (el?.childNodes?.length === 0 && el.textContent.trim()) {
      results.push({
        type: 'text',
        content: el.textContent.trim(),
        tagName: el.tagName.toLowerCase(),
      })
    }

    return results
  }

  // 过滤和去重
  function filterAndDeduplicateResults(items: TextLinkItem[]): TextLinkItem[] {
    const uniqueItems: TextLinkItem[] = []
    const seenContent = new Set<string>()

    for (const item of items) {
      // 去除重复内容
      const key = `${item.type}:${item.content}`
      if (!seenContent.has(key)) {
        seenContent.add(key)
        uniqueItems.push(item)
      }
    }

    return uniqueItems
  }

  // 执行遍历并过滤结果
  return filterAndDeduplicateResults(traverse(element))
}

interface TextLinkItem {
  type: 'text' | 'link'
  content: string
  href?: string
  tagName?: string
}
