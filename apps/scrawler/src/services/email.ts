import { newGoogleOAuth2Client, sendEmailByGmail, verifyGoogleTokens } from '@/api/gmail'
import { getChannelEmailFromNano } from '@/api/nano'
import TiktokApi from '@/api/tiktok'
import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import { NODE_ENV, SLACK_ALERT_EMAIL } from '@/config/env.ts'
import { UpdateTemplateRequest } from '@/routes/schemas/email'
import { EmailSourceType, KolPrompt, SendEmailRequest, SenderClient } from '@/types/email'
import { EmailContext } from '@/types/emailSend'
import {
  EmailCredentialType,
  EmailSender,
  KolInfo,
  KolPlatform,
  Project,
  ProjectKolAttitude,
  ProviderType,
  SendStatus,
  SentEmailType,
  prisma,
} from '@repo/database'
import { OAuth2Client } from 'google-auth-library'
import _ from 'lodash'
import EmailManageService from './emailManage'
import { addEmailForKol } from './kolInfo.service'

class EmailService {
  private static instance: EmailService

  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService()
    }
    return EmailService.instance
  }

  public compoundEmailPrompt = async (kolInfo: KolInfo, project: Project): Promise<KolPrompt> => {
    return {
      kolTitle: kolInfo.title || '',
      kolDescription: kolInfo.description || '',
      kolEmail: kolInfo.email || '',
      kolHistoryEmails: kolInfo.historyEmails || [],
      platformAccount: kolInfo.platformAccount || '',
      infoLastUpdated: kolInfo.updatedAt?.toISOString() || '',
      projectTitle: project.title || '',
      projectDescription: project.description || '',
    }
  }

  public getKolEmail = async (
    userId: string | undefined,
    kol: KolInfo,
  ): Promise<{ email: string | null; source: EmailSourceType | null | undefined }> => {
    if (kol.email) {
      return { email: kol.email, source: kol.emailSource as EmailSourceType }
    }
    let source: EmailSourceType | undefined | null = kol.emailSource as EmailSourceType
    let email: string | undefined = undefined
    if (kol.platformAccount) {
      // 增加 tt的邮箱获取途径
      if (kol.platform === KolPlatform.TIKTOK) {
        const userDetail = await TiktokApi.getInstance().getUserDetail({
          unique_id: kol.platformAccount,
        })
        if (userDetail?.user?.email) {
          await addEmailForKol(userId, kol.id, userDetail.user.email, userDetail.user.emailSource)
          return { email: userDetail.user.email, source: userDetail.user.emailSource }
        }
      }
      if (kol.platform == KolPlatform.YOUTUBE) {
        const res = await getChannelEmailFromNano(kol.platform, kol.platformAccount)
        email = res.email
        source = res.source
      }
      if (!email) {
        return { email: null, source: null }
      }
      return { email, source }
    }
    return { email: null, source: null }
  }

  public prepareSenderClient = async (sender: EmailSender): Promise<SenderClient> => {
    switch (sender.type) {
      case EmailCredentialType.GMAIL: {
        if (!sender.providerCredentialId) {
          throw new SenderPrepareError(400, 'Email credential not set')
        }
        const credential = await prisma.providerCredential.findUnique({
          where: { id: sender.providerCredentialId },
        })

        if (!credential?.accessToken || !credential?.refreshToken) {
          throw new SenderPrepareError(400, 'Email credential not ready')
        }

        const gmailClinet = newGoogleOAuth2Client(credential as any)

        const info = await verifyGoogleTokens(gmailClinet)

        if (info?.email !== sender.email) {
          throw new SenderPrepareError(400, 'Email credential not match')
        }

        return {
          sendEmail: async ({ from, to, subject, text, cc, bcc }, context: EmailContext) => {
            // 非生产环境：转发到 Slack
            if (NODE_ENV !== 'production') {
              const originalTo = to
              const originalCc = cc
              const originalBcc = bcc

              to = SLACK_ALERT_EMAIL
              // 清空抄送，避免在测试环境误发
              cc = []
              bcc = []

              // 在邮件内容前添加原始收件人信息
              let recipientInfo = `原始收件人: ${originalTo}\n原始发件人: ${from}`
              if (originalCc && originalCc.length > 0) {
                recipientInfo += `\n原始抄送: ${originalCc.join(', ')}`
              }
              if (originalBcc && originalBcc.length > 0) {
                recipientInfo += `\n原始密送: ${originalBcc.join(', ')}`
              }

              text = `[非生产环境邮件转发]\n${recipientInfo}\n\n${text}`
              subject = `[${NODE_ENV || 'dev'}] ${subject}`
            }

            const res = await sendEmailByGmail(
              {
                client: gmailClinet,
                from,
                to,
                subject,
                text,
                cc,
                bcc,
              },
              context,
            )
            return res.data as any
          },
        }
      }
      default:
        throw new SenderPrepareError(400, 'Invalid email credential type')
    }
  }

  public populateEmailSender = async (crential: EmailSender) => {
    return _.pick(crential, ['id', 'email', 'type', 'createdBy', 'createdAt', 'updatedAt'])
  }

  /**
   * 添加邮箱变更审计日志
   * @param userId 用户ID
   * @param kolId 博主ID
   * @param email 新邮箱
   * @param emailSource 邮箱来源
   * @returns 是否成功添加日志,不成功则不更新邮箱
   */
  public async addEmailAuditLog(
    userId: string | undefined,
    kolId: string,
    email: string | undefined | null,
    emailSource: EmailSourceType | undefined,
  ): Promise<boolean> {
    if (!userId || !kolId) {
      console.log('addEmailAuditLog: userId or kolId is null')
      return false
    }
    if (!email) {
      return false
    }
    try {
      await prisma.kolEmailAuditLog.create({
        data: {
          userId,
          kolId,
          email: email || '',
          emailSource: emailSource || 'unknown',
        },
      })
      return true
    } catch (error) {
      console.error('addEmailAuditLog error:', error)
      return false
    }
  }

  /**
   * 创建Google OAuth2客户端
   */
  public createGoogleOAuth2Client = ({
    accessToken,
    refreshToken,
  }: {
    accessToken: string
    refreshToken: string
  }): OAuth2Client => {
    return newGoogleOAuth2Client({ accessToken, refreshToken })
  }

  /**
   * 获取Google token的实际授权scope列表
   */
  public getTokenScopes = async (
    client: OAuth2Client,
  ): Promise<{
    scopes?: string[]
    email?: string
    error?: string
  }> => {
    try {
      // 刷新token确保有效性
      await client.refreshAccessToken()
      const accessToken = await client.getAccessToken()

      if (!accessToken.token) {
        throw new Error('No access token available')
      }

      // 调用Google的tokeninfo端点获取详细信息
      const response = await fetch(
        `https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${accessToken.token}`,
      )

      if (!response.ok) {
        throw new Error(`HTTP Error: ${response.status} ${response.statusText}`)
      }

      const tokenInfo = await response.json()

      return {
        scopes: tokenInfo.scope ? tokenInfo.scope.split(' ') : [],
        email: tokenInfo.email,
      }
    } catch (error: any) {
      console.error('Error getting token scopes:', error)
      return {
        error: error.message,
        scopes: [],
      }
    }
  }

  public updateTemplate = async (id: string, data: UpdateTemplateRequest, userId: string) => {
    const template = await prisma.emailTemplate.findFirst({
      where: { id, createdBy: userId },
    })
    if (!template) {
      throwError(StatusCodes.NOT_FOUND, 'Template not found')
    }
    return prisma.emailTemplate.update({
      where: { id },
      data: { ...data, updatedAt: new Date() },
    })
  }

  /**
   * 发送邮件
   */
  public sendEmail = async (
    userId: string,
    request: SendEmailRequest,
  ): Promise<{
    record: any
    followupThread: any
  }> => {
    const { templateId, kolId, email, usingEmail, projectId } = request

    // 查找 KOL 信息
    const kolInfo = await prisma.kolInfo.findFirst({
      where: {
        id: kolId,
      },
    })

    if (!kolInfo) {
      throw new Error('KOL not found')
    }
    // if (!kolInfo.email) {
    //   throw new Error('KOL email not found')
    // }

    // get email template
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: templateId,
      },
    })
    if (!template) {
      throw new Error('template not found')
    }

    // get email sender
    const sender = await prisma.emailSender.findFirst({
      where: {
        createdBy: userId,
        email: usingEmail,
      },
    })
    if (!sender) {
      throw new Error('sender not found')
    }
    const client = await this.prepareSenderClient(sender)

    const project = await prisma.project.findFirst({
      where: {
        id: projectId,
      },
    })

    if (!project) {
      throw new Error('projectId is required')
    }

    // 没提供 email 则从 kolInfo 获取
    let targetEmail = email
    if (!targetEmail) {
      const result = await this.getKolEmail(userId, kolInfo)
      targetEmail = result.email ?? ''
    }

    if (!targetEmail) {
      throw new Error('KOL email not found')
    }

    // 默认 like 这个 kol
    const relation = await prisma.projectKol.upsert({
      where: {
        projectId_kolId: {
          projectId: projectId,
          kolId: kolId,
        },
      },
      update: {
        attitude: ProjectKolAttitude.LIKE,
        rateBy: userId,
      },
      create: {
        projectId: projectId,
        similarTaskId: '',
        kolId: kolId,
        rateBy: userId,
        attitude: ProjectKolAttitude.LIKE,
      },
    })

    // 发送邮件
    const sendResult = await client.sendEmail(
      {
        from: usingEmail,
        to: targetEmail,
        subject: template.subject,
        text: template.content,
        cc: template.cc,
        bcc: template.bcc,
      },
      {
        nickname: kolInfo.title || kolInfo.platformAccount || '',
      },
    )

    const record = await prisma.emailRecord.create({
      data: {
        templateId: templateId,
        type: SentEmailType.SAY_HI,
        status: SendStatus.SENT,
        projectKolId: relation.id,
        from: usingEmail,
        to: targetEmail,
        subject: template.subject,
        content: template.content,
        cc: template.cc,
        bcc: template.bcc,
        resultObj: sendResult as any,
        sentAt: new Date(),
      },
    })

    // 检查是否有跟进配置并创建跟进线程
    const followup = await prisma.emailFollowup.findFirst({
      where: {
        emailTemplateId: templateId,
        userId: userId,
      },
    })

    let followupThread = null
    if (
      followup &&
      followup.followupsEmails &&
      typeof followup.followupsEmails === 'object' &&
      followup.followupsEmails.length
    ) {
      // 获取邮件线程ID (假设在sendResult中)
      const threadId = (sendResult as any).threadId || (sendResult as any).id || record.id

      try {
        followupThread = await EmailManageService.createFollowupThread({
          kolId,
          kolEmail: targetEmail,
          userId: userId,
          userEmail: usingEmail,
          followupId: followup.id,
          threadId: threadId,
          followupEmails: followup.followupsEmails as any,
        })
        console.log('Created followup thread:', followupThread.id)
      } catch (error) {
        console.error('Failed to create followup thread:', error)
        // 不阻塞主流程，只记录错误
      }
    }

    return {
      record,
      followupThread: followupThread
        ? {
            id: followupThread.id,
            status: followupThread.status,
            emailPlansCreated: true,
          }
        : null,
    }
  }

  /**
   * 获取用户Gmail授权scope列表
   */
  public getGmailScopes = async (userId: string) => {
    // 查找用户的Google Provider Credentials
    const credentials = await prisma.providerCredential.findMany({
      where: {
        createdBy: userId,
      },
      include: {
        Provider: true,
      },
    })

    // 过滤出Google类型的credentials
    const googleCredentials = credentials.filter(
      (credential) => credential.Provider.type === ProviderType.GOOGLE,
    )

    if (googleCredentials.length === 0) {
      throw new Error('No Google credentials found for this user')
    }

    // 获取每个credential的实际授权scope
    const scopeResults = await Promise.all(
      googleCredentials.map(async (credential) => {
        try {
          if (!credential.accessToken || !credential.refreshToken) {
            return {
              credentialId: credential.id,
              providerKey: credential.Provider.key,
              error: 'Missing access token or refresh token',
              actualScopes: [],
              configuredScopes: credential.Provider.scopes,
              createdAt: credential.createdAt,
              updatedAt: credential.updatedAt,
              expiresAt: credential.expiresAt,
            }
          }

          // 创建OAuth2客户端
          const client = this.createGoogleOAuth2Client({
            accessToken: credential.accessToken,
            refreshToken: credential.refreshToken,
          })
          const tokenInfo = await this.getTokenScopes(client)

          return {
            credentialId: credential.id,
            providerKey: credential.Provider.key,
            actualScopes: tokenInfo.scopes || [],
            configuredScopes: credential.Provider.scopes,
            email: tokenInfo.email,
            error: tokenInfo.error,
            createdAt: credential.createdAt,
            updatedAt: credential.updatedAt,
            expiresAt: credential.expiresAt,
          }
        } catch (error: any) {
          console.error(`Error getting scopes for credential ${credential.id}:`, error)
          return {
            credentialId: credential.id,
            providerKey: credential.Provider.key,
            error: error.message,
            actualScopes: [],
            configuredScopes: credential.Provider.scopes,
            createdAt: credential.createdAt,
            updatedAt: credential.updatedAt,
            expiresAt: credential.expiresAt,
          }
        }
      }),
    )

    return scopeResults ?? []
  }

  public getCurrentScopes = async (accessToken: string, refreshToken: string) => {
    const client = this.createGoogleOAuth2Client({
      accessToken,
      refreshToken,
    })
    return this.getTokenScopes(client)
  }
}

export default EmailService

export class SenderPrepareError extends Error {
  code: number

  constructor(code: number, message: string) {
    super(message)
    this.name = 'SenderPrepareError'
    this.code = code
  }
}
