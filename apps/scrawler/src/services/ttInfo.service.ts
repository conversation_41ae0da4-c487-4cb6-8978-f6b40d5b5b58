// ttInfoService
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tik<PERSON><PERSON>ommentUser, T<PERSON><PERSON><PERSON>ollower } from '@/api/@types/rapidapi/Tiktok'
import Tiktok<PERSON>pi from '@/api/tiktok'
import {
  TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COMMENTS_COUNT,
  TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COUNT,
  TIKTOK_MAX_COMMENT_COUNT,
} from '@/config/env'
import { getUserPortraitAnalysis } from '@/services/aiTools/userPortaitAnslysisWithoutReason'
import {
  AudienceAnalysisResult,
  CountryAnalysisResult,
  RegionAnalysisResult,
  UserPortraitLLMInput,
  UserPortraitLLMOutput,
  UserPortraitResult,
} from '@/types/audience'
import { TtVideoCommentsUsersAndRegionStatisticsResponse } from '@/types/response/ttInfo'
import { statisticsUserRegion } from '@/utils/country'
import Bluebird from 'bluebird'

const maxCommentCount = +TIKTOK_MAX_COMMENT_COUNT || 1000
const getAuthorRecentlyVideosCount = +TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COUNT || 10
const getAuthorRecentlyVideosCommentsCount =
  +TIKTOK_GET_AUTHOR_RECENTLY_VIDEOS_COMMENTS_COUNT || 1000
const maxAnalysisUsersCount = 100

/**
 * 获取视频评论用户数据并统计地区分布
 */
async function getVideoCommentsUsersWithStatistics(
  url: string,
): Promise<TtVideoCommentsUsersAndRegionStatisticsResponse> {
  const api = TiktokApi.getInstance()
  let cursor = 0
  let allCommentsUsers: TiktokCommentUser[] = []

  // 获取评论用户数据
  while (true) {
    const result = await api.getVideoComments({
      url,
      cursor,
      count: Math.min(50, maxCommentCount - allCommentsUsers.length),
    })

    if (!result?.comments?.length) {
      break
    }
    const commentUsers = result.comments.map((comment: TiktokComment) => comment.user)
    allCommentsUsers = [...allCommentsUsers, ...commentUsers]

    if (allCommentsUsers.length >= maxCommentCount || !result.hasMore) {
      break
    }

    cursor = result.cursor
  }

  const regionStats: Record<string, number> = {}
  const formattedUsers = allCommentsUsers.map((user) => {
    const region = user.region || '未知'
    regionStats[region] = (regionStats[region] || 0) + 1

    return {
      userId: user.id,
      uniqueId: user.unique_id,
      nickname: user.nickname,
      region: region,
    }
  })

  // 对地区统计进行排序
  const sortedRegionStats: Record<string, number> = Object.fromEntries(
    Object.entries(regionStats).sort(([, a], [, b]) => b - a),
  )

  // 修改从 URL 中提取视频 Id 的逻辑
  const videoId = url.match(/video\/(\d+)/)?.[1] || ''

  const response: TtVideoCommentsUsersAndRegionStatisticsResponse = {
    videoId,
    total: formattedUsers.length,
    region: sortedRegionStats,
    commentsUsers: formattedUsers,
  }
  return response
}

/**
 * 获取视频发布后的粉丝用户
 * @param userId 作者用户ID
 * @param videoCreateTime 视频发布时间戳
 * @param maxFollowersCount 最大粉丝数量
 * @returns 视频发布后关注的粉丝列表
 */
async function getFollowersAfterVideo(
  userId: string,
  videoCreateTime: number,
  maxFollowersCount: number = 5000,
): Promise<TiktokFollower[]> {
  try {
    const api = TiktokApi.getInstance()
    const MAX_FOLLOWERS = maxFollowersCount
    const PAGE_SIZE = 50 // 每次请求的数量

    console.log(`[tt] 获取用户 ${userId} 在视频发布后的粉丝，视频发布时间: ${videoCreateTime}`)

    const followerIdSet = new Set<string>()
    const followerMap = new Map<string, TiktokFollower>()
    let time = 0

    while (followerIdSet.size < MAX_FOLLOWERS) {
      try {
        console.log(
          `[tt] 获取用户 ${userId} 的粉丝，当前已获取 ${followerIdSet.size} 个，time: ${time}`,
        )

        const result = await api.getUserFollowers(
          userId,
          Math.min(PAGE_SIZE, MAX_FOLLOWERS - followerIdSet.size),
          time,
        )

        if (!result?.followers?.length) {
          console.log(`[tt] 用户 ${userId} 没有更多粉丝`)
          break
        }

        console.log(
          `[tt] 用户 ${userId} 本次获取到 ${result.followers.length} 个粉丝，分页时间: ${result.time}`,
        )

        // 检查分页时间是否小于视频发布时间
        if (result.time < videoCreateTime) {
          console.log(`[tt] 分页时间 ${result.time} 小于视频发布时间 ${videoCreateTime}，停止获取`)
          // 如果是第一页就小于视频发布时间，仍然取第一页的数据
          if (followerIdSet.size === 0) {
            console.log(`[tt] 第一页数据仍然添加到结果中`)
            for (const follower of result.followers) {
              if (follower.id && !followerIdSet.has(follower.id)) {
                followerIdSet.add(follower.id)
                followerMap.set(follower.id, follower)
              }
            }
          }
          break
        }

        for (const follower of result.followers) {
          if (follower.id && !followerIdSet.has(follower.id)) {
            followerIdSet.add(follower.id)
            followerMap.set(follower.id, follower)
          }
        }

        if (result.hasMore && followerIdSet.size < MAX_FOLLOWERS) {
          time = result.time
          console.log(`[tt] 用户 ${userId} 将获取下一页粉丝，time: ${time}`)
        } else {
          if (!result.hasMore) {
            console.log(`[tt] 用户 ${userId} 没有更多粉丝页`)
          } else {
            console.log(`[tt] 用户 ${userId} 已达到最大粉丝数限制`)
          }
          break
        }
      } catch (error) {
        console.error(`[tt] 获取用户 ${userId} 粉丝失败:`, error)
        break
      }
    }

    const followers = Array.from(followerMap.values())
    console.log(`[tt] 成功获取 ${followers.length} 个视频发布后的粉丝`)

    return followers
  } catch (error) {
    console.error('[tt] 获取TikTok视频发布后粉丝用户信息失败:', error)
    return []
  }
}

/**
 * 获取作者的粉丝用户
 */
async function getAuthorFollowers(
  userId: string,
  maxFollowersCount: number = 5000,
): Promise<TiktokFollower[]> {
  try {
    const api = TiktokApi.getInstance()
    const MAX_FOLLOWERS = maxFollowersCount
    const PAGE_SIZE = 200 // 每次请求的数量

    console.log(`[tt] 获取用户 ${userId} 的粉丝`)

    const followerIdSet = new Set<string>()
    const followerMap = new Map<string, TiktokFollower>()
    let time = 0

    while (followerIdSet.size < MAX_FOLLOWERS) {
      try {
        console.log(
          `[tt] 获取用户 ${userId} 的粉丝，当前已获取 ${followerIdSet.size} 个，time: ${time}`,
        )

        const result = await api.getUserFollowers(
          userId,
          Math.min(PAGE_SIZE, MAX_FOLLOWERS - followerIdSet.size),
          time,
        )

        if (!result?.followers?.length) {
          console.log(`[tt] 用户 ${userId} 没有更多粉丝`)
          break
        }

        console.log(`[tt] 用户 ${userId} 本次获取到 ${result.followers.length} 个粉丝`)

        for (const follower of result.followers) {
          if (follower.id && !followerIdSet.has(follower.id)) {
            followerIdSet.add(follower.id)
            followerMap.set(follower.id, follower)
          }
        }

        if (result.hasMore && followerIdSet.size < MAX_FOLLOWERS) {
          time = result.time
          console.log(`[tt] 用户 ${userId} 将获取下一页粉丝，time: ${time}`)
        } else {
          if (!result.hasMore) {
            console.log(`[tt] 用户 ${userId} 没有更多粉丝页`)
          } else {
            console.log(`[tt] 用户 ${userId} 已达到最大粉丝数限制`)
          }
          break
        }
      } catch (error) {
        console.error(`[tt] 获取用户 ${userId} 粉丝失败:`, error)
        break
      }
    }

    const followers = Array.from(followerMap.values())
    console.log(`[tt] 成功获取 ${followers.length} 个粉丝的用户信息`)

    return followers
  } catch (error) {
    console.error('[tt] 获取TikTok粉丝用户信息失败:', error)
    return []
  }
}

/**
 * 获取作者最近视频的评论用户
 */
async function getAuthorVideosCommentsUsers(
  uniqueId: string,
  pageSize: number = 50,
  maxCommentsCount: number = getAuthorRecentlyVideosCommentsCount,
  maxVideosCount: number = getAuthorRecentlyVideosCount,
): Promise<TiktokCommentUser[]> {
  try {
    const api = TiktokApi.getInstance()
    const MAX_COMMENTS = maxCommentsCount
    const CONCURRENCY = 50
    const MAX_VIDEOS = maxVideosCount
    const MAX_PAGES_PER_VIDEO = 5

    console.log(`[tt] 获取用户 ${uniqueId} 的视频`)
    const authorVideos = await api.getUserVideos({ unique_id: uniqueId, count: 30, cursor: 0 })

    if (!authorVideos?.length) {
      console.error(`[tt] 未找到用户 ${uniqueId} 的视频`)
      return []
    }

    // filter is_top
    const filteredVideos = authorVideos
      .filter((video) => !video.is_top)
      .sort((a, b) => b.comment_count - a.comment_count)
      .slice(0, MAX_VIDEOS)

    console.log(`[tt] 将处理 ${filteredVideos.length} 个评论最多的视频`)

    const commentUserIdSet = new Set<string>()
    const commentUserMap = new Map<string, TiktokCommentUser>()

    async function processVideoComments(video: any): Promise<void> {
      if (commentUserIdSet.size >= MAX_COMMENTS) {
        return
      }

      let cursor = 0
      let pageCount = 0

      while (pageCount < MAX_PAGES_PER_VIDEO && commentUserIdSet.size < MAX_COMMENTS) {
        try {
          console.log(
            `[tt] 获取视频 ${video.video_id} 的第 ${pageCount + 1} 页评论${cursor ? '(分页)' : '(首页)'}`,
          )

          const result = await api.getVideoComments({
            url: video.video_id,
            cursor,
            count: Math.min(pageSize, MAX_COMMENTS - commentUserIdSet.size),
          })

          if (!result?.comments?.length) {
            console.log(`[tt] 视频 ${video.video_id} 没有更多评论`)
            break
          }

          console.log(
            `[tt] 视频 ${video.video_id} 第 ${pageCount + 1} 页获取到 ${result.comments.length} 条评论`,
          )

          for (const comment of result.comments) {
            if (comment.user && comment.user.id && !commentUserIdSet.has(comment.user.id)) {
              commentUserIdSet.add(comment.user.id)
              commentUserMap.set(comment.user.id, comment.user)
            }
          }

          if (
            result.hasMore &&
            pageCount < MAX_PAGES_PER_VIDEO - 1 &&
            commentUserIdSet.size < MAX_COMMENTS
          ) {
            cursor = result.cursor
            pageCount++
            console.log(`[tt] 视频 ${video.video_id} 将获取第 ${pageCount + 1} 页评论`)
          } else {
            if (!result.hasMore) {
              console.log(`[tt] 视频 ${video.video_id} 没有更多评论页`)
            } else {
              console.log(`[tt] 视频 ${video.video_id} 达到最大页数限制或已收集足够用户`)
            }
            break
          }

          if (pageCount % 2 === 0) {
            console.log(
              `[tt] 视频 ${video.video_id} 已处理 ${pageCount} 页评论，当前已收集 ${commentUserMap.size} 个用户`,
            )
          }
        } catch (error) {
          console.error(`[tt] 获取视频 ${video.video_id} 评论失败:`, error)
          break
        }
      }
    }

    console.log(`[tt] 开始并行处理 ${filteredVideos.length} 个视频的评论，并发数: ${CONCURRENCY}`)
    await Bluebird.map(filteredVideos, processVideoComments, { concurrency: CONCURRENCY })

    const commentUsers = Array.from(commentUserMap.values())
    console.log(`[tt] 成功获取 ${commentUsers.length} 个评论者的用户信息`)

    return commentUsers
  } catch (error) {
    console.error('[tt] 获取TikTok评论者用户信息失败:', error)
    return []
  }
}

/**
 * 统计评论用户的地区分布
 * @deprecated 使用 statisticsUserRegion 替代
 */
async function statisticsCommentsUsers(
  commentsUsers: TiktokCommentUser[],
): Promise<CountryAnalysisResult> {
  // 统计各地区数量
  const regionStats: Record<string, number> = {}
  commentsUsers.forEach((user) => {
    const region = user.region || '未知'
    regionStats[region] = (regionStats[region] || 0) + 1
  })

  const total = commentsUsers.length

  const statistics = Object.entries(regionStats)
    .map(([region, count]) => ({
      region,
      count,
      percentage: `${((count / total) * 100).toFixed(2)}%`,
    }))
    .sort((a, b) => b.count - a.count)

  const filteredStatistics = statistics.filter(
    (item) =>
      !(item.region.toLowerCase() === 'us' && parseFloat(item.percentage.replace('%', '')) < 10),
  )

  return {
    total,
    statistics: filteredStatistics,
  }
}

/**
 * 获取作者评论区用户的用户画像
 * @param commentsUsers 评论用户列表
 * @returns 用户画像
 */
async function getAuthorCommentsUsersPortrait(
  commentsUsers: TiktokCommentUser[],
): Promise<UserPortraitLLMOutput> {
  const users: UserPortraitLLMInput[] = commentsUsers.map((user) => {
    return {
      username: user.nickname,
      avatar: user.avatar,
      signature: user.signature || '',
    }
  })

  console.log(`[getAuthorCommentsUsersPortrait] 处理了 ${users.length} 个用户数据`)
  const result = await getUserPortraitAnalysis(users)
  return result
}

/**
 * 获取作者评论区用户和粉丝的受众分析
 * @param uniqueId 作者ID
 * @returns 受众分析结果
 */
async function getAuthorAudienceAnalysis(uniqueId: string): Promise<AudienceAnalysisResult> {
  try {
    const api = TiktokApi.getInstance()
    const userDetail = await api.getUserDetail({ unique_id: uniqueId })

    if (!userDetail?.user?.id) {
      throw new Error(`无法获取用户 ${uniqueId} 的详细信息`)
    }

    const userId = userDetail.user.id
    console.log(`[getAuthorAudienceAnalysis] 获取到用户ID: ${userId}`)

    const [commentUsersResult, followersResult] = await Promise.allSettled([
      getAuthorVideosCommentsUsers(uniqueId),
      getAuthorFollowers(userId),
    ])

    const commentUsers = commentUsersResult.status === 'fulfilled' ? commentUsersResult.value : []
    if (commentUsersResult.status === 'rejected') {
      console.error('[getAuthorAudienceAnalysis] 获取评论用户失败:', commentUsersResult.reason)
    }

    const followers = followersResult.status === 'fulfilled' ? followersResult.value : []
    if (followersResult.status === 'rejected') {
      console.error('[getAuthorAudienceAnalysis] 获取粉丝失败:', followersResult.reason)
    }

    console.log(`[getAuthorAudienceAnalysis] 获取了 ${commentUsers.length} 个评论用户数据`)
    console.log(`[getAuthorAudienceAnalysis] 获取了 ${followers.length} 个粉丝数据`)

    const userMap = new Map<string, TiktokCommentUser>()

    commentUsers.forEach((user) => {
      if (user.id) {
        userMap.set(user.id, user)
      }
    })

    followers.forEach((follower) => {
      if (follower.id && !userMap.has(follower.id)) {
        userMap.set(follower.id, follower as TiktokCommentUser)
      }
    })

    const mergedUsers = Array.from(userMap.values())
    console.log(`[getAuthorAudienceAnalysis] 合并去重后共 ${mergedUsers.length} 个用户数据`)

    if (!mergedUsers?.length) {
      throw new Error('未找到用户数据')
    }

    // 限制分析用户数量，避免请求过大
    const needAgeAndGenderAnalysisUsers = mergedUsers.slice(0, maxAnalysisUsersCount)

    const [userPortraitResult, regionAnalysisResult] = await Promise.all([
      getAuthorCommentsUsersPortrait(needAgeAndGenderAnalysisUsers).catch((error) => {
        console.error('[getAuthorAudienceAnalysis] 用户画像分析失败:', error)
        return {
          gender: { male: 0, female: 0 },
          age: { under18: 0, age18to25: 0, age25to45: 0, above45: 0 },
        } as UserPortraitLLMOutput
      }),
      statisticsUserRegion(mergedUsers).catch((error) => {
        console.error('[getAuthorAudienceAnalysis] 国家分析失败:', error)
        return {
          total: 0,
          statistics: [],
          developmentStatistics: [],
        } as RegionAnalysisResult
      }),
    ])

    // 验证返回的数据结构是否正确
    if (!userPortraitResult || typeof userPortraitResult !== 'object') {
      throw new Error('用户画像数据格式错误')
    }

    if (!regionAnalysisResult || typeof regionAnalysisResult !== 'object') {
      throw new Error('地区分析数据格式错误')
    }

    return {
      userPortraitResult: formatToPercentage(userPortraitResult),
      regionAnalysisResult,
    }
  } catch (error) {
    console.error('[getAuthorAudienceAnalysis] 受众分析失败:', error)
    throw error
  }
}

const formatToPercentage = (result: any): UserPortraitResult => {
  return {
    gender: {
      male: `${result.gender.male}%`,
      female: `${result.gender.female}%`,
    },
    age: {
      under18: `${result.age.under18}%`,
      age18to25: `${result.age.age18to25}%`,
      age25to45: `${result.age.age25to45}%`,
      above45: `${result.age.above45}%`,
    },
  }
}

/**
 * 获取单个视频的评论用户
 * @param videoId 视频ID
 * @param pageSize 每页评论数量
 * @param maxCommentsCount 最大评论数量
 * @returns 评论用户列表
 */
async function getVideosCommentsUsers(
  videoId: string,
  pageSize: number = 50,
  maxPages: number = 20,
  maxCommentsCount: number = maxCommentCount,
): Promise<TiktokCommentUser[]> {
  try {
    const api = TiktokApi.getInstance()

    console.log(`[tt] 获取视频 ${videoId} 的评论用户`)

    const commentUserIdSet = new Set<string>()
    const commentUserMap = new Map<string, TiktokCommentUser>()

    let cursor = 0
    let pageCount = 0

    while (pageCount < maxPages && commentUserIdSet.size < maxCommentsCount) {
      try {
        console.log(
          `[tt] 获取视频 ${videoId} 的第 ${pageCount + 1} 页评论${cursor ? '(分页)' : '(首页)'}`,
        )

        const result = await api.getVideoComments({
          url: videoId,
          cursor,
          count: Math.min(pageSize, maxCommentCount - commentUserIdSet.size),
        })

        if (!result?.comments?.length) {
          console.log(`[tt] 视频 ${videoId} 没有更多评论`)
          break
        }

        console.log(
          `[tt] 视频 ${videoId} 第 ${pageCount + 1} 页获取到 ${result.comments.length} 条评论`,
        )

        for (const comment of result.comments) {
          if (comment.user && comment.user.id && !commentUserIdSet.has(comment.user.id)) {
            commentUserIdSet.add(comment.user.id)
            commentUserMap.set(comment.user.id, comment.user)
          }
        }

        if (
          result.hasMore &&
          pageCount < maxPages - 1 &&
          commentUserIdSet.size < maxCommentsCount
        ) {
          cursor = result.cursor
          pageCount++
          console.log(`[tt] 视频 ${videoId} 将获取第 ${pageCount + 1} 页评论`)
        } else {
          if (!result.hasMore) {
            console.log(`[tt] 视频 ${videoId} 没有更多评论页`)
          } else {
            console.log(`[tt] 视频 ${videoId} 达到最大页数限制或已收集足够用户`)
          }
          break
        }
      } catch (error) {
        console.error(`[tt] 获取视频 ${videoId} 评论失败:`, error)
        break
      }
    }

    const commentUsers = Array.from(commentUserMap.values())
    console.log(`[tt] 成功获取 ${commentUsers.length} 个评论者的用户信息`)

    return commentUsers
  } catch (error) {
    console.error('[tt] 获取TikTok视频评论者用户信息失败:', error)
    return []
  }
}

async function getPostAudience(videoId: string): Promise<AudienceAnalysisResult> {
  try {
    const api = TiktokApi.getInstance()
    const videoDetail = await api.getVideoDetail(videoId)

    if (!videoDetail) {
      throw new Error(`无法获取视频 ${videoId} 的详细信息`)
    }

    const videoCreateTime = videoDetail.create_time
    const authorUserId = videoDetail.author.id
    console.log(`[getPostAudience] 视频发布时间: ${videoCreateTime}, 作者ID: ${authorUserId}`)

    const [commentUsersResult, followersResult] = await Promise.allSettled([
      getVideosCommentsUsers(videoId),
      getFollowersAfterVideo(authorUserId, videoCreateTime),
    ])

    // 处理评论用户结果
    const commentUsers = commentUsersResult.status === 'fulfilled' ? commentUsersResult.value : []
    if (commentUsersResult.status === 'rejected') {
      console.error('[getPostAudience] 获取评论用户失败:', commentUsersResult.reason)
    }

    // 处理粉丝结果
    const followers = followersResult.status === 'fulfilled' ? followersResult.value : []
    if (followersResult.status === 'rejected') {
      console.error('[getPostAudience] 获取粉丝失败:', followersResult.reason)
    }

    console.log(`[getPostAudience] 获取了 ${commentUsers.length} 个评论用户数据`)
    console.log(`[getPostAudience] 获取了 ${followers.length} 个视频发布后的粉丝数据`)

    const userMap = new Map<string, TiktokCommentUser>()

    commentUsers.forEach((user) => {
      if (user.id) {
        userMap.set(user.id, user)
      }
    })

    followers.forEach((follower) => {
      if (follower.id && !userMap.has(follower.id)) {
        userMap.set(follower.id, follower as TiktokCommentUser)
      }
    })

    const mergedUsers = Array.from(userMap.values())
    console.log(`[getPostAudience] 合并去重后共 ${mergedUsers.length} 个用户数据`)

    if (!mergedUsers?.length) {
      return {
        userPortraitResult: {},
        regionAnalysisResult: {},
      } as AudienceAnalysisResult
    }

    const needAgeAndGenderAnalysisUsers = mergedUsers.slice(0, 300)

    const portraitPromise = getAuthorCommentsUsersPortrait(needAgeAndGenderAnalysisUsers)
    const regionPromise = statisticsUserRegion(mergedUsers)
    const [userPortraitResult, regionAnalysisResult] = await Promise.all([
      portraitPromise,
      regionPromise,
    ])

    return {
      userPortraitResult: formatToPercentage(userPortraitResult),
      regionAnalysisResult,
    } as AudienceAnalysisResult
  } catch (error) {
    console.error(`[getPostAudience] 视频 ${videoId} 受众分析失败:`, error)
    throw error
  }
}

export const TtInfoService = {
  getVideoCommentsUsersWithStatistics,
  getAuthorVideosCommentsUsers,
  getAuthorFollowers,
  statisticsCommentsUsers,
  getAuthorCommentsUsersPortrait,
  getAuthorAudienceAnalysis,
  getPostAudience,
  getVideosCommentsUsers,
}
