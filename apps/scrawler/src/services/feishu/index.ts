import TiktokApi from '@/api/tiktok'
import {
  emptyVideoRes,
  FeishuLinkType,
  FeishuPlatform,
  FeishuTrackRes,
  Unsupported,
} from '@/constants/feishu'
import { getTiktokUniqueId } from '@/utils/url'
import { prisma } from '@repo/database'
import countries from 'i18n-iso-countries'
import { ExternalLink, LinkType } from '../contact/contact'

export const quotaKeys = [
  'https://supertrack.com',
  'https://supertrack.com/',
  'http://supertrack.com',
  'http://supertrack.com/',
]

export class FeishuTrackService {
  /**
   * 记录请求日志
   */
  private async logRequest(
    requestId: string,
    apiKey: string,
    requestUrl: string,
    status: 'SUCCESS' | 'FAILED' | 'ERROR' | 'TIMEOUT' | 'RATE_LIMIT',
    options: {
      platform?: string
      linkType?: string
      accountName?: string
      displayName?: string
      responseCode?: number
      responseData?: any
      errorMessage?: string
      processingTime?: number
      quotaUsed?: number
      ipAddress?: string
      userAgent?: string
    } = {},
  ) {
    try {
      await prisma.feishuTrackLog.create({
        data: {
          requestId,
          apiKey,
          requestUrl,
          platform: options.platform,
          linkType: options.linkType,
          accountName: options.accountName,
          displayName: options.displayName,
          status,
          responseCode: options.responseCode,
          responseData: options.responseData,
          errorMessage: options.errorMessage,
          processingTime: options.processingTime,
          quotaUsed: options.quotaUsed || 1,
          ipAddress: options.ipAddress,
          userAgent: options.userAgent,
        },
      })
    } catch (error) {
      console.error('Failed to log feishu track request:', error)
    }
  }

  /**
   * 获取日志列表
   */
  async getLogs(params: {
    apiKey?: string
    platform?: string
    status?: string
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  }) {
    const { apiKey, platform, status, startDate, endDate, limit = 50, offset = 0 } = params

    const where: any = {}

    if (apiKey) where.apiKey = apiKey
    if (platform) where.platform = platform
    if (status) where.status = status
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const [logs, total] = await Promise.all([
      prisma.feishuTrackLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        select: {
          id: true,
          requestId: true,
          apiKey: true,
          requestUrl: true,
          platform: true,
          linkType: true,
          accountName: true,
          displayName: true,
          status: true,
          responseCode: true,
          errorMessage: true,
          processingTime: true,
          quotaUsed: true,
          ipAddress: true,
          createdAt: true,
          // 排除 responseData 和 userAgent 以减少数据量
        },
      }),
      prisma.feishuTrackLog.count({ where }),
    ])

    return {
      logs,
      total,
      limit,
      offset,
      hasMore: total > offset + limit,
    }
  }

  /**
   * 获取详细日志信息（包含完整响应数据）
   */
  async getLogDetail(requestId: string) {
    return await prisma.feishuTrackLog.findUnique({
      where: { requestId },
    })
  }

  /**
   * 获取统计信息
   */
  async getStatistics(params: { apiKey?: string; startDate?: Date; endDate?: Date }) {
    const { apiKey, startDate, endDate } = params

    const where: any = {}
    if (apiKey) where.apiKey = apiKey
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    // 基础统计
    const [
      totalRequests,
      successRequests,
      errorRequests,
      totalQuotaUsed,
      platformStats,
      avgProcessingTime,
    ] = await Promise.all([
      // 总请求数
      prisma.feishuTrackLog.count({ where }),

      // 成功请求数
      prisma.feishuTrackLog.count({
        where: { ...where, status: 'SUCCESS' },
      }),

      // 错误请求数
      prisma.feishuTrackLog.count({
        where: { ...where, status: 'ERROR' },
      }),

      // 总配额消耗
      prisma.feishuTrackLog.aggregate({
        where,
        _sum: { quotaUsed: true },
      }),

      // 按平台统计
      prisma.feishuTrackLog.groupBy({
        by: ['platform'],
        where,
        _count: { platform: true },
        _sum: { quotaUsed: true },
      }),

      // 平均处理时间
      prisma.feishuTrackLog.aggregate({
        where: { ...where, processingTime: { not: null } },
        _avg: { processingTime: true },
      }),
    ])

    const successRate = totalRequests > 0 ? (successRequests / totalRequests) * 100 : 0
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0

    return {
      totalRequests,
      successRequests,
      errorRequests,
      successRate: Math.round(successRate * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      totalQuotaUsed: totalQuotaUsed._sum.quotaUsed || 0,
      avgProcessingTime: avgProcessingTime._avg.processingTime || 0,
      platformStats: platformStats.map((stat) => ({
        platform: stat.platform,
        requests: stat._count.platform,
        quotaUsed: stat._sum.quotaUsed || 0,
      })),
    }
  }

  /**
   * 获取 API Key 使用统计
   */
  async getApiKeyStats(startDate?: Date, endDate?: Date) {
    const where: any = {}
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const stats = await prisma.feishuTrackLog.groupBy({
      by: ['apiKey'],
      where,
      _count: { apiKey: true },
      _sum: { quotaUsed: true },
      orderBy: { _count: { apiKey: 'desc' } },
    })

    return stats.map((stat) => ({
      apiKey: stat.apiKey,
      requests: stat._count.apiKey,
      quotaUsed: stat._sum.quotaUsed || 0,
    }))
  }

  async track(
    url: string,
    api_key: string,
    clientInfo?: { ipAddress?: string; userAgent?: string },
  ): Promise<FeishuTrackRes> {
    const startTime = Date.now()
    const uuid = crypto.randomUUID()

    try {
      if (quotaKeys.includes(url.toLowerCase())) {
        const result = await this.trackQuota(api_key)
        const processingTime = Date.now() - startTime

        // 记录配额查询日志
        await this.logRequest(uuid, api_key, url, 'SUCCESS', {
          platform: undefined,
          linkType: 'Quota',
          displayName: result.displayName,
          accountName: result.accountName,
          responseCode: 200,
          responseData: result,
          processingTime,
          quotaUsed: 0, // 配额查询不消耗配额
          ipAddress: clientInfo?.ipAddress,
          userAgent: clientInfo?.userAgent,
        })

        return result
      }

      const linkType = await ExternalLink.getLinkType(url)
      let result: FeishuTrackRes

      switch (linkType) {
        case LinkType.INSTAGRAM:
          result = Unsupported(uuid, api_key)
          break
        case LinkType.TIKTOK:
          result = await this.trackTiktok(uuid, url, api_key)
          break
        case LinkType.YOUTUBE:
          result = Unsupported(uuid, api_key)
          break
        case LinkType.INSTAGRAM_POST:
        case LinkType.INSTAGRAM_REEL:
          result = Unsupported(uuid, api_key)
          break
        default:
          result = Unsupported(uuid, api_key)
          break
      }

      const processingTime = Date.now() - startTime

      // 记录成功日志
      const log = await this.logRequest(uuid, api_key, url, 'SUCCESS', {
        platform: result.platform,
        linkType: result.linkType,
        accountName: result.accountName,
        displayName: result.displayName,
        responseCode: 200,
        responseData: result,
        processingTime,
        quotaUsed: result.useQuota ? 1 : 0,
        ipAddress: clientInfo?.ipAddress,
        userAgent: clientInfo?.userAgent,
      })

      return result
    } catch (error) {
      const processingTime = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)

      // 记录错误日志
      const log = await Promise.all([
        this.logRequest(uuid, api_key, url, 'ERROR', {
          errorMessage,
          processingTime,
          quotaUsed: 0, // 失败不消耗配额
          ipAddress: clientInfo?.ipAddress,
          userAgent: clientInfo?.userAgent,
        }),
        prisma.feishuShortcutApiKey.update({
          where: { apiKey: api_key },
          data: {
            remainingQuota: { increment: 1 },
          },
        }),
      ])
      throw error
    }
  }

  async trackTiktok(id: string, url: string, apiKey: string): Promise<FeishuTrackRes> {
    const uniqueId = getTiktokUniqueId(url)
    if (!uniqueId) {
      throw new Error('Invalid TikTok URL')
    }
    const ttUser = await TiktokApi.getInstance().getUserDetail({ unique_id: uniqueId })
    if (!ttUser) {
      throw new Error('User not found')
    }
    const videos = await TiktokApi.getInstance().getUserVideos({
      user_id: ttUser.user.id,
      count: 10,
      cursor: 0,
    })
    let publishedTime = 0,
      viewsCount = 0,
      likesCount = 0,
      country = '',
      medianER = 0

    if (videos && videos.length > 0) {
      // 按时间排序并取最新的10个视频
      const latestVideos = [...videos].sort((a, b) => b.create_time - a.create_time).slice(0, 10)

      // console.log('获取到的视频数量:', latestVideos.length)
      // console.log('视频数据示例:', JSON.stringify(latestVideos[0], null, 2))

      publishedTime = latestVideos[0].create_time
      const sortedViews = latestVideos.map((video) => video.play_count).sort((a, b) => a - b)
      const sortedLikes = latestVideos.map((video) => video.digg_count).sort((a, b) => a - b)
      viewsCount = sortedViews[Math.floor(sortedViews.length / 2)]
      likesCount = sortedLikes[Math.floor(sortedLikes.length / 2)]

      // console.log('播放数排序:', sortedViews)
      // console.log('点赞数排序:', sortedLikes)
      // console.log('中位数播放数:', viewsCount)
      // console.log('中位数点赞数:', likesCount)

      // 计算每个视频的 ER
      const videoERs = latestVideos
        .map((video) => {
          const er =
            (video.digg_count + video.comment_count + video.collect_count) / video.play_count
          // console.log(`视频 ${video.id} ER计算:`, {
          //   play_count: video.play_count,
          //   digg_count: video.digg_count,
          //   comment_count: video.comment_count,
          //   collect_count: video.collect_count,
          //   er: er,
          // })
          return er
        })
        .sort((a, b) => a - b)

      // console.log('所有视频ER值:', videoERs)
      medianER = videoERs[Math.floor(videoERs.length / 2)]
      // console.log('最终中位数ER:', medianER)

      const countryCount = latestVideos.reduce(
        (acc, video) => {
          if (video.region) {
            acc[video.region] = (acc[video.region] || 0) + 1
          }
          return acc
        },
        {} as Record<string, number>,
      )
      country = Object.entries(countryCount).sort(([, a], [, b]) => b - a)[0]?.[0] || ''
    }
    const countryName = (countries.getName(country, 'zh') as string) || ''
    return {
      id,
      kolUrl: url,
      linkType: FeishuLinkType.Kol,
      displayName: ttUser.user.nickname,
      accountName: ttUser.user.uniqueId,
      platform: FeishuPlatform.TIKTOK,
      country: countryName,
      email: ttUser.user.email || '',
      subscriberCount: ttUser.stats.followerCount,
      lastUpdatedTime: publishedTime * 1000,
      medianViewsCount: viewsCount,
      medianLikesCount: likesCount,
      medianER: medianER,
      ...emptyVideoRes,
      message: '✅ success',
      fetchTime: Date.now(),
      useQuota: 1,
      apiKey,
    }
  }

  async trackQuota(api_key: string): Promise<FeishuTrackRes> {
    const quota = await prisma.feishuShortcutApiKey.findUnique({
      where: {
        apiKey: api_key,
      },
    })
    if (!quota) {
      throw new Error('Quota info not found')
    }
    return {
      id: quota.id,
      kolUrl: '',
      displayName: `${quota.apiKey.startsWith('FREE') ? '免费' : ''}额度剩余:${quota.remainingQuota}`,
      accountName: quota.apiKey,
      platform: '',
      country: '',
      email: '',
      ...emptyVideoRes,
      message: '✅ success',
      fetchTime: Date.now(),
      linkType: '',
      useQuota: 0,
      apiKey: api_key,
    }
  }
}

export default new FeishuTrackService()
