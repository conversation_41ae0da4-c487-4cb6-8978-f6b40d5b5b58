import { CreateTagParams, GetTagsResponse } from '@/types/tag'
import { prisma } from '@repo/database'

const MAX_LABELS_PER_USER = 24
const MAX_LABEL_NAME_LENGTH = 30

export const tagService = {
  async createTag(userId: string, params: CreateTagParams) {
    const userLabelsCount = await prisma.tag.count({
      where: { createdBy: userId },
    })
    if (userLabelsCount >= MAX_LABELS_PER_USER) {
      throw new Error('Tag limit exceeded')
    }

    if (params.name.length > MAX_LABEL_NAME_LENGTH) {
      throw new Error('Tag name cannot exceed 30 characters')
    }

    const existingLabel = await prisma.tag.findUnique({
      where: { name: params.name },
    })
    if (existingLabel) {
      throw new Error('Tag name already exists')
    }

    return await prisma.tag.create({
      data: {
        name: params.name,
        color: params.color,
        createdBy: userId,
      },
    })
  },

  async deleteTag(userId: string, tagId: string) {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    if (!tag) {
      throw new Error('Tag not found or access denied')
    }

    return await prisma.tag.delete({
      where: { id: tagId },
    })
  },

  async getTags(userId: string): Promise<GetTagsResponse> {
    const items = await prisma.tag.findMany({
      where: { createdBy: userId },
      orderBy: { createdAt: 'desc' },
    })

    return {
      items: items.map((item) => ({
        id: item.id,
        tagId: item.id,
        name: item.name,
        color: item.color,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      })),
      total: items.length,
    }
  },
}
