import { SheetStatus, UserGoogleSheet, prisma } from '@repo/database'
import { User } from '@supabase/supabase-js'
import GoogleSheetService from './googleSheet.service'

// 获取用户 Google Sheet
export const getUserGoogleSheet = async (userId: string): Promise<UserGoogleSheet | null> => {
  return await prisma.userGoogleSheet.findUnique({
    where: { userId },
  })
}

// 初始化用户 Google Sheet
export const initUserGoogleSheet = async (user: User): Promise<UserGoogleSheet> => {
  try {
    const existingSheet = await prisma.userGoogleSheet.findUnique({
      where: { userId: user.id },
    })

    if (existingSheet) {
      console.log('用户已有 Google Sheet:', existingSheet)
      return existingSheet
    }

    const googleSheetService = GoogleSheetService.getInstance()
    const { spreadsheetId, shareableLink } = await googleSheetService.createNewSheet(
      user.email! || '',
    )

    // if (user.email) {
    //   await googleSheetService.setUserAccess(spreadsheetId, user.email, GoogleDriveRole.WRITER)
    // } else {
    //   console.warn('用户没有邮箱，无法设置编辑权限:', user.id)
    //   throw new Error('用户没有邮箱，无法设置编辑权限')
    // }

    const userGoogleSheet = await prisma.userGoogleSheet.create({
      data: {
        userId: user.id,
        spreadsheetId: spreadsheetId,
        sheetUrl: shareableLink,
        title: `EasyTrack-${user.email || user.id}`,
        status: SheetStatus.ACTIVE,
        lastSyncAt: new Date(),
      },
    })

    console.log('成功创建用户 Google Sheet:', {
      userId: user.id,
      spreadsheetId,
      sheetUrl: shareableLink,
    })

    return userGoogleSheet
  } catch (error) {
    console.error('初始化用户 Google Sheet 失败:', error)
    throw error
  }
}

// 判断用户是否已经初始化 Google Sheet
export const userGoogleSheetHasInited = async (user: User) => {
  const sheet = await getUserGoogleSheet(user.id)
  return !!sheet
}

// 如果用户没有初始化 Google Sheet，则初始化
export const initUserGoogleSheetIfNotInited = async (user: User) => {
  const sheet = await getUserGoogleSheet(user.id)
  if (!sheet) {
    return await initUserGoogleSheet(user)
  }
  return sheet
}

// 向用户的sheet 渲染最新的数据
export const renderUserGoogleSheetData = async (user: User) => {
  const userGoogleSheet = await getUserGoogleSheet(user.id)
  if (!userGoogleSheet) {
    return
  }

  const googleSheetService = GoogleSheetService.getInstance()
  await googleSheetService.updateSheet(userGoogleSheet.spreadsheetId)
}

// 主动保存
export const saveUserGoogleSheet = async (user: User) => {
  const userGoogleSheet = await getUserGoogleSheet(user.id)
  if (!userGoogleSheet) {
    return
  }
  const googleSheetService = GoogleSheetService.getInstance()
  await googleSheetService.syncPublicationCostData(userGoogleSheet.spreadsheetId)
}
