import { getChannelEmailFromNano } from '@/api/nano.ts'
import EmailService from '@/services/email.ts'
import { extractEmail } from '@/utils/email'
import { $Enums, EmailSender, KolInfo, prisma, UserInfo } from '@repo/database'
import { assert, beforeAll, describe, expect, it } from 'vitest'
import KolPlatform = $Enums.KolPlatform

describe('should find email for kol', () => {
  it('should extract email', () => {
    const source =
      '✨ Over 30 | Chic, practical looks\n💪 Athletic wear + lifestyle inspo\n🧒 Mom of 3 | AZ girl \n📩 Email <EMAIL>\n👇🏻 Shop my looks'
    const emails = extractEmail(source)
    console.log(emails)
  })
  describe('should find email for youtuber', () => {
    it(
      'should get email from nano',
      async () => {
        const kol = await prisma.kolInfo.findFirst({
          where: {
            platformAccount: 'mohamed_selva',
            platform: 'TIKTOK',
          },
        })
        expect(kol).toBeDefined()
        const email = await EmailService.getInstance().getKolEmail(undefined, kol!)
        expect(email.email).toBeTypeOf('string')
      },
      { timeout: 1000 * 60 },
    )

    it('should get email by nano', async () => {
      const channelId = 'UCLGe0PxyRFWmXVGJKq_gGvw'
      const email = await getChannelEmailFromNano(KolPlatform.YOUTUBE, channelId)
      expect(email.email).eq('<EMAIL>')
    })
  })
})

describe('should test send email', () => {
  let kol: KolInfo | null = null
  let emailSender: EmailSender | null = null
  let user: UserInfo | null = null

  beforeAll(async () => {
    // prepare kol
    const insUsername = 'callboblee'
    kol = await prisma.kolInfo.findFirst({
      where: {
        platformAccount: insUsername,
        platform: KolPlatform.INSTAGRAM,
      },
    })
    expect(kol).toBeDefined()
    expect(kol?.email).toBeDefined()

    // prepare email sender
    emailSender = await prisma.emailSender.findFirst({
      where: {
        email: '<EMAIL>',
      },
    })
    expect(emailSender).toBeDefined()

    // prepare user
    user = await prisma.userInfo.findFirst({
      where: {
        email: '<EMAIL>',
      },
    })
    expect(user).toBeDefined()
  })
  it(
    'should prepare sender',
    async () => {
      const sender = await EmailService.getInstance().prepareSenderClient(emailSender!)
      expect(sender).toBeDefined()
      const scopes = await EmailService.getInstance().getGmailScopes(user!.userId)
      console.log(scopes)
      assert(scopes.length > 0)
    },
    1000 * 60,
  )
})
