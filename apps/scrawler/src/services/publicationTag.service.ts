import { PaginationParams } from '@/types/pagination'
import { GetPublicationTagsResponse, PublicationTagResponse } from '@/types/publicationTag'
import { PaginationService } from '@/utils/pagination'
import { KolPlatform, prisma } from '@repo/database'
import assert from 'assert'

const MAX_TAGS_PER_PUBLICATION = 5

export interface GetPublicationsParams extends PaginationParams {
  userId?: string
  ids?: string[]
  platforms?: string[]
}

export const publicationTagService = {
  async validatePublicationOwnership(userId: string, publicationId: string): Promise<void> {
    const publication = await prisma.publicationStatisticsSheetData.findUnique({
      where: { id: publicationId },
      include: {
        userGoogleSheet: true,
      },
    })

    if (!publication) {
      throw new Error('Publication not found')
    }

    if (publication.userGoogleSheet.userId !== userId) {
      throw new Error('Access denied: You do not have permission to access this publication')
    }
  },

  async addTagToPublication(
    userId: string,
    publicationId: string,
    tagId: string,
  ): Promise<PublicationTagResponse> {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    if (!tag) {
      throw new Error('Tag not found or access denied')
    }

    const existingTag = await prisma.publicationTag.findUnique({
      where: {
        publicationId_tagId: {
          publicationId,
          tagId,
        },
      },
    })

    if (existingTag) {
      throw new Error('Tag already added to this publication')
    }

    const tagsCount = await prisma.publicationTag.count({
      where: {
        publicationId,
      },
    })

    if (tagsCount >= MAX_TAGS_PER_PUBLICATION) {
      throw new Error(`Maximum ${MAX_TAGS_PER_PUBLICATION} tags allowed per publication`)
    }

    const publicationTag = await prisma.publicationTag.create({
      data: {
        publicationId,
        tagId,
      },
    })

    return {
      id: publicationTag.id,
      tagId: publicationTag.tagId,
      publicationId: publicationTag.publicationId,
      name: tag.name,
      color: tag.color,
      createdAt: publicationTag.createdAt,
    } as PublicationTagResponse
  },

  async removeTagFromPublication(
    userId: string,
    publicationId: string,
    tagId: string,
  ): Promise<void> {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    if (!tag) {
      throw new Error('Tag not found or access denied')
    }

    const publicationTag = await prisma.publicationTag.findUnique({
      where: {
        publicationId_tagId: {
          publicationId,
          tagId,
        },
      },
    })

    if (!publicationTag) {
      throw new Error('Tag not found on this publication')
    }

    await prisma.publicationTag.delete({
      where: {
        publicationId_tagId: {
          publicationId,
          tagId,
        },
      },
    })
  },

  async getPublicationTags(userId: string, publicationId: string) {
    const publicationTags = await prisma.publicationTag.findMany({
      where: {
        publicationId,
        tag: {
          id: {
            not: undefined,
          },
        },
      },
      include: {
        tag: true,
      },
    })

    const tags: PublicationTagResponse[] = publicationTags
      .filter((pt) => pt.tag)
      .map((pt) => ({
        id: pt.id,
        tagId: pt.tagId,
        publicationId: pt.publicationId,
        name: pt.tag.name,
        color: pt.tag.color,
        createdAt: pt.createdAt,
      }))

    return {
      items: tags,
      total: tags.length,
    } as GetPublicationTagsResponse
  },

  async getTagPublications(userId: string, params: GetPublicationsParams) {
    const { ids = [], platforms = [], ...pagination } = params

    const userGoogleSheet = await prisma.userGoogleSheet.findFirst({
      where: {
        userId,
        status: 'ACTIVE',
      },
    })

    if (!userGoogleSheet) {
      throw new Error('No active Google Sheet configuration found')
    }

    // 验证所有标签是否存在且属于该用户
    if (ids.length > 0) {
      const tags = await prisma.tag.findMany({
        where: {
          id: {
            in: ids,
          },
          createdBy: userId,
        },
      })

      assert(tags.length === ids.length, new Error('Some tags not found or access denied'))
    }

    // 验证平台是否有效
    if (platforms.length > 0) {
      const invalidPlatforms = platforms.filter(
        (platform) => !Object.values(KolPlatform).includes(platform as KolPlatform),
      )
      if (invalidPlatforms.length > 0) {
        throw new Error(`Invalid platform parameters: ${invalidPlatforms.join(', ')}`)
      }
    }

    const { skip, pageSize, page } = PaginationService.handlePagination(pagination)

    const where = {
      AND: [
        {
          spreadsheetId: userGoogleSheet.spreadsheetId,
        },
        ...(ids.length > 0
          ? [
              {
                tags: {
                  some: {
                    tagId: {
                      in: ids,
                    },
                  },
                },
              },
            ]
          : []),
        ...(platforms.length > 0
          ? [
              {
                platform: {
                  in: platforms as KolPlatform[],
                },
              },
            ]
          : []),
      ],
    }

    const [publications, total] = await Promise.all([
      prisma.publicationStatisticsSheetData.findMany({
        where,
        include: {
          tags: {
            where: {
              tag: {
                id: {
                  not: undefined,
                },
              },
            },
            include: {
              tag: true,
            },
          },
          kol: true,
        },
        skip,
        take: pageSize,
        orderBy: {
          publishDate: 'desc',
        },
      }),
      prisma.publicationStatisticsSheetData.count({
        where,
      }),
    ])

    return PaginationService.handlePaginatedResponse(publications, total, page, pageSize)
  },

  async getAllPostLinks(userId: string, tagId: string) {
    const tag = await prisma.tag.findFirst({
      where: {
        id: tagId,
        createdBy: userId,
      },
    })

    assert(tag, new Error('Tag not found or access denied'))

    const postLinks = await prisma.publicationStatisticsSheetData.findMany({
      where: {
        postLink: {
          not: null,
        },
        tags: {
          some: {
            tagId: tagId,
          },
        },
      },
      select: {
        postLink: true,
        platform: true,
        publishDate: true,
      },
      orderBy: {
        publishDate: 'desc',
      },
    })

    return {
      items: postLinks,
      total: postLinks.length,
    }
  },
}
