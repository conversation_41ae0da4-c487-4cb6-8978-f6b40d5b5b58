import { IEmailPlanTask } from '@/infras/worker/bullmq'
import { logger } from '@/infras/worker/log4js'
import EmailService from '@/services/email'
import { EmailPlanStatus, prisma, SendStatus, SentEmailType } from '@repo/database'
import Bull from 'bull'

/**
 * 处理邮件跟进发送任务
 */
export async function processEmailFollowupJob(job: Bull.Job<IEmailPlanTask>) {
  const { emailPlanId, userId } = job.data
  logger.info(`处理邮件计划: ${emailPlanId}`)

  try {
    // 获取邮件计划详情
    const emailPlan = await prisma.emailPlan.findUnique({
      where: { id: emailPlanId },
      include: {
        thread: {
          include: {
            emailFollowup: {
              include: {
                emailTemplate: true,
              },
            },
          },
        },
      },
    })

    if (!emailPlan) {
      throw new Error(`邮件计划未找到: ${emailPlanId}`)
    }

    // 检查状态，避免重复发送
    if (emailPlan.status !== EmailPlanStatus.PENDING) {
      logger.info(`邮件计划 ${emailPlanId} 状态不是待发送，跳过`)
      return
    }

    // 获取发件人信息
    const sender = await prisma.emailSender.findFirst({
      where: {
        email: emailPlan.from,
        createdBy: userId,
      },
    })

    if (!sender) {
      throw new Error(`发件人未找到: ${emailPlan.from}`)
    }

    // 准备邮件客户端
    const emailService = EmailService.getInstance()
    const client = await emailService.prepareSenderClient(sender)

    // 检查权限
    const scopes = await EmailService.getInstance().getGmailScopes(userId)
    if (!scopes?.[0]?.actualScopes?.includes('https://www.googleapis.com/auth/gmail.send')) {
      throw new Error(`用户 ${userId} 没有权限发送邮件`)
    }

    const info = await prisma.emailPlan.findUnique({
      where: { id: emailPlanId },
      include: {
        thread: {
          include: {
            kol: true,
          },
        },
      },
    })

    // 发送邮件
    // 更新状态为发送中
    await prisma.$transaction(async (tx) => {
      await tx.emailPlan.update({
        where: { id: emailPlanId },
        data: { status: EmailPlanStatus.SENDING },
      })
      const sendResult = await client.sendEmail(
        {
          from: emailPlan.from,
          to: emailPlan.to,
          subject: emailPlan.subject,
          text: emailPlan.content,
          cc: emailPlan.cc,
          bcc: emailPlan.bcc,
          // threadId: emailPlan.threadId,
        },
        {
          nickname: info?.thread?.kol?.title || info?.thread?.kol?.platformAccount || '',
        },
      )

      // 更新状态为已发送
      await tx.emailPlan.update({
        where: { id: emailPlanId },
        data: {
          status: EmailPlanStatus.SENT,
          sendTime: new Date(),
          updatedAt: new Date(),
        },
      })

      await tx.emailRecord.create({
        data: {
          templateId: emailPlan.thread.emailFollowup.emailTemplateId,
          projectKolId: emailPlan.thread.kolId,
          type: SentEmailType.FOLLOW_UP,
          status: SendStatus.SENT,
          from: emailPlan.from,
          to: emailPlan.to,
          cc: emailPlan.cc,
          bcc: emailPlan.bcc,
          subject: emailPlan.subject,
          content: emailPlan.content,
          tags: [],
          resultObj: sendResult,
          sentAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
          score: 0,
        },
      })

      // 如果是跟进邮件序列的最后一封，更新线程状态
      const thread = emailPlan.thread
      if (thread) {
        const remainingPlans = await tx.emailPlan.count({
          where: {
            threadId: thread.id,
            status: EmailPlanStatus.PENDING,
          },
        })

        if (remainingPlans === 0) {
          await tx.emailFollowupThread.update({
            where: { id: thread.id },
            data: {
              status: 'COMPLETED',
              updatedAt: new Date(),
            },
          })
        }
      }

      logger.info(`成功发送邮件计划: ${emailPlanId}`)
      return sendResult
    })
  } catch (error) {
    logger.error(`发送邮件计划 ${emailPlanId} 失败:`, error)

    // 如果是最后一次重试，更新状态为失败
    if (job.attemptsMade === job.opts.attempts) {
      await prisma.emailPlan.update({
        where: { id: emailPlanId },
        data: {
          status: EmailPlanStatus.FAILED,
          updatedAt: new Date(),
        },
      })
    }

    throw error // 重新抛出错误以触发重试
  }
}
