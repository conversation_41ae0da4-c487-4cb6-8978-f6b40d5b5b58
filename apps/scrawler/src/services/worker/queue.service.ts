import { logTime } from '@/api/util'
import { InsMode, TtMode, TwitterMode, YtbMode } from '@/enums/TaskMode'
import { IEmbeddingTask } from '@/infras/worker/bullmq'
import { logger } from '@/infras/worker/log4js'
import { default as InstagramService } from '@/services/instagram.ts'
import TaskService from '@/services/task.ts'
import TiktokService from '@/services/tiktok.ts'
import TwitterService from '@/services/twitter.ts'
import YoutubeService from '@/services/youtube.ts'
import { ProjectCandidates } from '@/types/project'
import {
  InsHashTagBreakTaskParams,
  InsTaggedBreakTaskParams,
  SimilarTaskParams,
  TtFollowingListTaskParams,
  TtHashTagBreakTaskParams,
  TtSearchInputBreakTaskParams,
  YoutubeHashTagBreakTaskParams,
  YoutubeSearchInputBreakTaskParams,
} from '@/types/task.ts'
import { GetTaskParams, TaskParamsService } from '@/types/taskParams'
import {
  KolPlatform,
  Prisma,
  SimilarChannelTaskStatus,
  TaskReason,
  TaskType,
  prisma,
} from '@repo/database'
import Bull from 'bull'
import { InsInfoService } from '../insInfo.service'
import KolService from '../kol'
import { terminateLatestTaskByType } from '../similar'
import { TtInfoService } from '../ttInfo.service'
import { YtbInfoService } from '../ytbInfo.service'
import { handleBgmBreakComplete } from './bgmBreak'
import { handleEasyKOLTrackComplete, processEasyKOLTrackJob } from './easyTrack'
import { handleFollowingListComplete } from './followingList'
import { handleHashTagBreakComplete } from './hashTagBreak'
import { handleLongCrawlerComplete } from './longCrawler'
import { handlePostAudienceComplete } from './postAudience'
import { handleSearchInputBreakComplete } from './searchInputBreak'
import { handleTaggedBreakComplete } from './taggedBreak'
import { handleWebListComplete } from './webList'

export class QueueService {
  async processJob(job: Bull.Job<IEmbeddingTask>) {
    const { id, reason, type, createdAt, params } = job.data

    const projectId = TaskParamsService.getProjectId(params)

    if (projectId && reason === TaskReason.SEARCH) {
      await terminateLatestTaskByType(projectId, id, type as TaskType, createdAt)
    }
    switch (type) {
      case TaskType.SIMILAR:
        return await this.processSimilarJob(job)
      case TaskType.HASH_TAG_BREAK:
        return await this.processHashTagBreakJob(job)
      case TaskType.SEARCH_INPUT_BREAK:
        return await this.processSearchInputJob(job)
      case TaskType.FOLLOWERS_SIMILAR:
        return await this.processFollowersSimilarJob(job)
      case TaskType.FOLLOWING_LIST:
        return await this.processFollowingListJob(job)
      case TaskType.AUDIENCE_ANALYSIS:
        return await this.processAudienceAnalysisJob(job)
      case TaskType.EASYKOL_TRACK:
        return await processEasyKOLTrackJob(job)
      case TaskType.BGM_BREAK:
        return await this.processBgmBreakJob(job)
      case TaskType.WEB_LIST:
        return await this.processWebListJob(job)
      case TaskType.TAGGED_BREAK:
        return await this.processTaggedBreakJob(job)
      case TaskType.POST_AUDIENCE:
        return await this.processPostAudienceJob(job)
      case TaskType.LONG_CRAWLER:
        return await this.processLongCrawlerJob(job)
      case TaskType.AUDIENCE_FAKE:
        return await this.processAudienceFakeJob(job)
      default:
        throw new Error('[job' + job.id + ']unsupported task type:' + job.data.type)
    }
  }

  async processHashTagBreakJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'HASH_TAG_BREAK'>
    const platform = params.platform

    console.log(`[processTtHashTagBreakJob] 开始处理 HASH_TAG_BREAK 任务 ${id}`)
    let result: any
    switch (platform) {
      case KolPlatform.TIKTOK:
        result = await logTime(
          TiktokService.getInstance().tiktokProcessHashTagBreakJob(
            params as TtHashTagBreakTaskParams,
            id,
          ),
          '[sj:tiktok]tiktokProcessHashTagBreakJob',
        )
        console.log(
          `[processTtHashTagBreakJob] 任务 ${id} 处理完成，共处理 ${result.uniqueIds.length} 个用户`,
        )
        job.data.result = result
        return result
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InstagramService.getInstance().insProcessHashTagBreakJob(
            params as InsHashTagBreakTaskParams,
            id,
          ),
          '[sj:instagram]processHashTagBreakJob',
        )
        console.log(
          `[processInsHashTagBreakJob] 任务 ${id} 处理完成，共处理 ${result.uniqueIds.length} 个用户`,
        )
        job.data.result = result
        return result
      case KolPlatform.YOUTUBE:
        result = await logTime(
          YoutubeService.getInstance().youtubeProcessHashTagBreakJob(
            params as YoutubeHashTagBreakTaskParams,
            id,
          ),
          '[sj:youtube]processHashTagBreakJob',
        )
        console.log(
          `[processYoutubeHashTagBreakJob] 任务 ${id} 处理完成，共处理 ${result.length} 个频道`,
        )
        job.data.result = result
        return result
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processSearchInputJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'SEARCH_INPUT_BREAK'>
    const platform = params.platform

    console.log(`[processTtSearchInputJob] 开始处理 SEARCH_INPUT 任务 ${id}`)

    switch (platform) {
      case KolPlatform.TIKTOK: {
        const result = await logTime(
          TiktokService.getInstance().tiktokProcessSearchInputJob(
            params as TtSearchInputBreakTaskParams,
            id,
          ),
          '[sj:tiktok]tiktokProcessSearchInputJob',
        )
        console.log(
          `[processTtSearchInputJob] 任务 ${id} 处理完成，共处理 ${result.videoCount} 个视频,共获取到 ${result.totalAuthorCount} 个用户,过滤后获取到 ${result.afterFilterAuthorCount} 个用户`,
        )
        job.data.result = result
        return result
      }
      case KolPlatform.YOUTUBE: {
        const result = await logTime(
          YoutubeService.getInstance().youtubeProcessSearchInputBreakJob(
            params as YoutubeSearchInputBreakTaskParams,
            id,
          ),
          '[sj:youtube]youtubeProcessSearchInputBreakJob',
        )
        console.log(
          `[processTtSearchInputJob] 任务 ${id} 处理完成，共处理 ${result.total} 个视频,共获取到 ${result.channelIds.length} 个用户`,
        )
        job.data.result = result
        return result
      }

      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processFollowersSimilarJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'FOLLOWERS_SIMILAR'>
    const platform = params.platform

    switch (platform) {
      case KolPlatform.TIKTOK:
        const result = await logTime(
          TiktokService.getInstance().tiktokProcessFollowersSimilarJob(params, id),
          '[sj:tiktok]tiktokProcessFollowersSimilarJob',
        )
        job.data.result = result
        return result

      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processFollowingListJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'FOLLOWING_LIST'>
    const platform = params.platform
    console.log(`[processTtFollowingListJob] 开始处理 FOLLOWING_LIST 任务 ${id}`)
    let result: any
    switch (platform) {
      case KolPlatform.TIKTOK:
        result = await logTime(
          TiktokService.getInstance().tiktokProcessFollowingListJob(
            params as TtFollowingListTaskParams,
            id,
          ),
          '[sj:tiktok]tiktokProcessFollowingListJob',
        )
        job.data.result = result
        return result
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InstagramService.getInstance().insProcessFollowingListJob(job),
          '[sj:instagram]insProcessFollowingListJob',
        )
        job.data.result = result
        return result
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processAudienceAnalysisJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'AUDIENCE_ANALYSIS'>
    const platform = params.platform
    let result: any
    switch (platform) {
      case KolPlatform.TIKTOK:
        result = await logTime(
          TtInfoService.getAuthorAudienceAnalysis(params.source),
          '[sj:tiktok]getAuthorAudienceAnalysis',
        )
        break
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InsInfoService.getUserAudienceAnalysis(params.source, id),
          '[sj:instagram]getUserAudienceAnalysis',
        )
        break
      case KolPlatform.YOUTUBE:
        result = await logTime(
          YtbInfoService.getChannelAudienceAnalysis(params.source),
          '[sj:youtube]getChannelAudienceAnalysis',
        )
        break
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }

    job.data.result = result
    return result
  }

  async processBgmBreakJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'BGM_BREAK'>
    const platform = params.platform

    switch (platform) {
      case KolPlatform.TIKTOK:
        const result = await logTime(
          TiktokService.getInstance().tiktokProcessBgmBreakJob(params, id),
          '[sj:tiktok]tiktokProcessBgmBreakJob',
        )
        job.data.result = result
        return result

      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processWebListJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'WEB_LIST'>
    const platform = params.platform

    let result: any
    switch (platform) {
      case KolPlatform.TIKTOK:
        result = await logTime(
          TiktokService.getInstance().tiktokProcessWebListJob(params, id),
          '[sj:tiktok]tiktokProcessWebListJob',
        )
        job.data.result = result
        return result
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InstagramService.getInstance().insProcessWebListJob(params, id),
          '[sj:instagram]insProcessWebListJob',
        )
        job.data.result = result
        return result
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processTaggedBreakJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'TAGGED_BREAK'>
    const platform = params.platform

    console.log(`[processInsTaggedBreakJob] 开始处理 TAGGED_BREAK 任务 ${id}`)
    let result: any
    switch (platform) {
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InstagramService.getInstance().insProcessTaggedBreakJob(
            params as InsTaggedBreakTaskParams,
            id,
          ),
          '[sj:instagram]processTaggedBreakJob',
        )
        console.log(
          `[processInsTaggedBreakJob] 任务 ${id} 处理完成，共处理 ${result.usernames.length} 个用户`,
        )
        job.data.result = result
        return result
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
  }

  async processSimilarJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as SimilarTaskParams
    const platform = params.platform
    let kolInfoResults: Array<{
      kolId: string
      platform: KolPlatform
      platformId: string
      score: number
    }>

    switch (platform) {
      case KolPlatform.YOUTUBE: {
        switch (Number(params.ytbMode ?? YtbMode.EMBEDDING)) {
          case YtbMode.AI_VISUAL:
            console.log(`使用视觉相似性分析处理YouTube任务 ${id}`)
            kolInfoResults = await logTime(
              YoutubeService.getInstance().youtubeProcessSimilarJobByVisualSimilarity(job),
              '[sj:youtube]youtubeProcessSimilarJobByVisualSimilarity',
            )
            break
          case YtbMode.EMBEDDING:
          default:
            console.log(`使用嵌入向量处理YouTube任务 ${id}`)
            kolInfoResults = await logTime(
              YoutubeService.getInstance().youtubeProcessSimilarJob(job),
              '[sj:youtube]youtubeProcessSimilarJob',
            )
            break
        }
        break
      }
      case KolPlatform.TIKTOK: {
        console.log(`开始处理 TikTok 用户 ${params.source} 的标签视频以及联合标签搜索`)
        switch (Number(params.ttMode ?? TtMode.EMBEDDING)) {
          case TtMode.EMBEDDING:
            kolInfoResults = await TiktokService.getInstance().processSimilarJobByEmbedding(job)
            break
          case TtMode.AI_VISUAL:
            kolInfoResults =
              await TiktokService.getInstance().processSimilarJobByVisualSimilarity(job)
            break
          default:
            throw new Error(`不支持的策略: ${params.ttMode}`)
        }
        break
      }
      case KolPlatform.INSTAGRAM: {
        switch (Number(params.insMode ?? InsMode.NORMAL)) {
          case InsMode.NORMAL:
            kolInfoResults = await InstagramService.getInstance().processEmbeddingSimilarJob(job)
            break
          case InsMode.AI_VISUAL:
            kolInfoResults = await InstagramService.getInstance().processVisualSimilarJob(job)
            break
          default:
            throw new Error(`不支持的策略: ${params.insMode}`)
        }
        break
      }
      case KolPlatform.TWITTER: {
        switch (Number(params.twitterMode ?? TwitterMode.AI_VISUAL)) {
          case TwitterMode.AI_VISUAL:
            kolInfoResults = await TwitterService.getInstance().processVisualSimilarJob(job)
            break
          default:
            throw new Error(`不支持的策略: ${params.twitterMode}`)
        }
        break
      }
      default:
        throw new Error(`Unknown platform: ${params.platform}`)
    }

    const task = await prisma.similarChannelTask.findUniqueOrThrow({
      where: { id },
      include: {
        project: {
          select: {
            candidates: true,
          },
        },
      },
    })

    const taskMetrics = job.data.metrics.getMetrics() || {}
    await prisma.similarChannelTask.update({
      where: { id },
      data: {
        status: SimilarChannelTaskStatus.RESULT_READY,
        meta: taskMetrics,
        candidate: kolInfoResults as unknown as Prisma.InputJsonArray,
      },
    })

    const existingCandidates = (task.project?.candidates as any) || {}
    const existingKols = existingCandidates[task.type]?.kols || []
    const newKols = kolInfoResults

    const mergedKols = [...existingKols]
    for (const newKol of newKols) {
      if (!mergedKols.some((k) => k.platformId === newKol.platformId)) {
        mergedKols.push(newKol)
      }
    }

    const candidates = {
      [task.type]: {
        taskId: id,
        updatedAt: new Date(),
        kols: mergedKols,
      },
    } as ProjectCandidates
    await prisma.project.update({
      where: { id: task.projectId },
      data: {
        candidates: {
          ...existingCandidates,
          ...candidates,
        },
      },
    })

    if (platform !== KolPlatform.TWITTER) {
      KolService.getInstance()
        .fulfillEmailInfo(
          task.createdBy ?? undefined,
          kolInfoResults.map((i) => i.platformId),
          platform,
          task.projectId,
        )
        .catch(console.error)
    }
    // 设置结果
    job.data.result = {}
    return kolInfoResults
  }

  async processPostAudienceJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'POST_AUDIENCE'>
    const platform = params.platform
    let result: any
    switch (platform) {
      case KolPlatform.TIKTOK:
        result = await logTime(
          TtInfoService.getPostAudience(params.videoId),
          '[sj:tiktok]getPostAudience',
        )
        break
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InsInfoService.getPostAudience(params.videoId),
          '[sj:instagram]getPostAudience',
        )
        break
      case KolPlatform.YOUTUBE:
        result = await logTime(
          YtbInfoService.getPostAudience(params.videoId),
          '[sj:youtube]getVideoAudience',
        )
        break
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }
    job.data.result = result
    return result
  }

  async processLongCrawlerJob(job: Bull.Job<IEmbeddingTask>) {
    const { id } = job.data
    const params = job.data.params as GetTaskParams<'LONG_CRAWLER'>
    const platform = params.platform as KolPlatform

    logger.info(`[processLongCrawlerJob] 开始处理长时间爬取任务 ${id}, 平台: ${platform}`)

    // 根据平台选择不同的处理逻辑
    let result: any
    switch (platform) {
      case KolPlatform.INSTAGRAM: {
        result = await InstagramService.getInstance().processLongCrawlerJob(job)
        break
      }
      case KolPlatform.YOUTUBE: {
        result = await YoutubeService.getInstance().processLongCrawlerJob(job)
        break
      }
      case KolPlatform.TIKTOK:
        // TODO
        throw new Error(`TikTok平台的长时间爬取任务尚未实现`)
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }

    job.data.result = result
    return result
  }

  async processAudienceFakeJob(job: Bull.Job<IEmbeddingTask>) {
    const params = job.data.params as GetTaskParams<'AUDIENCE_FAKE'>
    const platform = params.platform
    let result: any
    switch (platform) {
      case KolPlatform.INSTAGRAM:
        result = await logTime(
          InsInfoService.getAudienceFake(job),
          '[sj:instagram]getUserAudienceAnalysis',
        )
        break
      default:
        throw new Error(`不支持的平台: ${platform}`)
    }

    job.data.result = result
    return result
  }

  async handleComplete(job: Bull.Job<IEmbeddingTask>) {
    const { id, type, result } = job.data

    let task: any
    try {
      switch (type as TaskType) {
        case TaskType.EASYKOL_TRACK:
          console.log(`[handleComplete] 处理 EASYKOL_TRACK 类型任务 ${id}`)
          await handleEasyKOLTrackComplete(job)
          return
        case TaskType.HASH_TAG_BREAK:
          console.log(`[handleComplete] 处理 HASH_TAG_BREAK 类型任务 ${id}`)
          await handleHashTagBreakComplete(job)
          return
        case TaskType.BGM_BREAK:
          console.log(`[handleComplete] 处理 BGM_BREAK 类型任务 ${id}`)
          await handleBgmBreakComplete(job)
          return
        case TaskType.WEB_LIST:
          console.log(`[handleComplete] 处理 WEB_LIST 类型任务 ${id}`)
          await handleWebListComplete(job)
          return
        case TaskType.TAGGED_BREAK:
          console.log(`[handleComplete] 处理 TAGGED_BREAK 类型任务 ${id}`)
          await handleTaggedBreakComplete(job)
          return
        case TaskType.SEARCH_INPUT_BREAK: {
          console.log(`[handleComplete] 处理 SEARCH_INPUT_BREAK 类型任务 ${id}`)
          await handleSearchInputBreakComplete(job)
          break
        }
        case TaskType.FOLLOWERS_SIMILAR: {
          console.log(`[handleComplete] 处理 FOLLOWERS_SIMILAR 类型任务 ${id}`)
          task = await prisma.similarChannelTask.update({
            where: { id },
            data: {
              status: SimilarChannelTaskStatus.RESULT_READY,
              result: result,
            },
          })

          if (!task) {
            throw new Error(`task ${id} not found`)
          }
          const uniqueIds = task.result.uniqueIds as string[]
          const projectId = task.projectId

          if (!uniqueIds || !projectId) {
            throw new Error(`task ${id} result缺少必要的数据 uniqueIds or projectId`)
          }

          const kolInfos = await prisma.kolInfo.findMany({
            where: {
              platform: KolPlatform.TIKTOK,
              platformAccount: { in: uniqueIds },
            },
          })

          console.log(
            `[handleComplete] FOLLOWERS_SIMILAR 任务 ${id} 找到 ${kolInfos.length} 个 KolInfo 记录`,
          )

          const candidatesData = kolInfos.map((kol) => ({
            kolId: kol.id,
            platform: KolPlatform.TIKTOK,
            platformId: kol.platformAccount,
          }))

          const metaData = {
            total: task.result.total,
            uniqueId: task.params.uniqueId,
            updatedAt: new Date(),
          }

          console.log(`[handleComplete] FOLLOWERS_SIMILAR 任务 ${id} 创建新记录`)
          // 不翻页,无需拼接之前的数据
          await prisma.projectCandidate.upsert({
            where: {
              projectId_type: {
                projectId: projectId,
                type: TaskType.FOLLOWERS_SIMILAR,
              },
            },
            update: {
              candidates: {
                kols: candidatesData,
              },
              meta: metaData,
            },
            create: {
              projectId: projectId,
              type: TaskType.FOLLOWERS_SIMILAR,
              candidates: {
                kols: candidatesData,
              },
              meta: metaData,
            },
          })

          await TaskService.getInstance().finishTask(id, result)
          console.log(
            `[handleComplete] FOLLOWERS_SIMILAR 任务 ${id} 处理完成，状态已更新为 COMPLETED`,
          )
          return
        }
        case TaskType.FOLLOWING_LIST:
          console.log(`[handleComplete] 处理 FOLLOWING_LIST 类型任务 ${id}`)
          await handleFollowingListComplete(job)
          return
        case TaskType.AUDIENCE_ANALYSIS: {
          console.log(`[handleComplete] 处理 AUDIENCE_ANALYSIS 类型任务 ${id}`)

          task = await prisma.similarChannelTask.update({
            where: { id },
            data: { result, status: SimilarChannelTaskStatus.RESULT_READY },
          })

          if (!task) {
            throw new Error(`task ${id} not found`)
          }

          const kolInfo = await KolService.getInstance().findOrCreateKol(
            task.createdBy,
            task.params.source,
            task.params.source,
            task.params.platform as KolPlatform,
          )

          if (!kolInfo) {
            throw new Error(`kolInfo ${task.params.source} not found`)
          }

          const audienceAnalysisResult = {
            ...result,
            kolId: kolInfo.id,
            platformAccount: task.params.source,
            platform: task.params.platform,
            createUserIds: [task.createdBy],
            createdAt: new Date(),
            updatedAt: new Date(),
          }

          // 更新kolInfo
          await prisma.kolInfo.update({
            where: { id: kolInfo.id },
            data: {
              audienceAnalysis: audienceAnalysisResult,
            },
          })

          await TaskService.getInstance().finishTask(id, result, audienceAnalysisResult)
          console.log(
            `[handleComplete] AUDIENCE_ANALYSIS 任务 ${id} 处理完成，状态已更新为 COMPLETED`,
          )
          return
        }
        case TaskType.POST_AUDIENCE: {
          console.log(`[handleComplete] 处理 POST_AUDIENCE 类型任务 ${id}`)
          await handlePostAudienceComplete(job)
          return
        }
        case TaskType.LONG_CRAWLER: {
          console.log(`[handleComplete] 处理 LONG_CRAWLER 类型任务 ${id}`)
          await handleLongCrawlerComplete(job)
          return
        }
        case TaskType.AUDIENCE_FAKE: {
          console.log(`[handleComplete] 处理 AUDIENCE_FAKE 类型任务 ${id}`)
          await TaskService.getInstance().finishTask(id, result)
          console.log(`[handleComplete] AUDIENCE_FAKE 任务 ${id} 处理完成，状态已更新为 COMPLETED`)
          return
        }
        case TaskType.KEYWORD:
        case TaskType.SIMILAR: {
          task = await prisma.similarChannelTask.update({
            where: { id },
            data: { result, status: SimilarChannelTaskStatus.RESULT_READY },
            include: {
              project: {
                select: {
                  candidates: true,
                },
              },
            },
          })
          if (!task) {
            throw new Error('task not found')
          }
          await TaskService.getInstance().finishTask(
            id,
            result,
            job.data.metrics.getMetrics() ?? {},
          )
          break
        }
        default:
          throw new Error(`Unknown task type: ${type} `)
      }
    } catch (err) {
      console.error(`[handleComplete] 处理任务 ${id} 失败:`, err)
      this.handleFailed(job, err as Error)
    }
  }

  async handleFailed(job: Bull.Job<IEmbeddingTask>, err: Error) {
    const { id } = job.data
    try {
      const errorObject = {
        message: err.message || '未知错误',
        stack: err.stack,
        name: err.name,
        timestamp: new Date().toISOString(),
        jobId: job.id,
        additionalInfo: {
          platform: job.data.platform,
          taskType: job.data.type,
          params: job.data.params,
        },
      }

      await TaskService.getInstance().failTask(id, errorObject)
    } catch (error) {
      await TaskService.getInstance().failTask(id, {
        message: '任务失败处理时发生错误',
        originalError: String(err),
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const queueService = new QueueService()
