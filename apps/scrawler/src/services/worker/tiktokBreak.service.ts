import { TikTokVideo } from '@/api/@types/rapidapi/Tiktok'
import TiktokApi from '@/api/tiktok'
import { TIKTOK_MAX_CONCURRENCY, TIKTOK_MAX_HASHTAGS_TO_PROCESS } from '@/config/env'
import { PopularTags } from '@/enums/PopularTags'
import Sentry from '@/infras/sentry'
import { generateTagsFromVideoCovers } from '@/services/aiTools/tt.keywords'
import { analyzeVisualSimilarityService } from '@/services/aiTools/visualSimilarity'
import {
  TtBgmBreakResult,
  TtHashTagBreakResult,
  TtSearchKeyWordBreakResult,
} from '@/types/breakResult'
import { EmailSourceType } from '@/types/email'
import { TtVideoBasicInfo } from '@/types/kol'
import { TtUserDetailsAndVideos } from '@/types/tiktokUsers'
import { extractEmail } from '@/utils/email'
import Bluebird from 'bluebird'

export async function getTikTokKeywords(allVideos: TikTokVideo[]): Promise<string[]> {
  try {
    if (!allVideos?.length) {
      console.warn('没有提供视频数据用于生成关键词')
      return []
    }
    const videosForAnalysis = allVideos.slice(0, 6).map((video) => ({
      cover: video.cover || video.origin_cover || video.ai_dynamic_cover,
      title: video.title || '',
    }))

    return await analyzeVisualSimilarityService.generateAuthorKeyWords(videosForAnalysis)
  } catch (error) {
    console.error('基于视觉分析生成关键词时发生错误:', error)
    return []
  }
}

export async function getTagsFromVideoCovers(
  videos: TikTokVideo[],
): Promise<Array<{ tagId: string; tagName: string }>> {
  try {
    if (videos.length === 0) {
      return []
    }

    const sortedVideos = [...videos].slice(0, 12).sort((a, b) => b.create_time - a.create_time)

    const videoData = sortedVideos.map((video) => ({
      cover: video.cover,
      title: video.title || '',
    }))

    const { tags } = await generateTagsFromVideoCovers(videoData)

    const tagsWithIds = await Bluebird.map(
      tags,
      async (tag) => {
        try {
          const hashtagInfo = await TiktokApi.getInstance().getHashTag(tag)
          return {
            tagName: tag,
            tagId: hashtagInfo?.id || '',
          }
        } catch (error) {
          console.warn(`标签 ${tag} 验证失败:`, error)
          return {
            tagName: tag,
            tagId: '',
          }
        }
      },
      { concurrency: +TIKTOK_MAX_CONCURRENCY },
    )

    return tagsWithIds.filter((tag) => tag.tagName && !tag.tagName.startsWith('fyp'))
  } catch (error) {
    console.error('从视频封面和标题生成标签时出错:', error)
    return []
  }
}

export async function getTikTokHashTagsWithIds(
  videoTitles: string[],
): Promise<Array<{ tagId: string; tagName: string }>> {
  try {
    if (videoTitles.length === 0) {
      return []
    }
    const hashtagCounts = new Map<string, number>()

    videoTitles.forEach((videoTitle) => {
      extractHashtags(videoTitle).forEach((tag) => {
        const lowerTag = tag.toLowerCase()
        if (!PopularTags.has(lowerTag) && !lowerTag.startsWith('fyp')) {
          hashtagCounts.set(tag, (hashtagCounts.get(tag) || 0) + 1)
        }
      })
    })

    const sortedHashtags = Array.from(hashtagCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .map((entry) => entry[0])

    const hashtagsWithIds = await Bluebird.map(
      sortedHashtags.slice(0, +TIKTOK_MAX_HASHTAGS_TO_PROCESS),
      async (tag) => {
        try {
          const hashtagInfo = await TiktokApi.getInstance().getHashTag(tag)
          return hashtagInfo ? { tagId: hashtagInfo.id, tagName: tag } : { tagId: '', tagName: tag }
        } catch (error) {
          console.warn(`标签 ${tag} 验证失败, 获取不到hashtagId:`, error)
          return { tagId: '', tagName: tag }
        }
      },
      { concurrency: +TIKTOK_MAX_CONCURRENCY },
    )

    return hashtagsWithIds.filter(
      (item): item is { tagId: string; tagName: string } => item !== null,
    )
  } catch (error) {
    return []
  }
}

export async function getUniqueIdsFromHashTagForTtHashTagBreak(
  hashtagId: string,
  initialCursor: number,
  maxVideoCount: number,
  options?: {
    ratedUniqueIds?: string[]
  },
): Promise<TtHashTagBreakResult> {
  try {
    const uniqueAuthors = new Set<string>()
    const batchSize = 30
    let currentCursor = initialCursor
    let more = true
    let msg = ''
    let ratedUniqueIdsSet: Set<string> | null = null
    let videoCount = 0
    if (options?.ratedUniqueIds && options.ratedUniqueIds.length > 0) {
      ratedUniqueIdsSet = new Set<string>(options.ratedUniqueIds)
    }

    while (videoCount < maxVideoCount && more) {
      try {
        const response = await TiktokApi.getInstance().getHashtagVideosWithResponse(
          hashtagId,
          batchSize,
          currentCursor,
        )

        if (!response) {
          more = false
          msg = '获取标签视频失败,response is null'
          break
        }

        if (response.code !== 0 || !response.data) {
          more = false
          msg = `获取标签视频失败,code: ${response.code}, message: ${response.msg || ''}`
          break
        }

        const { cursor, hasMore, videos } = response.data

        if (!videos || videos.length === 0) {
          more = hasMore
          if (!more) {
            msg = '没有更多视频'
            break
          }
          currentCursor = cursor
        } else {
          videos.forEach((video: TikTokVideo) => {
            const authorId = video.author.unique_id
            if (ratedUniqueIdsSet) {
              if (!ratedUniqueIdsSet.has(authorId)) {
                uniqueAuthors.add(authorId)
              }
            } else {
              uniqueAuthors.add(authorId)
            }
          })
          currentCursor = cursor
          more = hasMore
          videoCount += videos.length
        }
        console.log(
          `[getUniqueIdsFromHashTagForTtHashTagBreak] 标签 ${hashtagId} 当前获取到 ${uniqueAuthors.size} 个唯一作者`,
        )
        if (videoCount >= maxVideoCount) {
          msg = `获取uniqueIds成功, videos 数量达到 maxVideoCount: ${maxVideoCount},more: ${more}`
          break
        }
      } catch (error) {
        console.error(`获取标签 ${hashtagId} 的视频时出错: ${error}`)
        more = false
        msg = `获取标签 ${hashtagId} 的视频时出错: ${error}`
        break
      }
    }

    return {
      uniqueIds: Array.from(uniqueAuthors),
      cursor: currentCursor,
      hasMore: more,
      total: uniqueAuthors.size,
      message: msg,
    }
  } catch (error) {
    console.error(`处理标签列表时出错: ${error}`)
    return {
      uniqueIds: [],
      cursor: initialCursor,
      hasMore: false,
      total: 0,
      message: `处理标签列表时出错: ${error}`,
    }
  }
}

export async function getHashtagVideosFiltered(
  hashtagId: string,
  options?: {
    maxVideoCount?: number
    regions?: string[]
  },
): Promise<TikTokVideo[]> {
  try {
    const maxVideoCount = options?.maxVideoCount || 60
    const regions = options?.regions || []
    const maxPageCount = 30 // 最大翻页次数限制

    const videos: TikTokVideo[] = []
    const batchSize = 30
    let currentCursor = 0
    let more = true
    let pageCount = 0 // 翻页计数器

    while (videos.length < maxVideoCount && more && pageCount < maxPageCount) {
      pageCount++ // 增加翻页计数
      try {
        const response = await TiktokApi.getInstance().getHashtagVideosWithResponse(
          hashtagId,
          batchSize,
          currentCursor,
        )

        if (!response) {
          more = false
          console.log('获取标签视频失败,response is null')
          break
        }

        if (response.code !== 0 || !response.data) {
          more = false
          console.log(`获取标签视频失败,code: ${response.code}, message: ${response.msg || ''}`)
          break
        }

        const { cursor, hasMore, videos: newVideos } = response.data

        if (!newVideos || newVideos.length === 0) {
          more = hasMore
          if (!more) {
            console.log('没有更多视频')
            break
          }
          currentCursor = cursor
        } else {
          let filteredVideos = newVideos
          if (regions && regions.length > 0) {
            filteredVideos = newVideos.filter(
              (video) => video.region && regions.includes(video.region),
            )
            if (filteredVideos.length === 0) {
              console.log(`没有找到符合地区 ${regions.join(',')} 的视频，跳过本批视频`)
              currentCursor = cursor
              more = hasMore
              continue // 跳过这批视频，尝试下一批
            }
          }

          videos.push(...filteredVideos.slice(0, maxVideoCount - videos.length))

          currentCursor = cursor
          more = hasMore

          console.log(
            `[getHashtagVideosFiltered] 标签 ${hashtagId} 当前获取到 ${videos.length}/${maxVideoCount} 个视频，已翻页 ${pageCount}/${maxPageCount} 次`,
          )

          if (videos.length >= maxVideoCount) {
            console.log(`获取视频成功, 数量达到 maxVideoCount: ${maxVideoCount}`)
            break
          }
        }
      } catch (error) {
        console.error(`获取标签 ${hashtagId} 的视频时出错: ${error}`)
        more = false
        break
      }
    }

    if (pageCount >= maxPageCount && videos.length < maxVideoCount) {
      console.log(`已达到最大翻页次数 ${maxPageCount}，停止获取更多视频`)
    }

    return videos
  } catch (error) {
    console.error(`处理标签视频列表时出错: ${error}`)
    return []
  }
}

export async function getAuthorsWithVideos(uniqueIds: string[]): Promise<TtUserDetailsAndVideos[]> {
  try {
    const results = await Bluebird.map(
      uniqueIds,
      async (uniqueId) => {
        try {
          // 并行请求用户视频和用户详情
          const [videos, userDetail] = await Promise.all([
            TiktokApi.getInstance().getUserVideos({
              unique_id: uniqueId,
              count: 15,
              cursor: 0,
            }),
            TiktokApi.getInstance().getUserDetail({
              unique_id: uniqueId,
            }),
          ])

          if (!videos || videos.length === 0) {
            console.log(`用户 ${uniqueId} 没有视频`)
            return null
          }

          if (!userDetail) {
            console.log(`无法获取用户 ${uniqueId} 的详细信息`)
            return null
          }

          const basicVideos: TtVideoBasicInfo[] = videos
            .filter((video) => video.is_top === 0 && !video.is_ad)
            .map((video) => ({
              videoId: video.video_id,
              region: video.region || '',
              title: video.title,
              cover: video.cover,
              ai_dynamic_cover: video.ai_dynamic_cover,
              origin_cover: video.origin_cover,
              play: video.play,
              wmplay: video.wmplay,
              size: video.size,
              play_count: video.play_count,
              digg_count: video.digg_count,
              comment_count: video.comment_count,
              share_count: video.share_count,
              download_count: video.download_count,
              collect_count: video.collect_count,
              create_time: video.create_time,
              is_ad: video.is_ad,
            }))

          // handle email & email source
          let email = undefined
          let emailSource = undefined
          if (userDetail?.user?.bio_email) {
            email = userDetail.user.bio_email
            emailSource = EmailSourceType.REVEAL_BUTTON
          } else {
            const bioEmail = extractEmail(userDetail?.user?.signature ?? '')
            if (bioEmail) {
              email = bioEmail
              emailSource = EmailSourceType.BIO_EMAIL
            }
          }
          return {
            userId: userDetail.user.id,
            uniqueId: userDetail.user.uniqueId,
            nickname: userDetail.user.nickname,
            avatar: userDetail.user.avatarThumb,
            signature: userDetail.user.signature,
            followerCount: userDetail.stats.followerCount,
            followingCount: userDetail.stats.followingCount,
            heartCount: userDetail.stats.heartCount,
            videoCount: userDetail.stats.videoCount,
            verified: userDetail.user.verified,
            privateAccount: userDetail.user.privateAccount,
            instagramId: userDetail.user.ins_id,
            twitterId: userDetail.user.twitter_id,
            youtubeChannelTitle: userDetail.user.youtube_channel_title,
            youtubeChannelId: userDetail.user.youtube_channel_id,
            videos: basicVideos,
            email: email,
            emailSource: emailSource,
          } as TtUserDetailsAndVideos
        } catch (error) {
          console.error(`获取用户 ${uniqueId} 的信息时出错: ${error}`)
          Sentry.captureException(error)
          return null
        }
      },
      { concurrency: +TIKTOK_MAX_CONCURRENCY },
    )

    return results
      .filter((result): result is TtUserDetailsAndVideos => result !== null)
      .filter((item) => item.followerCount > 200)
  } catch (error) {
    console.error(`获取作者信息和视频时出错: ${error}`)
    Sentry.captureException(error)
    throw error
  }
}

/**
 * 获取单个TikTok用户的信息和视频
 * @param uniqueId TikTok用户的唯一ID
 * @returns 用户信息和视频，如果获取失败则返回null
 */
export async function getUserAndVideos(uniqueId: string): Promise<TtUserDetailsAndVideos | null> {
  try {
    console.log(`[getUserAndVideos] 开始获取用户 ${uniqueId} 的信息和视频`)

    // 并行请求用户视频和用户详情
    const [videos, userDetail] = await Promise.all([
      TiktokApi.getInstance().getUserVideos({
        unique_id: uniqueId,
        count: 20,
        cursor: 0,
      }),
      TiktokApi.getInstance().getUserDetail({
        unique_id: uniqueId,
      }),
    ])

    if (!videos || videos.length === 0) {
      console.log(`用户 ${uniqueId} 没有视频`)
      return null
    }

    if (!userDetail) {
      console.log(`无法获取用户 ${uniqueId} 的详细信息`)
      return null
    }

    const basicVideos: TtVideoBasicInfo[] = videos.map((video) => ({
      videoId: video.video_id,
      region: video.region || '',
      title: video.title,
      cover: video.cover,
      ai_dynamic_cover: video.ai_dynamic_cover,
      origin_cover: video.origin_cover,
      play: video.play,
      wmplay: video.wmplay,
      size: video.size,
      play_count: video.play_count,
      digg_count: video.digg_count,
      comment_count: video.comment_count,
      share_count: video.share_count,
      download_count: video.download_count,
      collect_count: video.collect_count,
      create_time: video.create_time,
      is_ad: video.is_ad,
    }))

    // 处理邮箱信息
    let email = undefined
    let emailSource = undefined
    if (userDetail?.user?.bio_email) {
      email = userDetail.user.bio_email
      emailSource = EmailSourceType.REVEAL_BUTTON
    } else {
      const bioEmail = extractEmail(userDetail?.user?.signature ?? '')
      if (bioEmail) {
        email = bioEmail
        emailSource = EmailSourceType.BIO_EMAIL
      }
    }

    const userWithVideos: TtUserDetailsAndVideos = {
      userId: userDetail.user.id,
      uniqueId: userDetail.user.uniqueId,
      nickname: userDetail.user.nickname,
      avatar: userDetail.user.avatarThumb,
      signature: userDetail.user.signature,
      followerCount: userDetail.stats.followerCount,
      followingCount: userDetail.stats.followingCount,
      heartCount: userDetail.stats.heartCount,
      videoCount: userDetail.stats.videoCount,
      verified: userDetail.user.verified,
      privateAccount: userDetail.user.privateAccount,
      instagramId: userDetail.user.ins_id,
      twitterId: userDetail.user.twitter_id,
      youtubeChannelTitle: userDetail.user.youtube_channel_title,
      youtubeChannelId: userDetail.user.youtube_channel_id,
      videos: basicVideos,
      email: email,
      emailSource: emailSource,
    }

    console.log(`[getUserAndVideos] 成功获取用户 ${uniqueId} 的信息和 ${basicVideos.length} 个视频`)
    return userWithVideos
  } catch (error) {
    console.error(`[getUserAndVideos] 获取用户 ${uniqueId} 的信息时出错:`, error)
    Sentry.captureException(error)
    return null
  }
}

export async function getTikTokSearchVideos(
  keywords: string,
  maxVideos: number,
  sortType: number = 0,
): Promise<TikTokVideo[]> {
  const allVideos: TikTokVideo[] = []
  let cursor = 0
  const batchSize = 30
  // 如果没有指定region，则按顺序尝试 us -> uk
  const regionsToTry = ['us', 'uk', 'ca', 'jp']

  for (const currentRegion of regionsToTry) {
    cursor = 0
    while (allVideos.length < maxVideos) {
      try {
        const result = await TiktokApi.getInstance().searchVideos({
          keywords,
          count: Math.min(batchSize, maxVideos - allVideos.length),
          cursor,
          region: currentRegion,
          sortType,
        })

        if (!result?.videos?.length) {
          break // 当前region没有结果，尝试下一个region
        }

        allVideos.push(...result.videos)
        cursor = result.cursor

        if (!result.hasMore) {
          break
        }
      } catch (error) {
        console.error(`在 ${currentRegion} 搜索关键词 "${keywords}" 时出错: ${error}`)
        Sentry.captureException(error)
        break
      }
    }

    if (allVideos.length > 0) {
      break
    }
  }

  console.log(
    `关键词 "${keywords}" 在 ${regionsToTry.join('/')} 共获取到 ${allVideos.length} 个视频`,
  )

  return allVideos.slice(0, maxVideos)
}

export async function getSearchVideosFiltered(
  keyword: string,
  options?: {
    maxVideoCount?: number
    regions?: string[]
  },
): Promise<TikTokVideo[]> {
  try {
    const maxVideoCount = options?.maxVideoCount || 60
    const regions = options?.regions || []
    const maxPageCount = 30 // 最大翻页次数限制

    const videos: TikTokVideo[] = []
    const batchSize = 30
    const sortType = 0

    const priorityRegions = ['us', 'uk', 'ca', 'jp']

    let regionsToTry: string[] = []

    for (const region of priorityRegions) {
      regionsToTry.push(region)
    }

    for (const region of regions) {
      if (!priorityRegions.includes(region)) {
        regionsToTry.push(region)
      }
    }

    regionsToTry = regionsToTry.slice(0, 6)
    console.log(`将按以下顺序尝试地区: ${regionsToTry.join(', ')}`)

    for (const currentRegion of regionsToTry) {
      let cursor = 0 // 对每个region都从0开始
      let more = true // 对每个region维护一个more状态
      let pageCount = 0 // 每个region的翻页计数器

      while (videos.length < maxVideoCount && more && pageCount < maxPageCount) {
        pageCount++ // 增加翻页计数
        try {
          const result = await TiktokApi.getInstance().searchVideos({
            keywords: keyword,
            count: Math.min(batchSize, maxVideoCount - videos.length),
            cursor,
            region: currentRegion,
            sortType,
          })

          if (!result?.videos?.length) {
            console.log(`在 ${currentRegion} 搜索关键词 "${keyword}" 未找到视频`)
            more = false
            break // 当前region没有结果，尝试下一个region
          }

          let filteredVideos = result.videos
          if (regions && regions.length > 0) {
            filteredVideos = result.videos.filter(
              (video) => video.region && regions.includes(video.region),
            )
            if (filteredVideos.length === 0) {
              console.log(`没有找到符合地区 ${regions.join(',')} 的视频，尝试下一批视频`)
              cursor = result.cursor
              more = result.hasMore
              if (!more) {
                console.log(`在 ${currentRegion} 没有更多视频，尝试下一个地区`)
                break // 没有更多视频，尝试下一个region
              }
              continue // 继续尝试下一批视频
            }
          }

          videos.push(...filteredVideos.slice(0, maxVideoCount - videos.length))
          cursor = result.cursor
          more = result.hasMore

          console.log(
            `[getSearchVideosFiltered] 关键词 "${keyword}" 在 ${currentRegion} 当前获取到 ${videos.length}/${maxVideoCount} 个视频，已翻页 ${pageCount}/${maxPageCount} 次`,
          )

          if (videos.length >= maxVideoCount) {
            console.log(`获取视频成功, 数量达到 maxVideoCount: ${maxVideoCount}`)
            break
          }

          if (!more) {
            console.log(`在 ${currentRegion} 没有更多视频，尝试下一个地区`)
            break
          }
        } catch (error) {
          console.error(`在 ${currentRegion} 搜索关键词 "${keyword}" 时出错: ${error}`)
          more = false
          break
        }
      }

      if (pageCount >= maxPageCount) {
        console.log(`在 ${currentRegion} 已达到最大翻页次数 ${maxPageCount}，尝试下一个地区`)
      }

      if (videos.length >= maxVideoCount) {
        break
      }
    }

    console.log(`关键词 "${keyword}" 最终获取到 ${videos.length} 个视频`)
    return videos
  } catch (error) {
    console.error(`处理关键词搜索时出错: ${error}`)
    return []
  }
}

export async function getTikTokSearchVideosForKeywordBreak(
  keyword: string,
  cursor: number,
  sortType: number = 0,
  publishTime: number = 0,
  maxVideoCount: number = 100,
  options?: {
    ratedUniqueIds?: string[]
  },
): Promise<TtSearchKeyWordBreakResult> {
  try {
    const uniqueAuthors = new Set<string>()
    const batchSize = 30
    let currentCursor = cursor
    let more = true
    let msg = ''
    let videoCount = 0
    const regionsToTry = ['us', 'uk', 'ca', 'jp']

    const ratedUniqueIdsSet = new Set<string>()
    if (options?.ratedUniqueIds && options.ratedUniqueIds.length > 0) {
      options.ratedUniqueIds.forEach((uniqueId) => {
        ratedUniqueIdsSet.add(uniqueId)
      })
    }

    for (const currentRegion of regionsToTry) {
      try {
        while (videoCount < maxVideoCount && more) {
          const result = await TiktokApi.getInstance().searchVideos({
            keywords: keyword,
            count: Math.min(batchSize, maxVideoCount - videoCount),
            cursor: currentCursor,
            region: currentRegion,
            sortType,
            publishTime,
          })

          if (!result?.videos?.length) {
            more = false
            msg = `在 ${currentRegion} 搜索关键词 "${keyword}" 未找到视频`
            break
          }

          result.videos.forEach((video) => {
            if (ratedUniqueIdsSet.size > 0) {
              if (!ratedUniqueIdsSet.has(video.author.unique_id)) {
                uniqueAuthors.add(video.author.unique_id)
              }
            }
          })

          videoCount += result.videos.length
          currentCursor = result.cursor
          more = result.hasMore

          console.log(
            `关键词 "${keyword}" 在 ${currentRegion} 当前获取到 ${videoCount}/${maxVideoCount} 个视频`,
          )

          if (!result.hasMore || videoCount >= maxVideoCount) {
            break
          }
        }

        if (videoCount >= maxVideoCount) {
          msg = `获取uniqueIds成功,uniqueIds数量达到maxUniqueAuthors: <AUTHORS>
          break
        }
      } catch (error) {
        console.error(`在 ${currentRegion} 搜索关键词 "${keyword}" 时出错: ${error}`)
        Sentry.captureException(error)
        continue // 出错时尝试下一个region
      }
    }

    if (videoCount === 0) {
      msg = `关键词 "${keyword}" 在所有地区都未找到视频`
      return {
        uniqueIds: [],
        cursor: currentCursor,
        hasMore: false,
        total: 0,
        videoCount: 0,
        message: msg,
        progress: {
          videoCount: 0,
        },
      }
    }

    console.log(`关键词 "${keyword}" 最终获取到 ${videoCount} 个视频`)

    return {
      uniqueIds: Array.from(uniqueAuthors),
      cursor: currentCursor,
      hasMore: more,
      total: uniqueAuthors.size,
      videoCount,
      message: msg,
      progress: {
        videoCount,
      },
    }
  } catch (error) {
    console.error(`处理关键词搜索时出错: ${error}`)
    return {
      uniqueIds: [],
      cursor: cursor,
      hasMore: false,
      total: 0,
      videoCount: 0,
      message: `处理关键词搜索时出错: ${JSON.stringify(error)}`,
      progress: {
        videoCount: 0,
      },
    }
  }
}

// 专用于BGM爆破任务的函数，支持分页和项目去重
export async function getUniqueIdsFromMusicForTtBgmBreak(
  musicId: string,
  initialCursor: number,
  maxUniqueAuthors: <AUTHORS>
  options?: {
    ratedUniqueIds?: string[]
  },
): Promise<TtBgmBreakResult> {
  try {
    const uniqueAuthors = new Set<string>()
    const batchSize = 30
    let currentCursor = initialCursor
    let more = true
    let msg = ''
    let ratedUniqueIdsSet: Set<string> | null = null

    if (options?.ratedUniqueIds && options.ratedUniqueIds.length > 0) {
      ratedUniqueIdsSet = new Set<string>(options.ratedUniqueIds)
    }

    while (uniqueAuthors.size < maxUniqueAuthors && more) {
      try {
        const response = await TiktokApi.getInstance().getMusicPosts(
          musicId,
          batchSize,
          currentCursor,
        )

        if (!response) {
          more = false
          msg = '获取音乐视频失败,response is null'
          break
        }

        if (response.code !== 0 || !response.data) {
          more = false
          msg = `获取音乐视频失败,code: ${response.code}, message: ${response.msg || ''}`
          break
        }

        const { cursor, hasMore, videos } = response.data

        if (!videos?.length) {
          more = hasMore
          if (!more) {
            msg = '没有更多视频'
            break
          }
          currentCursor = cursor
        } else {
          videos.forEach((video: TikTokVideo) => {
            const authorId = video.author.unique_id
            if (ratedUniqueIdsSet) {
              if (!ratedUniqueIdsSet.has(authorId)) {
                uniqueAuthors.add(authorId)
              }
            } else {
              uniqueAuthors.add(authorId)
            }
          })
          currentCursor = cursor
          more = hasMore
        }
        console.log(
          `[getUniqueIdsFromMusicForTtBgmBreak] 音乐 ${musicId} 当前获取到 ${uniqueAuthors.size} 个唯一作者`,
        )
        if (uniqueAuthors.size >= maxUniqueAuthors) {
          msg = `获取uniqueIds成功,uniqueIds数量达到maxUniqueAuthors: <AUTHORS>
          break
        }
      } catch (error) {
        console.error(`获取音乐 ${musicId} 的视频时出错: ${error}`)
        more = false
        msg = `获取音乐 ${musicId} 的视频时出错: ${error}`
        break
      }
    }

    return {
      uniqueIds: Array.from(uniqueAuthors),
      cursor: currentCursor,
      hasMore: more,
      total: uniqueAuthors.size,
      message: msg,
    }
  } catch (error) {
    console.error(`处理音乐列表时出错: ${error}`)
    return {
      uniqueIds: [],
      cursor: initialCursor,
      hasMore: false,
      total: 0,
      message: `处理音乐列表时出错: ${error}`,
    }
  }
}

// 获取hashtag下的视频
export async function getTikTokVideosInHashtag(
  hashtags: string[],
  maxVideosPerTag: number = 300,
): Promise<TikTokVideo[]> {
  try {
    // 并发获取所有hashtag的信息
    const hashtagInfos = await Bluebird.map(
      hashtags,
      async (tag) => {
        const info = await TiktokApi.getInstance().getHashTag(tag)
        return info && info.id ? { tag, id: info.id } : null
      },
      { concurrency: +TIKTOK_MAX_CONCURRENCY },
    )

    // 过滤掉无效的hashtag
    const validHashtags = hashtagInfos.filter(
      (info): info is { tag: string; id: string } => info !== null,
    )
    if (validHashtags.length === 0) {
      console.log('没有找到有效的Hashtag')
      return []
    }

    // 并发获取所有hashtag下的视频
    const allVideos = await Bluebird.map(
      validHashtags,
      async ({ tag, id }) => {
        const videos: TikTokVideo[] = []
        let cursor = 0
        const batchSize = 30
        let hasMore = true

        while (hasMore && videos.length < maxVideosPerTag) {
          try {
            const remainingCount = maxVideosPerTag - videos.length
            const batch = await TiktokApi.getInstance().getHashtagVideos(
              id,
              Math.min(batchSize, remainingCount),
              cursor,
            )

            if (!batch || batch.length === 0) {
              console.log(`标签 ${tag} 没有更多视频了，当前共 ${videos.length} 个`)
              hasMore = false
              break
            }

            videos.push(...batch)
            cursor += batch.length
            console.log(`标签 ${tag} 当前获取到 ${videos.length}/${maxVideosPerTag} 个视频`)
          } catch (error) {
            console.error(`获取标签 ${tag} 的视频时出错: ${error}`)
            Sentry.captureException(error)
            hasMore = false
            break
          }
        }

        console.log(`标签 ${tag} 最终获取到 ${videos.length} 个视频`)
        return videos
      },
      { concurrency: +TIKTOK_MAX_CONCURRENCY },
    )

    // 添加返回语句，合并所有视频
    return allVideos.flat()
  } catch (error) {
    console.error(`处理标签时出错: ${error}`)
    Sentry.captureException(error)
    return []
  }
}

// 判断某个tag是否存在
export async function isHashtagExists(hashtag: string): Promise<boolean> {
  const result = await TiktokApi.getInstance().getHashTag(hashtag)
  console.log(`标签 ${hashtag} 存在: ${result !== null}`)
  if (!result || result?.id === '') {
    return false
  }
  return true
}

// 辅助函数：从文本中提取hashtag
function extractHashtags(text: string): string[] {
  // 使用更宽松的正则表达式，支持日文、中文等Unicode字符
  const regex = /#([^\s#]+)/g
  const matches = text.match(regex)
  return matches ? matches.map((tag) => tag.slice(1)) : []
}
