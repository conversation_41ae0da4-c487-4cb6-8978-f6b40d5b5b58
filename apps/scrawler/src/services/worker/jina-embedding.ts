import { openai } from '@/api/openai'
import { JINA_API_KEY } from '@/config/env'
import retry from 'async-retry'

interface JinaEmbeddingResponse {
  model: string
  object: string
  usage: {
    total_tokens: number
  }
  data: Array<{
    object: string
    index: number
    embedding: number[]
  }>
}

// 简化的输入输出接口
interface BloggerTextInput {
  bloggerId: string
  text: string
}

interface BloggerEmbeddingOutput {
  bloggerId: string
  embedding: number[]
}

async function getJinaEmbeddingFromAiHubMix(text: string): Promise<number[]> {
  const startTime = Date.now()
  try {
    console.log(`[AiHubMix] 🔄 使用 AiHubMix 兜底服务生成 embedding (文本长度: ${text.length})`)

    const response = await openai.embeddings.create({
      model: 'jina-embeddings-v4',
      input: [text],
      encoding_format: 'float',
    })

    if (!response.data || !response.data[0] || !response.data[0].embedding) {
      throw new Error('Invalid response from AiHubMix API')
    }

    const embedding = response.data[0].embedding
    const duration = Date.now() - startTime
    console.log(`[AiHubMix] ✅ 兜底服务成功！维度: ${embedding.length}, 耗时: ${duration}ms`)

    if (embedding.length !== 2048) {
      console.warn(`[AiHubMix] 警告：期望2048维向量，但得到${embedding.length}维`)
    }

    return embedding
  } catch (error) {
    const duration = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error(`[AiHubMix] ❌ 兜底服务也失败，耗时: ${duration}ms, 错误: ${errorMessage}`)
    throw error
  }
}

// 预处理单个文本
function preprocessText(text: string): string {
  if (!text || text.trim() === '') {
    return ''
  }

  let processedText = text
    .replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu, '')
    .replace(/[^\p{L}\p{N}\s.,?!-]/gu, ' ')
    .replace(/\s+/g, ' ')
    .trim()

  // 避免 413 错误
  if (processedText.length > 8000) {
    processedText = processedText.substring(0, 8000)
    console.log(`[JinaEmbedding] 文本被截断到 8000 字符`)
  }

  return processedText
}

export async function getJinaEmbedding(
  text: string,
  task: string = 'text-matching',
): Promise<number[]> {
  const processedText = preprocessText(text)

  if (processedText.length === 0) {
    return []
  }

  try {
    return await retry(
      async () => {
        const response = await fetch('https://api.jina.ai/v1/embeddings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${JINA_API_KEY}`,
          },
          body: JSON.stringify({
            model: 'jina-embeddings-v4',
            task: task,
            truncate: true,
            late_chunking: false,
            truncate_dim: null,
            input: [{ text: processedText }],
          }),
        })

        if (!response.ok) {
          const errorText = await response.text()
          throw new Error(`Jina Embedding API error: ${response.status} - ${errorText}`)
        }
        console.log(`[JinaEmbedding] 成功请求 Jina Embedding API`)
        const data: JinaEmbeddingResponse = await response.json()

        if (!data.data || !data.data[0] || !data.data[0].embedding) {
          throw new Error('Invalid response from Jina Embedding API')
        }

        const embedding = data.data[0].embedding
        if (embedding.length !== 2048) {
          console.warn(`警告：期望2048维向量，但得到${embedding.length}维`)
        }

        return embedding
      },
      {
        retries: 2,
        factor: 2,
        minTimeout: 1_000,
        maxTimeout: 10_000,
        onRetry: (error: Error, attempt) => {
          console.warn(`[JinaEmbedding] 重试 Jina API，错误: ${error.message}，尝试 ${attempt}`)
        },
      },
    )
  } catch (jinaError) {
    const jinaErrorMessage = jinaError instanceof Error ? jinaError.message : String(jinaError)
    console.error(`[JinaEmbedding] 🚨 Jina API 完全失败，启动 AiHubMix 兜底服务`)
    console.error(`[JinaEmbedding] Jina 错误详情: ${jinaErrorMessage.substring(0, 200)}...`)

    try {
      return await getJinaEmbeddingFromAiHubMix(processedText)
    } catch (aihubmixError) {
      const aihubmixErrorMessage =
        aihubmixError instanceof Error ? aihubmixError.message : String(aihubmixError)
      console.error(`[JinaEmbedding] 💥 双重失败！Jina 和 AiHubMix 都不可用`)
      throw new Error(
        `Both Jina API and AiHubMix fallback failed. Jina: ${jinaErrorMessage}, AiHubMix: ${aihubmixErrorMessage}`,
      )
    }
  }
}

export async function getEmbeddingForInstagram(text: string): Promise<number[]> {
  return getJinaEmbedding(text, 'text-matching')
}

/**
 * 🎯 简单高效的批量 embedding 处理函数
 * 输入: { bloggerId, text }[]
 * 输出: { bloggerId, embedding }[]
 */
export async function getBatchEmbeddingForBloggers(
  inputs: BloggerTextInput[],
): Promise<BloggerEmbeddingOutput[]> {
  if (inputs.length === 0) {
    return []
  }

  // 过滤有效文本，保持映射关系
  const validInputs = inputs
    .map((input, index) => ({
      ...input,
      originalIndex: index,
      processedText: preprocessText(input.text),
    }))
    .filter((item) => item.processedText.length > 0)

  if (validInputs.length === 0) {
    console.warn('[JinaEmbedding] 所有文本都为空')
    return inputs.map((input) => ({ bloggerId: input.bloggerId, embedding: [] }))
  }

  console.log(`[JinaEmbedding] 开始处理 ${validInputs.length}/${inputs.length} 个有效文本`)

  try {
    // 调用 Jina API
    const response = await fetch('https://api.jina.ai/v1/embeddings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${JINA_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'jina-embeddings-v4',
        task: 'text-matching',
        truncate: true,
        late_chunking: false,
        truncate_dim: null,
        input: validInputs.map((item) => ({ text: item.processedText })),
      }),
    })

    if (!response.ok) {
      throw new Error(`Jina API error: ${response.status}`)
    }

    const data: JinaEmbeddingResponse = await response.json()

    // 简单排序，按 index 排列
    const sortedEmbeddings = data.data
      .sort((a, b) => a.index - b.index)
      .map((item) => item.embedding)

    // 构建结果数组
    const results: BloggerEmbeddingOutput[] = inputs.map((input) => ({
      bloggerId: input.bloggerId,
      embedding: [],
    }))

    // 将有效的 embedding 映射回对应位置
    validInputs.forEach((validInput, validIndex) => {
      results[validInput.originalIndex].embedding = sortedEmbeddings[validIndex]
      console.log(
        `[JinaEmbedding] ${validInput.bloggerId}: embedding[${sortedEmbeddings[validIndex].length}维]`,
      )
    })

    console.log(`[JinaEmbedding] ✅ 处理完成`)
    return results
  } catch (jinaError) {
    console.error('[JinaEmbedding] Jina API 失败，尝试 AiHubMix 兜底')

    try {
      // AiHubMix 兜底
      const response = await openai.embeddings.create({
        model: 'jina-embeddings-v4',
        input: validInputs.map((item) => item.processedText),
        encoding_format: 'float',
      })

      // 简单排序，按 index 排列
      const sortedEmbeddings = response.data
        .sort((a, b) => a.index - b.index)
        .map((item) => item.embedding)

      // 构建结果数组
      const results: BloggerEmbeddingOutput[] = inputs.map((input) => ({
        bloggerId: input.bloggerId,
        embedding: [],
      }))

      // 将有效的 embedding 映射回对应位置
      validInputs.forEach((validInput, validIndex) => {
        results[validInput.originalIndex].embedding = sortedEmbeddings[validIndex]
        console.log(
          `[AiHubMix] ${validInput.bloggerId}: embedding[${sortedEmbeddings[validIndex].length}维]`,
        )
      })

      console.log(`[AiHubMix] ✅ 兜底处理完成`)
      return results
    } catch (aihubmixError) {
      console.error('[JinaEmbedding] 双重失败')
      throw new Error(`Both Jina and AiHubMix failed`)
    }
  }
}
