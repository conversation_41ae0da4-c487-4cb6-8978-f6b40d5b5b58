import { GOOG<PERSON>_PRIVATE_KEY, GOOGLE_PROJECT_ID, GOOGLE_SERVICE_ACCOUNT_EMAIL } from '@/config/env'
import { EDITABLE_FIELDS, POST_DATA_SHEET_HEADER } from '@/config/googleSheetStyle/postDataConf'
import {
  applyPostDataSheetDataRowsStyle,
  getPostDataSheetStyle,
} from '@/config/googleSheetStyle/sheetSchema/postDataSheet.schema'
import { FileCapability, GoogleDrivePermissionType, GoogleDriveRole } from '@/enums/GoogleSheet'
import { SheetInfo, SpreadsheetInfo } from '@/types/googleSheet'
import { formatCountryData } from '@/utils/country'
import { prisma } from '@repo/database'
import Bluebird from 'bluebird'
import { JWT } from 'google-auth-library'
import { google, sheets_v4 } from 'googleapis'
import { SHEETS_CONFIG } from '../config/googleSheetStyle/allSheetsConfig'

export class GoogleSheetService {
  private static instance: GoogleSheetService
  private auth: JWT | null = null

  private constructor() {
    process.env.NODE_OPTIONS = '--openssl-legacy-provider'
  }

  public static getInstance(): GoogleSheetService {
    if (!GoogleSheetService.instance) {
      GoogleSheetService.instance = new GoogleSheetService()
    }
    return GoogleSheetService.instance
  }

  private async getAuth(): Promise<JWT> {
    if (!this.auth) {
      try {
        // 移除多余的引号和空格
        const privateKey = process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n')
          ?.replace(/^"/, '')
          ?.replace(/"$/, '')
          ?.trim()

        this.auth = new JWT({
          email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
          key: privateKey,
          scopes: [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive',
          ],
        })
        await this.auth.authorize()
      } catch (error) {
        console.error('认证过程出错:', error)
        throw error
      }
    }
    return this.auth
  }

  // 验证google sheet的服务账号的凭据
  private async validateCredentials(): Promise<void> {
    try {
      console.log('验证凭据文件:', {
        email: GOOGLE_SERVICE_ACCOUNT_EMAIL ? '存在' : '缺失',
        privateKey: GOOGLE_PRIVATE_KEY ? '存在' : '缺失',
        projectId: GOOGLE_PROJECT_ID ? '存在' : '缺失',
      })

      // 检查私钥格式
      if (!GOOGLE_PRIVATE_KEY.includes('-----BEGIN PRIVATE KEY-----')) {
        throw new Error('私钥格式不正确')
      }

      // 验证服务账号邮箱格式
      if (!GOOGLE_SERVICE_ACCOUNT_EMAIL.endsWith('.iam.gserviceaccount.com')) {
        throw new Error('服务账号邮箱格式不正确')
      }
    } catch (error) {
      console.error('凭据验证失败:', error)
      throw error
    }
  }

  // 设置公开访问权限
  public async setPublicFileAccess(
    fileId: string,
    role: GoogleDriveRole = GoogleDriveRole.WRITER,
  ): Promise<void> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      await drive.permissions.create({
        fileId,
        requestBody: {
          role,
          type: GoogleDrivePermissionType.ANYONE,
        },
      })
    } catch (error) {
      console.error('设置公开访问权限失败:', error)
      throw error
    }
  }

  // 设置用户访问权限
  public async setUserAccess(
    fileId: string,
    email: string,
    role: GoogleDriveRole = GoogleDriveRole.READER,
  ): Promise<void> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      try {
        // 先尝试不发送通知
        await drive.permissions.create({
          fileId,
          requestBody: {
            type: GoogleDrivePermissionType.USER,
            role,
            emailAddress: email,
          },
          sendNotificationEmail: false,
        })
      } catch (error) {
        // 如果失败，检查是否是因为用户没关联，然后尝试发送通知
        if (
          (error as any).message &&
          (error as any).message.includes('no Google account associated')
        ) {
          console.log(`邮箱 ${email} 没有关联的Google账户，尝试发送邀请通知...`)
          await drive.permissions.create({
            fileId,
            requestBody: {
              type: GoogleDrivePermissionType.USER,
              role,
              emailAddress: email,
            },
            sendNotificationEmail: true,
            emailMessage: '您已被邀请访问EasyTrack数据表格。请点击链接查看详情。',
          })
        } else {
          throw error
        }
      }
    } catch (error) {
      console.error(`设置用户访问权限时出错 (${email}):`, error)
      throw new Error(`无法授予用户访问权限: ${error as any}.message`)
    }
  }

  // 设置文件权限控制
  public async setFileCapabilities(
    fileId: string,
    capabilities: {
      [FileCapability.SHARE]?: boolean // 是否允许共享
      [FileCapability.CHANGE_PERMISSION]?: boolean // 编辑者是否可以更改权限
    },
  ): Promise<void> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      await drive.files.update({
        fileId,
        requestBody: {
          ...capabilities,
        },
      })

      console.log('文件权限设置更新成功')
    } catch (error) {
      console.error('更新文件权限设置失败:', error)
      throw error
    }
  }

  // 设置受限的编辑者权限
  public async setRestrictedEditorAccess(fileId: string, email: string): Promise<void> {
    try {
      // 1. 设置用户为编辑者
      await this.setUserAccess(fileId, email, GoogleDriveRole.WRITER)

      // 2. 限制编辑者的权限
      await this.setFileCapabilities(fileId, {
        [FileCapability.CHANGE_PERMISSION]: false, // 编辑者不能更改权限
        [FileCapability.SHARE]: false, // 不能共享文件
      })

      console.log('已设置受限编辑者权限')
    } catch (error) {
      console.error('设置受限编辑者权限失败:', error)
      throw error
    }
  }

  // 获取分享链接
  public async getShareableLink(fileId: string): Promise<string> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      const file = await drive.files.get({
        fileId,
        fields: 'webViewLink',
      })

      return file.data.webViewLink || `https://docs.google.com/spreadsheets/d/${fileId}`
    } catch (error) {
      console.error('获取分享链接失败:', error)
      throw error
    }
  }

  // 更新Post Data表格表头和样式
  private async applyPostDataSheetHeaderAndStyle(
    sheets: sheets_v4.Sheets,
    spreadsheetId: string,
    sheetIds: { [key: string]: number },
  ): Promise<void> {
    try {
      // 1. 更新表头值
      const values = [Object.values(POST_DATA_SHEET_HEADER).map((header) => header.value)]
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: 'Post Data!A1:T1',
        valueInputOption: 'USER_ENTERED',
        requestBody: { values },
      })

      // 2. 应用样式
      if (sheetIds['Post Data']) {
        const postDataFormatRequests = await getPostDataSheetStyle(sheetIds['Post Data'])
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId,
          requestBody: { requests: postDataFormatRequests },
        })
      }
    } catch (error) {
      console.error('更新Post Data表格表头和样式失败:', error)
      throw error
    }
  }

  public async updateSheet(spreadsheetId: string): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })
      const drive = google.drive({ version: 'v3', auth })
      // 获取权限列表
      const permissionsResponse = await drive.permissions.list({
        fileId: spreadsheetId,
        fields: 'permissions(id,role,type,emailAddress)',
      })
      const currentPermissions = permissionsResponse.data.permissions || []
      // 保存非所有者的权限信息
      const permissionsToRestore = currentPermissions
        .filter((permission) => permission.role !== 'owner' && permission.emailAddress)
        .map((permission) => ({
          role: permission.role,
          type: permission.type,
          emailAddress: permission.emailAddress,
        }))
      const permissionPromises = permissionsToRestore
        .filter((permission) => permission.emailAddress)
        .map(async (permission) => {
          try {
            await drive.permissions.create({
              fileId: spreadsheetId,
              sendNotificationEmail: false,
              requestBody: {
                role: permission.role,
                type: permission.type,
                emailAddress: permission.emailAddress,
              },
            })
            console.log(`已恢复用户权限: ${permission.emailAddress}`)
          } catch (error) {
            console.error(`恢复权限失败 ${permission.emailAddress}:`, error)
          }
        })
      // 2. 处理 Sheet 逻辑（清除所有内容和权限）
      const sheetIds = await this.handleSheetLogic(sheets, spreadsheetId)

      await Promise.all([
        this.applyPostDataSheetHeaderAndStyle(sheets, spreadsheetId, sheetIds),
        this.setPublicFileAccess(spreadsheetId, GoogleDriveRole.WRITER),
        this.setSpreadsheetFunctionalities(sheets, spreadsheetId),
        await this.setFileCapabilities(spreadsheetId, {
          [FileCapability.CHANGE_PERMISSION]: false,
          [FileCapability.SHARE]: true,
        }),
        ...permissionPromises,
      ])

      // // 恢复spreadsheetId对应的用户的权限为编辑者。
      // const userGoogleSheet = await prisma.userGoogleSheet.findUnique({
      //   where: {
      //     spreadsheetId,
      //   },
      //   include: {
      //     user: true,
      //   },
      // })

      // if (userGoogleSheet?.user?.email) {
      //   await this.setUserAccess(spreadsheetId, userGoogleSheet.user.email, GoogleDriveRole.WRITER)
      // } else {
      //   console.warn(`未找到spreadsheetId ${spreadsheetId} 对应的用户邮箱`)
      // }
    } catch (error) {
      console.error('更新表格失败:', error)
      throw error
    }
  }

  // 处理sheet的逻辑，包括删除不在配置中的sheet，创建新的sheet，清除sheet内容。
  private async handleSheetLogic(
    sheets: sheets_v4.Sheets,
    spreadsheetId: string,
  ): Promise<{ [key: string]: number }> {
    try {
      // 1. 获取 Drive 服务实例用于处理权限
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      // 2. 获取当前所有权限
      const permissionsResponse = await drive.permissions.list({
        fileId: spreadsheetId,
        fields: 'permissions(id,role,type,emailAddress)',
      })

      // 3. 删除所有非所有者权限
      const permissions = permissionsResponse.data.permissions || []
      for (const permission of permissions) {
        if (permission.role !== 'owner' && permission.id) {
          try {
            await drive.permissions.delete({
              fileId: spreadsheetId,
              permissionId: permission.id,
            })
          } catch (error) {
            console.error(`删除权限失败: ${permission.id}`, error)
          }
        }
      }

      // 4. 获取所有sheet
      const sheetsResponse = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties',
      })

      const sheetsList = sheetsResponse.data.sheets || []
      const sheetIds: { [key: string]: number } = {}

      // 5. 先删除所有不在配置中的sheet
      const sheetsToDelete = sheetsList.filter(
        (sheet) =>
          sheet.properties?.title &&
          !SHEETS_CONFIG.sheets.some((s) => s.properties.title === sheet.properties?.title),
      )

      if (sheetsToDelete.length > 0) {
        const deleteRequests = sheetsToDelete
          .filter((sheet) => sheet.properties?.sheetId !== undefined)
          .map((sheet) => ({
            deleteSheet: {
              sheetId: sheet.properties?.sheetId,
            },
          }))

        if (deleteRequests.length > 0) {
          try {
            await sheets.spreadsheets.batchUpdate({
              spreadsheetId,
              requestBody: {
                requests: deleteRequests,
              },
            })
          } catch (error) {
            console.error('删除不需要的sheet失败:', error)
            // 继续执行，不中断流程
          }
        }
      }

      // 6. 处理每个配置中定义的sheet
      for (const configSheet of SHEETS_CONFIG.sheets) {
        const sheetTitle = configSheet.properties.title
        const existingSheet = sheetsList.find((sheet) => sheet.properties?.title === sheetTitle)

        if (existingSheet) {
          sheetIds[sheetTitle] = existingSheet.properties?.sheetId || 0
        } else {
          // 创建新的sheet
          const createResponse = await sheets.spreadsheets.batchUpdate({
            spreadsheetId,
            requestBody: {
              requests: [
                {
                  addSheet: {
                    properties: configSheet.properties,
                  },
                },
              ],
            },
          })

          sheetIds[sheetTitle] = createResponse.data.replies?.[0].addSheet?.properties?.sheetId || 0
        }

        // 7. 使用clearSheetContent方法清除sheet内容
        await this.clearSheetContent(spreadsheetId, sheetIds[sheetTitle])
      }

      return sheetIds
    } catch (error) {
      console.error('Error in handleSheetLogic:', error)
      throw error
    }
  }

  // 创建新的表格，以及创建表格后的权限初始化，样式渲染。
  public async createNewSheet(
    email: string,
  ): Promise<{ spreadsheetId: string; shareableLink: string }> {
    try {
      await this.validateCredentials()
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      // 创建表格的conf
      const requestBody = {
        ...SHEETS_CONFIG,
        properties: {
          ...SHEETS_CONFIG.properties,
          title: `EasyTrack-${email}`,
        },
      }

      // 创建包含两个sheet的表格
      const createResponse = await sheets.spreadsheets.create({
        requestBody,
      })

      const spreadsheetId = createResponse.data.spreadsheetId
      if (!spreadsheetId) {
        throw new Error('创建表格失败：未获取到 spreadsheetId')
      }

      const sheetsList = createResponse.data.sheets || []

      const postDataSheet = sheetsList.find((sheet) => sheet.properties?.title === 'Post Data')

      const postDataSheetId: number = postDataSheet?.properties?.sheetId || 0

      // 1. 设置电子表格功能
      await Promise.all([
        this.setSpreadsheetFunctionalities(sheets, spreadsheetId),
        this.setPublicFileAccess(spreadsheetId, GoogleDriveRole.WRITER),
        this.applyPostDataSheetHeaderAndStyle(sheets, spreadsheetId, {
          'Post Data': postDataSheetId,
        }),
        this.setFileCapabilities(spreadsheetId, {
          [FileCapability.CHANGE_PERMISSION]: false, // 不能修改权限
          [FileCapability.SHARE]: true, // 可以共享
        }),
      ])

      const shareableLink = await this.getShareableLink(spreadsheetId)

      return {
        spreadsheetId,
        shareableLink,
      }
    } catch (error) {
      console.error('创建表格失败:', error)
      throw error
    }
  }

  // 更新 Sheet中所有的 数据
  public async updateAllSheetsData(spreadsheetId: string): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      // 获取所有 sheet 的 ID
      const sheetsResponse = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties',
      })

      const sheetsList = sheetsResponse.data.sheets || []
      const sheetIds: { [key: string]: number } = {}
      sheetsList.forEach((sheet) => {
        if (sheet.properties?.title) {
          sheetIds[sheet.properties.title] = sheet.properties.sheetId || 0
        }
      })
      // 更新 Post Data sheet
      if (sheetIds['Post Data']) {
        // 在更新数据之前，先清除现有数据（第2行开始）
        await sheets.spreadsheets.values.clear({
          spreadsheetId,
          range: 'Post Data!A2:T200',
        })

        // 1. 从数据库获取数据
        const sheetData = await prisma.publicationStatisticsSheetData.findMany({
          where: { spreadsheetId },
          orderBy: { publishDate: 'desc' },
          include: {
            tags: {
              where: {
                tag: {
                  id: {
                    not: undefined,
                  },
                },
              },
              include: {
                tag: true,
              },
            },
          },
        })

        if (!sheetData || sheetData.length === 0) {
          console.log('没有找到需要更新的数据')
          return
        }

        // 2. 定义数据映射关系
        const dataMapper: Record<string, (data: any) => string> = {
          ID: (data: any) => data.id ?? '',
          'Post Date': (data: any) => data.publishDate?.toISOString().split('T')[0] ?? '',
          'Post Link': (data: any) => data.nickName ?? '',
          Platform: (data: any) => data.postType ?? '',
          CPM: (data: any) => data.cpm?.toString() ?? '',
          'Cost($)': (data: any) => data.totalCost?.toString() ?? '',
          'Free Note 1': (data: any) => data.notes1 ?? '',
          'Free Note 2': (data: any) => data.notes2 ?? '',
          Region: (data: any) => data.region ?? '',
          ER: (data: any) =>
            data.engagementRate ? `${(data.engagementRate * 100).toFixed(2)}%` : '',
          Views: (data: any) => data.views?.toLocaleString() ?? '0',
          Likes: (data: any) => data.likes?.toLocaleString() ?? '0',
          Comments: (data: any) => data.comments?.toLocaleString() ?? '0',
          Favorites: (data: any) => data.favorites?.toLocaleString() ?? '0',
          Shares: (data: any) => data.shares?.toLocaleString() ?? '0',
          'Audience Profile': (data: any) => formatCountryData(data.countryData),
          'Creator Profile': (data: any) => {
            const platform = (data.postType || '').toLowerCase()
            const handle = data.influencer?.replace(/^@/, '') || ''

            if (platform.startsWith('tiktok')) {
              return `https://www.tiktok.com/@${handle}`
            } else if (platform.startsWith('instagram')) {
              return `https://www.instagram.com/${handle}`
            } else if (platform.startsWith('youtube')) {
              return `https://www.youtube.com/@${handle}`
            }
            return ''
          },
          Contact: (data: any) => data.contactInformation ?? '',
          'User Input Link': (data: any) => data.postLink ?? '',
          Labels: (data: any) => data.tags.map((tag: any) => tag.tag.name).join(',') ?? '',
        }

        // 3. 转换数据为表格格式
        const values = sheetData.map((data) =>
          Object.keys(POST_DATA_SHEET_HEADER).map(
            (key) => dataMapper[key as keyof typeof dataMapper]?.(data) ?? '',
          ),
        )

        // 4. 更新表格数据
        await sheets.spreadsheets.values.update({
          spreadsheetId,
          range: 'Post Data!A2:T',
          valueInputOption: 'USER_ENTERED',
          requestBody: { values },
        })

        // 5. 设置格式和超链接
        if (values.length > 0) {
          await applyPostDataSheetDataRowsStyle(
            sheets,
            spreadsheetId,
            sheetIds['Post Data'],
            values.length,
            sheetData,
          )
        }

        console.log(`成功更新 Post Data ${values.length} 行数据`)
      }
    } catch (error) {
      console.error('更新表格数据失败:', error)
      throw error
    }
  }
  public async updatePostSheetData(spreadsheetId: string): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      // 获取所有 sheet 的 ID
      const sheetsResponse = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties',
      })

      const sheetsList = sheetsResponse.data.sheets || []
      const sheetIds: { [key: string]: number } = {}
      sheetsList.forEach((sheet) => {
        if (sheet.properties?.title) {
          sheetIds[sheet.properties.title] = sheet.properties.sheetId || 0
        }
      })

      // 更新 Post Data sheet
      if (sheetIds['Post Data']) {
        // 在更新数据之前，先清除现有数据（第2行开始）
        await sheets.spreadsheets.values.clear({
          spreadsheetId,
          range: 'Post Data!A2:T200',
        })

        // 1. 从数据库获取数据
        const sheetData = await prisma.publicationStatisticsSheetData.findMany({
          where: { spreadsheetId },
          orderBy: { publishDate: 'desc' },
          include: {
            tags: {
              where: {
                tag: {
                  id: {
                    not: undefined,
                  },
                },
              },
              include: {
                tag: true,
              },
            },
          },
        })

        if (!sheetData || sheetData.length === 0) {
          console.log('没有找到需要更新的数据')
          return
        }

        // 2. 定义数据映射关系
        const dataMapper: Record<string, (data: any) => string> = {
          ID: (data: any) => data.id ?? '',
          'Post Date': (data: any) => data.publishDate?.toISOString().split('T')[0] ?? '',
          'Post Link': (data: any) => data.nickName ?? '',
          Platform: (data: any) => data.postType ?? '',
          CPM: (data: any) => data.cpm?.toString() ?? '',
          'Cost($)': (data: any) => data.totalCost?.toString() ?? '',
          'Free Note 1': (data: any) => data.notes1 ?? '',
          'Free Note 2': (data: any) => data.notes2 ?? '',
          Region: (data: any) => data.region ?? '',
          ER: (data: any) =>
            data.engagementRate ? `${(data.engagementRate * 100).toFixed(2)}%` : '',
          Views: (data: any) => data.views?.toLocaleString() ?? '0',
          Likes: (data: any) => data.likes?.toLocaleString() ?? '0',
          Comments: (data: any) => data.comments?.toLocaleString() ?? '0',
          Favorites: (data: any) => data.favorites?.toLocaleString() ?? '0',
          Shares: (data: any) => data.shares?.toLocaleString() ?? '0',
          'Audience Profile': (data: any) => formatCountryData(data.countryData),
          'Creator Profile': (data: any) => {
            const platform = (data.postType || '').toLowerCase()
            const handle = data.influencer?.replace(/^@/, '') || ''

            if (platform.startsWith('tiktok')) {
              return `https://www.tiktok.com/@${handle}`
            } else if (platform.startsWith('instagram')) {
              return `https://www.instagram.com/${handle}`
            } else if (platform.startsWith('youtube')) {
              return `https://www.youtube.com/@${handle}`
            }
            return ''
          },
          Contact: (data: any) => data.contactInformation ?? '',
          'User Input Link': (data: any) => data.postLink ?? '',
          Labels: (data: any) => data.tags.map((tag: any) => tag.tag.name).join(',') ?? '',
        }

        // 3. 转换数据为表格格式
        const values = sheetData.map((data) =>
          Object.keys(POST_DATA_SHEET_HEADER).map(
            (key) => dataMapper[key as keyof typeof dataMapper]?.(data) ?? '',
          ),
        )

        // 4. 更新表格数据
        await sheets.spreadsheets.values.update({
          spreadsheetId,
          range: 'Post Data!A2:T',
          valueInputOption: 'USER_ENTERED',
          requestBody: { values },
        })

        // 5. 设置格式和超链接
        if (values.length > 0) {
          await applyPostDataSheetDataRowsStyle(
            sheets,
            spreadsheetId,
            sheetIds['Post Data'],
            values.length,
            sheetData,
          )
        }

        console.log(`成功更新 Post Data ${values.length} 行数据`)
      }
    } catch (error) {
      console.error('更新表格数据失败:', error)
      throw error
    }
  }
  // 只更新Post Data 表格不可编辑字段的数据
  public async updatePostDataReadOnlyData(spreadsheetId: string): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      // 1. 从数据库获取数据
      const sheetData = await prisma.publicationStatisticsSheetData.findMany({
        where: { spreadsheetId },
        orderBy: { publishDate: 'desc' },
        include: {
          tags: {
            where: {
              tag: {
                id: {
                  not: undefined,
                },
              },
            },
            include: {
              tag: true,
            },
          },
        },
      })
      if (!sheetData || sheetData.length === 0) {
        console.log('没有找到需要更新的数据')
        return
      }

      // 3. 定义只读字段的数据映射关系
      const readOnlyDataMapper: Record<string, (data: any) => string> = {
        ID: (data: any) => data.id ?? '',
        'Post Date': (data: any) => data.publishDate?.toISOString().split('T')[0] ?? '',
        'Post Link': (data: any) => data.nickName ?? '',
        Platform: (data: any) => data.postType ?? '',
        CPM: (data: any) => data.cpm?.toString() ?? '',
        Region: (data: any) => data.region ?? '',
        ER: (data: any) =>
          data.engagementRate ? `${(data.engagementRate * 100).toFixed(2)}%` : '',
        Views: (data: any) => data.views?.toLocaleString() ?? '0',
        Likes: (data: any) => data.likes?.toLocaleString() ?? '0',
        Comments: (data: any) => data.comments?.toLocaleString() ?? '0',
        Favorites: (data: any) => data.favorites?.toLocaleString() ?? '0',
        Shares: (data: any) => data.shares?.toLocaleString() ?? '0',
        'Audience Profile': (data: any) => formatCountryData(data.countryData),
        'Creator Profile': (data: any) => {
          const platform = (data.postType || '').toLowerCase()
          const handle = data.influencer?.replace(/^@/, '') || ''

          if (platform.startsWith('tiktok')) {
            return `https://www.tiktok.com/@${handle}`
          } else if (platform.startsWith('instagram')) {
            return `https://www.instagram.com/${handle}`
          } else if (platform.startsWith('youtube')) {
            return `https://www.youtube.com/@${handle}`
          }
          return ''
        },
        'User Input Link': (data: any) => data.postLink ?? '',
        Labels: (data: any) => data.tags.map((tag: any) => tag.tag.name).join(',') ?? '',
      }

      // 4. 获取只读字段的列信息
      const readOnlyFields = Object.entries(POST_DATA_SHEET_HEADER)
        .filter(([key]) => !EDITABLE_FIELDS.includes(key))
        .sort(([, a], [, b]) => a.column - b.column)

      // 将更新逻辑修改为分段更新
      async function updateSegmentedData(
        sheets: sheets_v4.Sheets,
        spreadsheetId: string,
        sheetData: any[],
        readOnlyFields: [string, any][],
      ) {
        // 按照连续的列区段分组
        const segments = []
        let currentSegment = []
        let previousColumn = -1

        for (const [key, info] of readOnlyFields) {
          if (info.column !== previousColumn + 1 && currentSegment.length > 0) {
            segments.push([...currentSegment])
            currentSegment = []
          }
          currentSegment.push([key, info])
          previousColumn = info.column
        }
        if (currentSegment.length > 0) {
          segments.push(currentSegment)
        }

        // 对每个连续的区段进行更新
        for (const segment of segments) {
          const startColumn = segment[0][1].letter
          const endColumn = segment[segment.length - 1][1].letter
          const updateRange = `Post Data!${startColumn}2:${endColumn}${sheetData.length + 1}`

          const segmentValues = sheetData.map((data) =>
            segment.map(([key]) => readOnlyDataMapper[key]?.(data) ?? ''),
          )

          await sheets.spreadsheets.values.update({
            spreadsheetId,
            range: updateRange,
            valueInputOption: 'USER_ENTERED',
            requestBody: { values: segmentValues },
          })
        }
      }

      // 在主函数中调用
      await updateSegmentedData(sheets, spreadsheetId, sheetData, readOnlyFields)

      // 8. 设置格式和超链接
      const sheetId = await this.getSheetId(sheets, spreadsheetId, 'Post Data')
      if (sheetData.length > 0) {
        await applyPostDataSheetDataRowsStyle(
          sheets,
          spreadsheetId,
          sheetId,
          sheetData.length,
          sheetData,
        )
      }
    } catch (error) {
      console.error('更新只读数据失败:', error)
      throw error
    }
  }
  //----------获取sheet 的辅助方法----------
  // 获取 Sheet ID 的辅助方法
  private async getSheetId(
    sheets: sheets_v4.Sheets,
    spreadsheetId: string,
    sheetTitle: string,
  ): Promise<number> {
    const response = await sheets.spreadsheets.get({
      spreadsheetId,
      fields: 'sheets.properties',
    })

    const sheet = response.data.sheets?.find((s) => s.properties?.title === sheetTitle)
    return sheet?.properties?.sheetId || 0
  }
  // 根据 sheet 名称获取 sheetId
  public async getSheetIdByTitle(
    spreadsheetId: string,
    sheetTitle: string,
  ): Promise<number | null> {
    try {
      const sheets = await this.getSheetsList(spreadsheetId)
      const sheet = sheets.find((s) => s.title === sheetTitle)
      return sheet?.sheetId || null
    } catch (error) {
      console.error('获取 SheetId 失败:', error)
      throw error
    }
  }
  public async getSpreadsheetInfo(spreadsheetId: string): Promise<SpreadsheetInfo> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })
      const drive = google.drive({ version: 'v3', auth })

      // 获取 spreadsheet 的基本信息
      const [sheetResponse, fileResponse] = await Promise.all([
        sheets.spreadsheets.get({
          spreadsheetId,
          fields: 'properties.title,sheets.properties',
        }),
        drive.files.get({
          fileId: spreadsheetId,
          fields: 'createdTime,modifiedTime',
        }),
      ])

      const sheetsInfo: SheetInfo[] =
        sheetResponse.data.sheets?.map((sheet) => ({
          sheetId: sheet.properties?.sheetId || 0,
          title: sheet.properties?.title || '',
          index: sheet.properties?.index || 0,
          rowCount: sheet.properties?.gridProperties?.rowCount || 0,
          columnCount: sheet.properties?.gridProperties?.columnCount || 0,
          hidden: sheet.properties?.hidden || false,
        })) || []

      // 确保时间字段有默认值
      const createdTime = fileResponse.data.createdTime || new Date().toISOString()
      const modifiedTime = fileResponse.data.modifiedTime || new Date().toISOString()

      return {
        spreadsheetId,
        spreadsheetTitle: sheetResponse.data.properties?.title || '',
        sheets: sheetsInfo,
        createdTime,
        modifiedTime,
      }
    } catch (error) {
      console.error('获取 Spreadsheet 信息失败:', error)
      throw error
    }
  }
  // 获取指定 spreadsheet 下的所有 sheet 列表
  public async getSheetsList(spreadsheetId: string): Promise<SheetInfo[]> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      const response = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties',
      })

      return (
        response.data.sheets?.map((sheet) => ({
          sheetId: sheet.properties?.sheetId || 0,
          title: sheet.properties?.title || '',
          index: sheet.properties?.index || 0,
          rowCount: sheet.properties?.gridProperties?.rowCount || 0,
          columnCount: sheet.properties?.gridProperties?.columnCount || 0,
          hidden: sheet.properties?.hidden || false,
        })) || []
      )
    } catch (error) {
      console.error('获取 Sheets 列表失败:', error)
      throw error
    }
  }

  // 删除指定的 sheet
  public async deleteSheet(spreadsheetId: string, sheetId: number): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [
            {
              deleteSheet: {
                sheetId: sheetId,
              },
            },
          ],
        },
      })
    } catch (error) {
      console.error('删除 Sheet 失败:', error)
      throw error
    }
  }

  // 获取历史记录
  public async getSheetHistory(spreadsheetId: string): Promise<{
    revisions: Array<{
      time: string
      user: string
      revisionId: string
    }>
  }> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      // 获取文件修改历史
      const response = await drive.revisions.list({
        fileId: spreadsheetId,
        fields: 'revisions(id,modifiedTime,lastModifyingUser)',
        pageSize: 1000, // 设置较大的页面大小以获取更多历史记录
      })

      const revisions = response.data.revisions || []

      // 格式化返回数据
      const formattedRevisions = revisions.map((revision) => ({
        time: revision.modifiedTime || '',
        user: revision.lastModifyingUser?.emailAddress || '',
        revisionId: revision.id || '',
      }))

      return {
        revisions: formattedRevisions,
      }
    } catch (error) {
      console.error('获取表格历史记录失败:', error)
      throw error
    }
  }

  // 获取特定版本的内容
  public async getSheetRevisionContent(spreadsheetId: string, revisionId: string): Promise<any> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })
      const sheets = google.sheets({ version: 'v4', auth })

      // 1. 先获取版本信息
      const revisionResponse = await drive.revisions.get({
        fileId: spreadsheetId,
        revisionId: revisionId,
        fields: '*',
      })

      // 2. 获取当前表格内容
      const sheetResponse = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'properties,sheets.properties,sheets.data',
      })

      return {
        revision: {
          id: revisionResponse.data.id,
          modifiedTime: revisionResponse.data.modifiedTime,
          lastModifyingUser: revisionResponse.data.lastModifyingUser,
        },
        content: {
          properties: sheetResponse.data.properties,
          sheets: sheetResponse.data.sheets,
        },
      }
    } catch (error) {
      console.error('获取表格特定版本内容失败:', error)
      throw error
    }
  }

  // 同步可编辑字段的值
  public async syncPublicationCostData(spreadsheetId: string): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      const response = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: 'Post Data!A2:T',
        valueRenderOption: 'UNFORMATTED_VALUE',
      })

      const values = response.data.values || []

      if (!values?.length) {
        return
      }

      const updateData = values
        .filter((row) => row[0])
        .map((row) => ({
          id: String(row[0]),
          totalCost: Number(row[5] || 0),
          notes1: String(row[6] || ''),
          notes2: String(row[7] || ''),
          contactInformation: String(row[17] || ''),
        }))

      const allIds = updateData.map((item) => item.id)
      const existingRecords = await prisma.publicationStatisticsSheetData.findMany({
        where: {
          id: {
            in: allIds,
          },
        },
        select: { id: true },
      })

      const existingIds = new Set(existingRecords.map((record) => record.id))

      const validUpdates = updateData.filter((item) => existingIds.has(item.id))

      const missingIds = updateData
        .filter((item) => !existingIds.has(item.id))
        .map((item) => item.id)
      if (missingIds.length > 0) {
        console.warn(`找不到以下ID的记录: ${missingIds.join(', ')}`)
      }

      const batchSize = 100
      for (let i = 0; i < validUpdates.length; i += batchSize) {
        const batch = validUpdates.slice(i, i + batchSize)

        await prisma.$transaction(
          async (tx) => {
            await Bluebird.map(
              batch,
              async (item: any) => {
                return tx.publicationStatisticsSheetData.update({
                  where: { id: item.id },
                  data: {
                    totalCost: item.totalCost,
                    notes1: item.notes1,
                    notes2: item.notes2,
                    contactInformation: item.contactInformation,
                  },
                })
              },
              { concurrency: 50 },
            )
          },
          {
            maxWait: 30_000,
            timeout: 30_000,
          },
        )

        console.log(`已更新第${i + 1}至${Math.min(i + batchSize, validUpdates.length)}条记录`)
      }

      console.log(`成功同步 ${validUpdates.length} 行数据的成本和备注信息`)
    } catch (error) {
      console.error('同步成本数据失败:', error)
      throw error
    }
  }

  // 添加新的方法来设置电子表格的功能权限
  private async setSpreadsheetFunctionalities(
    sheets: sheets_v4.Sheets,
    spreadsheetId: string,
  ): Promise<void> {
    try {
      // 1. 获取电子表格中的所有 sheets
      const spreadsheet = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties',
      })

      if (!spreadsheet.data.sheets || spreadsheet.data.sheets.length === 0) {
        throw new Error('未找到任何工作表')
      }

      // 2. 更新电子表格属性以启用外部函数访问
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [
            {
              updateSpreadsheetProperties: {
                properties: {
                  importFunctionsExternalUrlAccessAllowed: true,
                  iterativeCalculationSettings: {
                    convergenceThreshold: 0.001,
                    maxIterations: 100,
                  },
                },
                fields: 'importFunctionsExternalUrlAccessAllowed,iterativeCalculationSettings',
              },
            },
          ],
        },
      })

      // 3. 添加开发者元数据来标记功能启用状态
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [
            {
              createDeveloperMetadata: {
                developerMetadata: {
                  metadataKey: 'enableExternalFunctions',
                  metadataValue: 'true',
                  visibility: 'DOCUMENT',
                  location: {
                    spreadsheet: true,
                  },
                },
              },
            },
          ],
        },
      })

      // 4. 为每个 sheet 设置基本格式和保护
      const formatRequests = spreadsheet.data.sheets.map((sheet) => ({
        repeatCell: {
          range: {
            sheetId: sheet.properties?.sheetId,
            startRowIndex: 0,
            endRowIndex: 1,
          },
          cell: {
            userEnteredFormat: {
              textFormat: {
                bold: true,
              },
              horizontalAlignment: 'CENTER',
            },
          },
          fields: 'userEnteredFormat(textFormat,horizontalAlignment)',
        },
      }))

      // 批量应用所有格式请求
      if (formatRequests.length > 0) {
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId,
          requestBody: {
            requests: formatRequests,
          },
        })
      }

      console.log(`已成功设置电子表格功能和权限，共处理 ${spreadsheet.data.sheets.length} 个工作表`)
    } catch (error) {
      console.error('设置电子表格功能失败:', error)
      throw error
    }
  }

  public async getAllSpreadsheets(): Promise<{
    total: number
    spreadsheets: Array<{
      id: string
      name: string
      createdTime: string
      modifiedTime: string
      webViewLink: string
      owners: Array<{
        displayName: string
        emailAddress: string
      }>
      editors: Array<{
        displayName: string
        emailAddress: string
        role: string
      }>
    }>
  }> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      // 查询所有由该服务账号创建的 Google Sheets
      const response = await drive.files.list({
        q: "mimeType='application/vnd.google-apps.spreadsheet'",
        fields: 'files(id, name, createdTime, modifiedTime, webViewLink, owners, permissions)',
        orderBy: 'createdTime desc',
        pageSize: 1000,
      })

      const spreadsheets = await Promise.all(
        (response.data.files || []).map(async (file) => {
          // 获取每个文件的详细权限信息
          const permissionsResponse = await drive.permissions.list({
            fileId: file.id || '',
            fields: 'permissions(id,displayName,emailAddress,role,type)',
          })

          // 过滤出编辑者（排除所有者）
          const editors = (permissionsResponse.data.permissions || [])
            .filter((permission) => permission.role === 'writer' && permission.type === 'user')
            .map((editor) => ({
              displayName: editor.displayName || '',
              emailAddress: editor.emailAddress || '',
              role: editor.role || '',
            }))

          return {
            id: file.id || '',
            name: file.name || '',
            createdTime: file.createdTime || '',
            modifiedTime: file.modifiedTime || '',
            webViewLink: file.webViewLink || '',
            owners:
              file.owners?.map((owner) => ({
                displayName: owner.displayName || '',
                emailAddress: owner.emailAddress || '',
              })) || [],
            editors,
          }
        }),
      )

      return {
        total: spreadsheets.length,
        spreadsheets,
      }
    } catch (error) {
      console.error('获取所有 Google Sheets 失败:', error)
      throw error
    }
  }

  // 给某个google sheet设置多个编辑者权限
  public async setMultipleWriterAccess(spreadsheetId: string, emails: string[]): Promise<void> {
    try {
      const auth = await this.getAuth()
      const drive = google.drive({ version: 'v3', auth })

      // 批量处理所有邮箱
      const promises = emails.map(async (email) => {
        try {
          await drive.permissions.create({
            fileId: spreadsheetId,
            sendNotificationEmail: false,
            requestBody: {
              role: GoogleDriveRole.WRITER,
              type: GoogleDrivePermissionType.USER,
              emailAddress: email,
            },
          })
          console.log(`已成功设置用户权限: ${email}`)
        } catch (error) {
          console.error(`设置用户权限失败 ${email}:`, error)
          throw error
        }
      })

      await Promise.all(promises)
    } catch (error) {
      console.error('批量设置用户权限失败:', error)
      throw error
    }
  }

  // 清除指定sheet的所有内容和样式
  public async clearSheetContent(spreadsheetId: string, sheetId: number): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      // 1. 获取当前sheet的属性
      const sheetResponse = await sheets.spreadsheets.get({
        spreadsheetId,
        ranges: [],
        fields: 'sheets.properties',
      })

      const targetSheet = sheetResponse.data.sheets?.find(
        (sheet) => sheet.properties?.sheetId === sheetId,
      )

      if (!targetSheet) {
        throw new Error(`未找到ID为 ${sheetId} 的sheet`)
      }

      // 2. 构建清除请求
      const clearRequests: sheets_v4.Schema$Request[] = [
        // 清除所有格式和内容
        {
          updateCells: {
            range: {
              sheetId,
              startRowIndex: 0,
              endRowIndex: targetSheet.properties?.gridProperties?.rowCount || 1000,
              startColumnIndex: 0,
              endColumnIndex: targetSheet.properties?.gridProperties?.columnCount || 26,
            },
            fields: '*',
          },
        },
        // 重置所有合并的单元格
        {
          unmergeCells: {
            range: {
              sheetId,
              startRowIndex: 0,
              endRowIndex: targetSheet.properties?.gridProperties?.rowCount || 1000,
              startColumnIndex: 0,
              endColumnIndex: targetSheet.properties?.gridProperties?.columnCount || 26,
            },
          },
        },
        // 重置工作表大小到默认值
        {
          updateSheetProperties: {
            properties: {
              sheetId,
              gridProperties: {
                rowCount: 1000,
                columnCount: 26,
                frozenRowCount: 0,
                frozenColumnCount: 0,
              },
            },
            fields: 'gridProperties(rowCount,columnCount,frozenRowCount,frozenColumnCount)',
          },
        },
        // 清除所有筛选器
        {
          clearBasicFilter: {
            sheetId,
          },
        },
        // 重置列宽到默认值
        {
          updateDimensionProperties: {
            range: {
              sheetId,
              dimension: 'COLUMNS',
              startIndex: 0,
              endIndex: 26,
            },
            properties: {
              pixelSize: 120, // 默认列宽
            },
            fields: 'pixelSize',
          },
        },
        // 重置行高到默认值
        {
          updateDimensionProperties: {
            range: {
              sheetId,
              dimension: 'ROWS',
              startIndex: 0,
              endIndex: 1000,
            },
            properties: {
              pixelSize: 30, // 默认行高
            },
            fields: 'pixelSize',
          },
        },
      ]

      // 3. 获取并删除所有保护范围
      const protectedRangesResponse = await sheets.spreadsheets.get({
        spreadsheetId,
        ranges: [],
        fields: 'sheets.protectedRanges',
      })

      const protectedRanges =
        protectedRangesResponse.data.sheets
          ?.find((sheet) => sheet.protectedRanges)
          ?.protectedRanges?.filter((range) => range.range?.sheetId === sheetId) || []

      const deleteProtectedRangeRequests = protectedRanges
        .filter((range) => range.protectedRangeId !== undefined)
        .map((range) => ({
          deleteProtectedRange: {
            protectedRangeId: range.protectedRangeId as number,
          },
        }))

      // 4. 执行所有清除请求
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [...clearRequests, ...deleteProtectedRangeRequests],
        },
      })

      console.log(`成功清除sheet(ID: ${sheetId})的所有内容和样式`)
    } catch (error) {
      console.error('清除sheet内容失败:', error)
      throw error
    }
  }

  /**
   * 更新指定 spreadsheet 的工作区间大小
   * @param spreadsheetId 要更新的 spreadsheet ID
   */
  public async updateSheetGridProperties(spreadsheetId: string): Promise<void> {
    try {
      const auth = await this.getAuth()
      const sheets = google.sheets({ version: 'v4', auth })

      const spreadsheet = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets.properties',
      })

      if (!spreadsheet.data.sheets || spreadsheet.data.sheets.length === 0) {
        throw new Error('未找到任何工作表')
      }

      const updateRequests: sheets_v4.Schema$Request[] = spreadsheet.data.sheets
        .map((sheet) => {
          const sheetTitle = sheet.properties?.title
          const configSheet = SHEETS_CONFIG.sheets.find((s) => s.properties.title === sheetTitle)

          if (!configSheet || !sheet.properties?.sheetId) {
            return undefined
          }

          const currentGridProps = sheet.properties.gridProperties || {}
          const configGridProps = configSheet.properties.gridProperties || {}

          const isRowCountSame = currentGridProps.rowCount === configGridProps.rowCount
          const isColumnCountSame = currentGridProps.columnCount === configGridProps.columnCount

          // 如果所有值都相同，则跳过更新
          if (isRowCountSame && isColumnCountSame) {
            console.log(`跳过工作表 "${sheetTitle}" 的更新：属性值未变化`)
            return undefined
          }

          // 记录变化的内容
          const changes: string[] = []
          if (!isRowCountSame)
            changes.push(`行数: ${currentGridProps.rowCount} -> ${configGridProps.rowCount}`)
          if (!isColumnCountSame)
            changes.push(`列数: ${currentGridProps.columnCount} -> ${configGridProps.columnCount}`)
          console.log(`工作表 "${sheetTitle}" 需要更新: ${changes.join(', ')}`)

          return {
            updateSheetProperties: {
              properties: {
                sheetId: sheet.properties.sheetId,
                gridProperties: {
                  rowCount: configGridProps.rowCount,
                  columnCount: configGridProps.columnCount,
                },
              },
              fields: 'gridProperties(rowCount,columnCount)',
            },
          } as sheets_v4.Schema$Request
        })
        .filter((request): request is sheets_v4.Schema$Request => request !== undefined)

      if (updateRequests.length === 0) {
        console.log('没有需要更新的工作表')
        return
      }

      // 3. 执行批量更新
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: updateRequests,
        },
      })

      console.log(`成功更新 ${updateRequests.length} 个工作表的网格属性`)
    } catch (error) {
      console.error('更新工作表网格属性失败:', error)
      throw new Error(
        `更新工作表网格属性失败: ${error instanceof Error ? error.message : String(error)}`,
      )
    }
  }
}

export default GoogleSheetService
