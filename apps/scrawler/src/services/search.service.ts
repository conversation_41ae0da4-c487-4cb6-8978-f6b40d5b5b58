import { InsLongCrawlerTaskParams, YtbLongCrawlerTaskParams } from '@/types/task'
import { KolPlatform, SimilarChannelTaskStatus, prisma } from '@repo/database'
import TaskService from './task'

/**
 * 处理superlike的特殊逻辑：增加配额和重新入队
 * @param task 任务对象
 * @param platform 平台类型
 */
async function handleSuperlikeLogic(task: any, platform: KolPlatform): Promise<void> {
  try {
    console.log(`[SearchService] 开始处理 ${platform} 平台任务 ${task.id} 的superlike逻辑`)

    const addQuota = 10
    let newMaxQuotaCost: number

    switch (platform) {
      case KolPlatform.INSTAGRAM: {
        const currentParams = task.params as unknown as InsLongCrawlerTaskParams
        newMaxQuotaCost = (currentParams.maxQuotaCost || 0) + addQuota

        const updatedParams: InsLongCrawlerTaskParams = {
          ...currentParams,
          maxQuotaCost: newMaxQuotaCost,
        }

        await prisma.similarChannelTask.update({
          where: { id: task.id },
          data: { params: updatedParams },
        })
        break
      }
      case KolPlatform.YOUTUBE: {
        const currentParams = task.params as unknown as YtbLongCrawlerTaskParams
        newMaxQuotaCost = (currentParams.maxQuotaCost || 0) + addQuota

        const updatedParams: YtbLongCrawlerTaskParams = {
          ...currentParams,
          maxQuotaCost: newMaxQuotaCost,
        }

        await prisma.similarChannelTask.update({
          where: { id: task.id },
          data: { params: updatedParams },
        })
        break
      }
      default:
        console.warn(`[SearchService] 不支持的平台: ${platform}`)
        return
    }

    console.log(
      `[SearchService] 为任务 ${task.id} 增加配额 ${addQuota}，新配额: ${newMaxQuotaCost}`,
    )

    if (task.status === SimilarChannelTaskStatus.COMPLETED) {
      const updatedTask = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: task.id },
      })

      await TaskService.getInstance().resumeCompletedLongCrawlerTask(updatedTask)
      console.log(`[SearchService] 任务 ${task.id} 已完成，重新入队成功`)
    } else {
      console.log(`[SearchService] 任务 ${task.id} 状态为 ${task.status}，仅增加配额`)
    }
  } catch (error) {
    console.error(`[SearchService] 处理superlike逻辑失败:`, error)
  }
}

export const SearchService = {
  handleSuperlikeLogic,
}
