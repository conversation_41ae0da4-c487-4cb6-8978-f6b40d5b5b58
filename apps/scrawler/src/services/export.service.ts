import { uploadFileByStream } from '@/api/aliyun'
import { throwError } from '@/common/errors/statusCodes'
import { StatusCodes } from '@/common/response/response'
import { likesKolColumns } from '@/config/exportColumns'
import { PublicationStatsService } from '@/services/publicationStats.service'
import { kolExportData } from '@/types/export'
import { BreakTaskSearchResponse } from '@/types/response/search'
import { KolInfoWithScore } from '@/types/response/union-search.response'
import { exportToExcel } from '@/utils/excel'
import {
  ProjectKol as DBProjectKol,
  InstagramUserInfo,
  KolPlatform,
  ProjectKolAttitude,
  TaskType,
  TikTokUserInfo,
  UserMembership,
  YouTubeChannel,
  prisma,
} from '@repo/database'
import ExcelJS, { Alignment, FillPattern } from 'exceljs'
import _ from 'lodash'
import { Readable } from 'stream'
import { getProjectCandidatesWithPostsByTaskType } from './project'
import { TimezoneService } from './timezone.service'

type ProjectKol = DBProjectKol & {
  KolInfo?: any
  EmailRecord?: any[]
  score?: number
}

export async function generateKolExportData(
  projectId: string,
  membership: UserMembership | null,
): Promise<kolExportData[]> {
  const relations = await prisma.projectKol.findMany({
    where: {
      projectId,
      attitude: {
        in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.SUPERLIKE],
      },
    },
    include: {
      KolInfo: true,
      EmailRecord: true,
    },
    orderBy: {
      createdAt: 'asc',
    },
  })

  // 如果没有数据，返回空数组
  if (!relations.length) {
    return []
  }

  const { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap } =
    await fetchPlatformData(relations)

  // 获取所有相关的tags
  const kolIds = relations.map((rel) => rel.kolId).filter(Boolean)
  const tagsMap = await fetchKolTags(kolIds, membership)

  const data = generateExportData(
    relations,
    { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap },
    membership,
    tagsMap,
  )

  return data
}

async function fetchPlatformData(relations: ProjectKol[]) {
  const youtubeKolIds = relations
    .filter((kol) => kol.KolInfo?.platform === KolPlatform.YOUTUBE)
    .map((kol) => kol.KolInfo?.platformAccount || '')
  const tiktokKolIds = relations
    .filter((kol) => kol.KolInfo?.platform === KolPlatform.TIKTOK)
    .map((kol) => kol.KolInfo?.platformAccount || '')
  const instagramKolIds = relations
    .filter((kol) => kol.KolInfo?.platform === KolPlatform.INSTAGRAM)
    .map((kol) => kol.KolInfo?.platformAccount || '')
  const twitterKolIds = relations
    .filter((kol) => kol.KolInfo?.platform === KolPlatform.TWITTER)
    .map((kol) => kol.KolInfo?.platformAccount || '')

  const [youtubeKols, tiktokKols, instagramKols, twitterKols] = await Promise.all([
    prisma.youTubeChannel.findMany({
      where: { channelId: { in: youtubeKolIds } },
    }),
    prisma.tikTokUserInfo.findMany({
      where: { uniqueId: { in: tiktokKolIds } },
    }),
    prisma.instagramUserInfo.findMany({
      where: { username: { in: instagramKolIds } },
    }),
    prisma.twitterUser.findMany({
      where: { screenName: { in: twitterKolIds } },
    }),
  ])

  return {
    youtubeKolMap: new Map(youtubeKols.map((item) => [item.channelId, item])),
    tiktokKolMap: new Map(tiktokKols.map((item) => [item.uniqueId, item])),
    instagramKolMap: new Map(instagramKols.map((item) => [item.username, item])),
    twitterKolMap: new Map(twitterKols.map((item) => [item.screenName, item])),
  }
}

async function fetchKolTags(
  kolIds: string[],
  membership: UserMembership | null,
): Promise<Map<string, string>> {
  const tagsMap = new Map<string, string>()

  if (!kolIds.length) {
    return tagsMap
  }

  // Determine owner ID based on membership type
  const ownerId = membership?.enterpriseId || membership?.userId || ''

  if (!ownerId) {
    return tagsMap
  }

  // Fetch all KolRecords with their tags for the given KOL IDs
  const kolRecords = await prisma.kolRecord.findMany({
    where: {
      kolId: { in: kolIds },
      ownerId: ownerId,
      deletedAt: null,
    },
    include: {
      tags: {
        include: {
          tag: {
            select: {
              name: true,
            },
          },
        },
        where: {
          deletedAt: null,
        },
      },
    },
  })

  // Build the tags map
  kolRecords.forEach((record) => {
    const tagNames = record.tags
      .map((kolTag) => kolTag.tag.name)
      .filter(Boolean)
      .join(', ')

    if (tagNames) {
      tagsMap.set(record.kolId, tagNames)
    }
  })

  return tagsMap
}

function generateExportData(
  relations: ProjectKol[],
  maps: {
    youtubeKolMap: Map<string, any>
    tiktokKolMap: Map<string, any>
    instagramKolMap: Map<string, any>
    twitterKolMap?: Map<string, any>
  },
  membership: UserMembership | null,
  tagsMap: Map<string, string> = new Map(),
): kolExportData[] {
  return _.map(relations, (relation) => {
    const platform = relation.KolInfo?.platform
    const platformAccount = relation.KolInfo?.platformAccount

    let platformData = {
      platformAccount: '',
      country: '',
      numericSubscriberCount: '',
      videosAverageViewCount: '',
      officialEmailButton: '',
      emailRecord: relation.EmailRecord?.length ? '已发送' : '',
      score: relation.score ? `${(relation.score * 100).toFixed(2)}%` : '',
      url: '',
      createdAt: TimezoneService.formatToUserTimezone(
        relation.createdAt,
        membership?.timezone || 'Asia/Shanghai',
      ),
      isSuperlike: relation.attitude == ProjectKolAttitude.SUPERLIKE ? 'SuperLike' : '',
      lastPublishedTime: 'N/A',
      weeklyPostCount: 0,
      monthlyPostCount: 0,
    }

    switch (platform) {
      case KolPlatform.YOUTUBE: {
        if (!platformAccount) break
        const youtubeKol = maps.youtubeKolMap.get(platformAccount)

        // 获取发布统计数据
        const publicationStats = youtubeKol?.publicationStats
          ? typeof youtubeKol.publicationStats === 'string'
            ? JSON.parse(youtubeKol.publicationStats)
            : youtubeKol.publicationStats
          : null

        const formattedStats = publicationStats
          ? PublicationStatsService.formatStatsForDisplay(publicationStats)
          : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        platformData = {
          ...platformData,
          platformAccount: youtubeKol?.channelHandle
            ? youtubeKol?.channelHandle.replace('@', '')
            : '',
          numericSubscriberCount: youtubeKol?.numericSubscriberCount.toString() || '',
          videosAverageViewCount: youtubeKol?.videosAverageViewCount.toString() || '',
          country: youtubeKol?.country || '',
          officialEmailButton: youtubeKol?.officialEmail ? '存在' : '',
          url: youtubeKol?.channelHandle
            ? `https://youtube.com/${youtubeKol?.channelHandle}`
            : `https://youtube.com/channel/${youtubeKol?.channelId}`,
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        }
        break
      }
      case KolPlatform.TIKTOK: {
        if (!platformAccount) break
        const tiktokKol = maps.tiktokKolMap.get(platformAccount)

        // 获取发布统计数据
        const publicationStats = tiktokKol?.publicationStats
          ? typeof tiktokKol.publicationStats === 'string'
            ? JSON.parse(tiktokKol.publicationStats)
            : tiktokKol.publicationStats
          : null

        const formattedStats = publicationStats
          ? PublicationStatsService.formatStatsForDisplay(publicationStats)
          : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        platformData = {
          ...platformData,
          platformAccount: platformAccount,
          numericSubscriberCount: tiktokKol?.followerCount.toString() || '',
          videosAverageViewCount: tiktokKol?.averagePlayCount.toString() || '',
          country: tiktokKol?.region || '',
          url: `https://tiktok.com/@${platformAccount}`,
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        }
        break
      }
      case KolPlatform.INSTAGRAM: {
        if (!platformAccount) break
        const instagramKol = maps.instagramKolMap.get(platformAccount)

        // 获取发布统计数据
        const publicationStats = instagramKol?.publicationStats
          ? typeof instagramKol.publicationStats === 'string'
            ? JSON.parse(instagramKol.publicationStats)
            : instagramKol.publicationStats
          : null

        const formattedStats = publicationStats
          ? PublicationStatsService.formatStatsForDisplay(publicationStats)
          : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        platformData = {
          ...platformData,
          platformAccount: platformAccount,
          numericSubscriberCount: instagramKol?.followerCount.toString() || '',
          videosAverageViewCount: instagramKol?.averageLikeCount.toString() || '',
          country: instagramKol?.region ?? '',
          url: `https://instagram.com/${platformAccount}`,
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        }
        break
      }
      case KolPlatform.TWITTER: {
        if (!platformAccount) break
        const twitterKol = maps.twitterKolMap?.get(platformAccount)

        // 获取基本信息
        const screenName = twitterKol?.screenName || platformAccount || ''
        const displayName = twitterKol?.name || ''
        const followersCount = twitterKol?.followersCount || 0
        const location = twitterKol?.location || ''

        // 将显示名称保存到 relation.KolInfo.title 以便后续使用
        if (displayName && relation.KolInfo) {
          relation.KolInfo.title = displayName
        }

        const formattedStats = { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        platformData = {
          ...platformData,
          platformAccount: screenName,
          numericSubscriberCount: followersCount.toString(),
          videosAverageViewCount: '0', // Twitter 暂无平均互动数据
          country: location,
          url: `https://twitter.com/${screenName}`,
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        }
        break
      }
    }

    const finalPlatformData = {
      ...platformData,
      platformAccount: platformData.platformAccount?.startsWith('@')
        ? platformData.platformAccount.slice(1)
        : platformData.platformAccount,
    }

    const tags = tagsMap.get(relation.kolId) || ''

    return {
      kolTitle: relation.KolInfo?.title || '',
      platform: relation.KolInfo?.platform || '',
      kolEmail: relation.KolInfo?.email || '',
      tags,
      ...finalPlatformData,
    }
  }).sort((a, b) => {
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  })
}

export async function formatTikTokSearchDataForExport(
  data: KolInfoWithScore[],
  membership?: UserMembership | null,
): Promise<kolExportData[]> {
  // 获取所有 platformAccount
  const platformAccounts = data
    .map((kol) => kol.platformAccount)
    .filter((account): account is string => !!account)

  // 批量查询 TikTokUserInfo
  const tiktokUsers = await prisma.tikTokUserInfo.findMany({
    where: {
      uniqueId: {
        in: platformAccounts,
      },
    },
  })

  console.log(`tiktokUserInfo find users'length : ${tiktokUsers.length}`)
  // 统计粉丝数为0的数量
  const zeroFollowersCount = tiktokUsers.filter((user) => !user.followerCount).length
  console.log(`zeroFollowersCount user's length: ${zeroFollowersCount}`)

  // 创建查找映射
  const tiktokUserMap = new Map(tiktokUsers.map((user) => [user.uniqueId, user]))

  // 获取所有相关的tags
  const kolIds = data.map((kol) => kol.id).filter(Boolean)
  const tagsMap = membership ? await fetchKolTags(kolIds, membership) : new Map()

  return data.map((kol) => {
    const tiktokUser = kol.platformAccount ? tiktokUserMap.get(kol.platformAccount) : undefined

    // 获取发布统计数据
    const publicationStats = tiktokUser?.publicationStats
      ? typeof tiktokUser.publicationStats === 'string'
        ? JSON.parse(tiktokUser.publicationStats)
        : tiktokUser.publicationStats
      : null

    const formattedStats = publicationStats
      ? PublicationStatsService.formatStatsForDisplay(publicationStats)
      : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

    const tags = tagsMap.get(kol.id) || ''

    return {
      kolTitle: tiktokUser?.nickname || kol.title || '',
      platformAccount: kol.platformAccount || '',
      country: tiktokUser?.region || '',
      platform: KolPlatform.TIKTOK,
      numericSubscriberCount: tiktokUser?.followerCount.toString() || '',
      videosAverageViewCount: tiktokUser?.averagePlayCount.toString() || '',
      score: `${(kol.score * 100).toFixed(2)}%`,
      kolEmail: kol.email || '',
      tags,
      emailRecord: '', // 相似待筛选没有邮件记录
      officialEmailButton: '', // TikTok 没有官方邮件按钮
      url: `https://tiktok.com/@${kol.platformAccount || ''}`,
      commonFields: kol.commonFields || '',
      lastPublishedTime: formattedStats.lastPublishedTime,
      weeklyPostCount: formattedStats.weeklyPostCount,
      monthlyPostCount: formattedStats.monthlyPostCount,
    }
  })
}

export async function formatYouTubeSearchDataForExport(
  data: KolInfoWithScore[],
  membership?: UserMembership | null,
): Promise<kolExportData[]> {
  // 获取所有 YouTube channel IDs
  const channelIds = data.map((kol) => kol.platformAccount).filter((id): id is string => !!id)

  // 批量查询 YouTubeChannel
  const youtubeChannels = await prisma.youTubeChannel.findMany({
    where: {
      channelId: {
        in: channelIds,
      },
    },
  })

  const youtubeChannelMap = new Map(youtubeChannels.map((channel) => [channel.channelId, channel]))

  // 获取所有相关的tags
  const kolIds = data.map((kol) => kol.id).filter(Boolean)
  const tagsMap = membership ? await fetchKolTags(kolIds, membership) : new Map()

  return data.map((kol) => {
    const youtubeChannel = kol.platformAccount
      ? youtubeChannelMap.get(kol.platformAccount)
      : undefined

    // 获取发布统计数据
    const publicationStats = youtubeChannel?.publicationStats
      ? typeof youtubeChannel.publicationStats === 'string'
        ? JSON.parse(youtubeChannel.publicationStats)
        : youtubeChannel.publicationStats
      : null

    const formattedStats = publicationStats
      ? PublicationStatsService.formatStatsForDisplay(publicationStats)
      : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

    const tags = tagsMap.get(kol.id) || ''

    return {
      kolTitle: youtubeChannel?.channelName || kol.title || '',
      platformAccount: youtubeChannel?.channelId,
      handle: youtubeChannel?.channelHandle,
      country: youtubeChannel?.country || '',
      platform: KolPlatform.YOUTUBE,
      numericSubscriberCount: youtubeChannel?.numericSubscriberCount.toString() || '',
      videosAverageViewCount: youtubeChannel?.videosAverageViewCount.toString() || '',
      score: `${(kol.score * 100).toFixed(2)}%`,
      kolEmail: kol.email || '',
      tags,
      emailRecord: '',
      officialEmailButton: youtubeChannel?.officialEmail ? '存在' : '',
      url: youtubeChannel?.channelHandle
        ? `https://youtube.com/${youtubeChannel.channelHandle}`
        : `https://youtube.com/channel/${youtubeChannel?.channelId}`,
      commonFields: kol.commonFields || '',
      lastPublishedTime: formattedStats.lastPublishedTime,
      weeklyPostCount: formattedStats.weeklyPostCount,
      monthlyPostCount: formattedStats.monthlyPostCount,
    } as kolExportData
  })
}

export async function formatInstagramSearchDataForExport(
  data: KolInfoWithScore[],
  membership?: UserMembership | null,
): Promise<kolExportData[]> {
  // 获取所有 instagram channel IDs
  const usernames = data
    .map((kol) => kol.platformAccount)
    .filter((username): username is string => !!username)

  // 批量查询 instagramUserInfo
  const users = await prisma.instagramUserInfo.findMany({
    where: {
      username: {
        in: usernames,
      },
    },
  })

  const instagramUserMap = new Map(users.map((u) => [u.username, u]))

  // 获取所有相关的tags
  const kolIds = data.map((kol) => kol.id).filter(Boolean)
  const tagsMap = membership ? await fetchKolTags(kolIds, membership) : new Map()

  return data.map((kol) => {
    const user = kol.platformAccount ? instagramUserMap.get(kol.platformAccount) : undefined

    // 获取发布统计数据
    const publicationStats = user?.publicationStats
      ? typeof user.publicationStats === 'string'
        ? JSON.parse(user.publicationStats)
        : user.publicationStats
      : null

    const formattedStats = publicationStats
      ? PublicationStatsService.formatStatsForDisplay(publicationStats)
      : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

    const tags = tagsMap.get(kol.id) || ''

    return {
      kolTitle: user?.fullName ?? '',
      platformAccount: user?.username ?? '',
      country: user?.region ?? '',
      platform: KolPlatform.INSTAGRAM,
      numericSubscriberCount: String(user?.followerCount ?? 0),
      videosAverageViewCount: String(user?.averageLikeCount ?? 0),
      score: `${(kol.score * 100).toFixed(2)}%`,
      kolEmail: kol.email || '',
      tags,
      emailRecord: '',
      officialEmailButton: '',
      url: `https://instagram.com/${kol.platformAccount || ''}`,
      commonFields: kol.commonFields || '',
      lastPublishedTime: formattedStats.lastPublishedTime,
      weeklyPostCount: formattedStats.weeklyPostCount,
      monthlyPostCount: formattedStats.monthlyPostCount,
    } as kolExportData
  })
}

export async function formatTwitterSearchDataForExport(
  data: KolInfoWithScore[],
  membership?: UserMembership | null,
): Promise<kolExportData[]> {
  // 获取所有 Twitter user screenNames
  const screenNames = data
    .map((kol) => kol.platformAccount)
    .filter((screenName): screenName is string => !!screenName)

  const twitterUsers = await prisma.twitterUser.findMany({
    where: {
      screenName: {
        in: screenNames,
      },
    },
  })

  const twitterUserMap = new Map(twitterUsers.map((user) => [user.screenName, user]))

  // 获取所有相关的tags
  const kolIds = data.map((kol) => kol.id).filter(Boolean)
  const tagsMap = membership ? await fetchKolTags(kolIds, membership) : new Map()

  return data.map((kol) => {
    const twitterUser = kol.platformAccount ? twitterUserMap.get(kol.platformAccount) : undefined

    const screenName = twitterUser?.screenName || kol.platformAccount || ''
    const name = twitterUser?.name || kol.title || ''
    const followersCount = twitterUser?.followersCount || 0
    const location = twitterUser?.location || ''
    const averageEngagement = 0
    const formattedStats = { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

    const tags = tagsMap.get(kol.id) || ''

    return {
      kolTitle: name,
      platformAccount: screenName,
      country: location,
      platform: KolPlatform.TWITTER,
      numericSubscriberCount: followersCount.toString(),
      videosAverageViewCount: averageEngagement.toString(),
      score: `${(kol.score * 100).toFixed(2)}%`,
      kolEmail: kol.email || '',
      tags,
      emailRecord: '', // 相似待筛选没有邮件记录
      officialEmailButton: '', // Twitter 没有官方邮件按钮
      url: `https://twitter.com/${screenName}`,
      commonFields: kol.commonFields || '',
      lastPublishedTime: formattedStats.lastPublishedTime,
      weeklyPostCount: formattedStats.weeklyPostCount,
      monthlyPostCount: formattedStats.monthlyPostCount,
    }
  })
}

export const commonStyle = {
  headerStyle: {
    font: { bold: true, size: 12 },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' },
    } as FillPattern,
  },
  rowStyle: {
    alignment: { vertical: 'middle', horizontal: 'left' } as Alignment,
  },
}

export const CommonColumns = [{ header: '来源', key: 'commonFields', width: 20 }]

export async function generateUserLikesExportData(
  userId: string,
  membership: UserMembership | null,
): Promise<{
  youtubeData: kolExportData[]
  tiktokData: kolExportData[]
  instagramData: kolExportData[]
  twitterData: kolExportData[]
}> {
  // 先查询用户所属的所有项目
  const userProjects = await prisma.projectMembership.findMany({
    where: {
      userId: userId,
    },
    select: {
      projectId: true,
    },
  })

  // 提取项目 ID 数组
  const projectIds = userProjects.map((membership) => membership.projectId)

  // 如果用户没有任何项目，返回空数据
  if (projectIds.length === 0) {
    return {
      youtubeData: [],
      tiktokData: [],
      instagramData: [],
      twitterData: [],
    }
  }

  // 查询用户所有项目中的 like 和 superlike 记录，并按 kolId 去重
  const relations = await prisma.projectKol.findMany({
    where: {
      projectId: {
        in: projectIds,
      },
      attitude: {
        in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.SUPERLIKE],
      },
    },
    include: {
      KolInfo: true,
      EmailRecord: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
    distinct: ['kolId'],
  })

  if (!relations.length) {
    return {
      youtubeData: [],
      tiktokData: [],
      instagramData: [],
      twitterData: [],
    }
  }

  // 按平台分组获取数据
  const youtubeRelations = relations.filter((rel) => rel.KolInfo?.platform === KolPlatform.YOUTUBE)
  const tiktokRelations = relations.filter((rel) => rel.KolInfo?.platform === KolPlatform.TIKTOK)
  const instagramRelations = relations.filter(
    (rel) => rel.KolInfo?.platform === KolPlatform.INSTAGRAM,
  )
  const twitterRelations = relations.filter((rel) => rel.KolInfo?.platform === KolPlatform.TWITTER)

  // 获取平台数据
  const { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap } =
    await fetchPlatformData(relations)

  // 获取所有相关的tags
  const kolIds = relations.map((rel) => rel.kolId).filter(Boolean)
  const tagsMap = await fetchKolTags(kolIds, membership)

  // 生成各平台导出数据
  const youtubeData = generateExportData(
    youtubeRelations,
    { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap },
    membership,
    tagsMap,
  )
  const tiktokData = generateExportData(
    tiktokRelations,
    { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap },
    membership,
    tagsMap,
  )
  const instagramData = generateExportData(
    instagramRelations,
    { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap },
    membership,
    tagsMap,
  )
  const twitterData = generateExportData(
    twitterRelations,
    { youtubeKolMap, tiktokKolMap, instagramKolMap, twitterKolMap },
    membership,
    tagsMap,
  )

  return {
    youtubeData,
    tiktokData,
    instagramData,
    twitterData,
  }
}

export async function exportUserLikesToOSS(
  userId: string,
  membership: UserMembership | null,
): Promise<string> {
  // 获取导出数据
  const { youtubeData, tiktokData, instagramData, twitterData } = await generateUserLikesExportData(
    userId,
    membership,
  )

  // 准备工作表数据，按平台分sheets
  const sheets = []

  if (youtubeData.length > 0) {
    sheets.push({
      name: 'YouTube',
      data: youtubeData,
      columns: likesKolColumns,
      styles: commonStyle,
    })
  }

  if (tiktokData.length > 0) {
    sheets.push({
      name: 'TikTok',
      data: tiktokData,
      columns: likesKolColumns,
      styles: commonStyle,
    })
  }

  if (instagramData.length > 0) {
    sheets.push({
      name: 'Instagram',
      data: instagramData,
      columns: likesKolColumns,
      styles: commonStyle,
    })
  }

  if (twitterData.length > 0) {
    sheets.push({
      name: 'Twitter',
      data: twitterData,
      columns: likesKolColumns,
      styles: commonStyle,
    })
  }

  // 如果没有数据，创建一个空的sheet
  if (sheets.length === 0) {
    sheets.push({
      name: 'No Data',
      data: [],
      columns: likesKolColumns,
      styles: commonStyle,
    })
  }

  // 生成Excel文件
  const buffer = await exportToExcel(sheets)

  // 生成文件名
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const fileName = `excel/user-likes-${userId}-${timestamp}.xlsx`

  // 创建可读流
  const stream = new Readable({
    read() {
      this.push(buffer)
      this.push(null) // 结束流
    },
  })

  // 流式上传到OSS
  await uploadFileByStream(fileName, stream)

  return fileName
}

const supportedTaskTypes = [
  TaskType.FOLLOWING_LIST,
  TaskType.TAGGED_BREAK,
  TaskType.HASH_TAG_BREAK,
  TaskType.SEARCH_INPUT_BREAK,
  TaskType.WEB_LIST,
] as const

export async function exportTaskResult(taskId: string): Promise<Buffer> {
  const task = await prisma.similarChannelTask.findUniqueOrThrow({ where: { id: taskId } })

  if (!supportedTaskTypes.includes(task.type as any)) {
    throwError(StatusCodes.BAD_REQUEST, 'Unsupported task type')
  }
  const params = task.params as any
  const data = (await getProjectCandidatesWithPostsByTaskType(
    task.projectId,
    task.type,
    params.platform as KolPlatform,
    {
      page: 1,
      pageSize: 2000,
    },
  )) as BreakTaskSearchResponse
  const excelData: (kolExportData | null)[] = data.data.map((kol) => {
    switch (kol.platform) {
      case KolPlatform.YOUTUBE: {
        const youtubeChannel = kol.youtubeChannel as YouTubeChannel
        if (!youtubeChannel) {
          return null
        }
        // 获取发布统计数据
        const publicationStats = youtubeChannel.publicationStats
          ? typeof youtubeChannel.publicationStats === 'string'
            ? JSON.parse(youtubeChannel.publicationStats)
            : youtubeChannel.publicationStats
          : null

        const formattedStats = publicationStats
          ? PublicationStatsService.formatStatsForDisplay(publicationStats)
          : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        return {
          kolTitle: youtubeChannel.channelName,
          platformAccount: youtubeChannel.channelId,
          handle: youtubeChannel.channelHandle,
          country: youtubeChannel.country,
          platform: KolPlatform.YOUTUBE,
          numericSubscriberCount: youtubeChannel.numericSubscriberCount.toString(),
          videosAverageViewCount: youtubeChannel.videosAverageViewCount.toString(),
          score: '0',
          kolEmail: kol.email || '',
          emailRecord: '',
          officialEmailButton: '',
          url: youtubeChannel.channelHandle
            ? `https://youtube.com/${youtubeChannel.channelHandle}`
            : `https://youtube.com/channel/${youtubeChannel.channelId}`,
          commonFields: '',
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        } as kolExportData
      }
      case KolPlatform.INSTAGRAM: {
        const instagramUser = kol.instagramUser as InstagramUserInfo
        if (!instagramUser) {
          return null
        }
        // 获取发布统计数据
        const publicationStats = instagramUser.publicationStats
          ? typeof instagramUser.publicationStats === 'string'
            ? JSON.parse(instagramUser.publicationStats)
            : instagramUser.publicationStats
          : null

        const formattedStats = publicationStats
          ? PublicationStatsService.formatStatsForDisplay(publicationStats)
          : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        return {
          kolTitle: instagramUser.fullName,
          platformAccount: instagramUser.username,
          handle: instagramUser.id,
          country: instagramUser.region,
          platform: KolPlatform.INSTAGRAM,
          numericSubscriberCount: instagramUser.followerCount.toString(),
          videosAverageViewCount: instagramUser.averageLikeCount.toString(),
          score: '0',
          kolEmail: kol.email || '',
          emailRecord: '',
          officialEmailButton: '',
          url: `https://instagram.com/${instagramUser.username}`,
          commonFields: '',
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        }
      }
      case KolPlatform.TIKTOK: {
        const tiktokUser = kol.tiktokUser as TikTokUserInfo
        if (!tiktokUser) {
          return null
        }
        // 获取发布统计数据
        const publicationStats = tiktokUser.publicationStats
          ? typeof tiktokUser.publicationStats === 'string'
            ? JSON.parse(tiktokUser.publicationStats)
            : tiktokUser.publicationStats
          : null

        const formattedStats = publicationStats
          ? PublicationStatsService.formatStatsForDisplay(publicationStats)
          : { lastPublishedTime: 'N/A', weeklyPostCount: 0, monthlyPostCount: 0 }

        return {
          kolTitle: tiktokUser.nickname,
          platformAccount: tiktokUser.uniqueId,
          handle: tiktokUser.userId,
          country: tiktokUser.region,
          platform: KolPlatform.TIKTOK,
          numericSubscriberCount: tiktokUser.followerCount.toString(),
          videosAverageViewCount: tiktokUser.averagePlayCount.toString(),
          score: '0',
          kolEmail: kol.email || '',
          emailRecord: '',
          officialEmailButton: '',
          url: `https://tiktok.com/@${tiktokUser.uniqueId}`,
          commonFields: '',
          lastPublishedTime: formattedStats.lastPublishedTime,
          weeklyPostCount: formattedStats.weeklyPostCount,
          monthlyPostCount: formattedStats.monthlyPostCount,
        }
      }
      default:
        return null
    }
  })

  // 过滤掉 null 值并确保类型正确
  const filteredExcelData: kolExportData[] = excelData.filter(
    (item): item is kolExportData => item !== null,
  )

  // 根据任务类型定义列结构
  const columns: Array<{ header: string; key: string; width?: number; hyperlink?: string }> = [
    { header: 'nickname', key: 'kolTitle', width: 20, hyperlink: 'url' },
    { header: '@username', key: 'platformAccount', width: 20 },
    { header: 'Region', key: 'country', width: 15 },
    { header: 'Platform', key: 'platform', width: 15 },
    { header: 'Followers', key: 'numericSubscriberCount', width: 15 },
    { header: 'Avg. Likes', key: 'videosAverageViewCount', width: 15 },
    // { header: 'Similarity score', key: 'score', width: 20 },
    { header: 'Email', key: 'kolEmail', width: 25 },
    // { header: 'Email Send Status', key: 'emailRecord', width: 20 },
    { header: 'URL', key: 'url', width: 25 },
  ]

  // // 如果result中有自定义headers，使用自定义headers
  // if (headers && Array.isArray(headers)) {
  //   columns = headers.map((header: string, index: number) => ({
  //     header,
  //     key: `col_${index}`,
  //     width: 20,
  //   }))
  // }

  // 准备工作表数据
  const sheets = [
    {
      name: `Task_${task.type}`,
      data: filteredExcelData,
      columns,
      styles: {
        headerStyle: {
          font: { bold: true },
          fill: {
            type: 'pattern' as const,
            pattern: 'solid' as const,
            fgColor: { argb: 'FFE0E0E0' },
          } as ExcelJS.FillPattern,
          border: {
            top: { style: 'thin' as ExcelJS.BorderStyle },
            left: { style: 'thin' as ExcelJS.BorderStyle },
            bottom: { style: 'thin' as ExcelJS.BorderStyle },
            right: { style: 'thin' as ExcelJS.BorderStyle },
          },
        },
      },
    },
  ]

  return exportToExcel(sheets)
}
