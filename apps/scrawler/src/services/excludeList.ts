import { IgPost } from '@/api/@types/rapidapi/Instagram'
import { TiktokVideoDetail } from '@/api/@types/rapidapi/Tiktok'
import { VideoDetail } from '@/api/@types/rapidapi/Youtube'
import { uploadFileByStream } from '@/api/aliyun'
import { logTime } from '@/api/util'
import YoutubeApi from '@/api/youtube'
import Sentry from '@/infras/sentry'
import { ExcludeListStatistic, UploadRecord } from '@/types/excludeList'
import { DeductQuotaParams } from '@/types/memberShip'
import { parsePublishedTime } from '@/utils/date'
import { exportToExcel } from '@/utils/excel'
import * as UrlService from '@/utils/url'
import {
  ExcludeList,
  ExcludeListRecordStatus,
  KolPlatform,
  KolRelationType,
  QuotaType,
  UserMembership,
  prisma,
} from '@repo/database'
import Bluebird from 'bluebird'
import _ from 'lodash'
import { Readable } from 'stream'
import { LinkType } from './contact/contact'
import { newExternalLink } from './contact/contact.utils'
import { InstagramPostOrReelExternalLink } from './contact/instagramPostOrReel'
import { TiktokVideoExternalLink } from './contact/tiktokVideo'
import { YoutubeVideoOrShortsExternalLink } from './contact/youtubeVideoOrShorts'
import { commonStyle } from './export.service'
import KolService from './kol'
import { MembershipService } from './membership.service'
import { TimezoneService } from './timezone.service'
import { getUserMembershipAndEnterprise } from './user'

const uploadList = async (content: string, userId: string) => {
  if (!userId) {
    throw new Error('user id is empty')
  }
  const membership = await getUserMembershipAndEnterprise(userId)
  if (!membership) {
    throw new Error('user not found')
  }

  const record = await prisma.excludeListRecord.create({
    data: {
      userId: membership.userId,
      enterpriseId: membership?.enterpriseId ?? '',
      rawContent: content,
      result: {},
    },
  })

  // 处理外链
  const links = processContent(content)
  if (!links.length) {
    throw new Error('Cannot get any link from input.')
  }

  // deduct quota
  await MembershipService.getInstance().deductQuota({
    userId: userId,
    type: QuotaType.EXCLUDE_LIST,
    count: links.map((i) => i.content.trim()).filter((i) => i).length,
  } as DeductQuotaParams)

  // 核心逻辑：处理所有的外链
  let processingLinks = await logTime(processLinks(userId, links), '[sj:processLinks]')

  // 记录所有投放记录
  if (membership?.type == 'ENTERPRISE' && membership.enterpriseId) {
    await createKolRelationRecords(
      processingLinks,
      KolRelationType.CONTACTED,
      userId,
      membership.enterpriseId,
    )
  } else {
    await createKolRelationRecords(processingLinks, KolRelationType.CONTACTED, userId)
  }

  // 对自己去重
  const kolSet = new Set<string>()
  processingLinks.forEach((link) => {
    if (link.kolId && kolSet.has(link.kolId)) {
      link.result = 'skipped'
      link.message = 'This kol included above and skip'
    }
  })
  processingLinks = processingLinks.filter((i) => i.result !== 'skipped' && i.result !== 'failed')

  // 和库中去重
  const existingList = await prisma.excludeList.findMany({
    where: {
      enterpriseId: membership?.type == 'ENTERPRISE' ? membership.enterpriseId : undefined,
      userId: membership?.type == 'ENTERPRISE' ? undefined : membership.userId,
    },
    select: {
      kolId: true,
    },
  })
  const existingKolIds = existingList.map((i) => i.kolId)
  processingLinks.forEach((link) => {
    if (link.kolId && existingKolIds.includes(link.kolId)) {
      link.result = 'skipped'
      link.message = `This kol included in current ${membership?.type} user exclusive list.`
    }
  })
  processingLinks = processingLinks.filter((link) => !link.result)

  // check 一下字段
  processingLinks = processingLinks.filter((link) => {
    if (!link.kolId || !link.platform || !link.platformAccount) {
      link.result = 'failed'
      link.message = `The Kol has empty field: ${{ platform: link.platform, paltformAccount: link.platformAccount, kolId: link.kolId }}`
    }
    return !link.result
  })

  const insertData = processingLinks.map((link) => {
    return {
      kolId: link.kolId,
      platform: link.platform,
      platformAccount: link.platformAccount,
      userId: membership.userId,
      enterpriseId: membership?.type == 'ENTERPRISE' ? membership.enterpriseId : undefined,
    } as ExcludeList
  })
  // insert
  const insertResults = await prisma.excludeList.createManyAndReturn({
    data: insertData,
    skipDuplicates: true,
    select: {
      kolId: true,
    },
  })
  const successKolIds = insertResults.map((i) => i.kolId)
  processingLinks.forEach((link) => {
    if (link.kolId && successKolIds.includes(link.kolId)) {
      link.result = 'success'
      link.message = 'success'
    }
  })
  const errorLinks = links.filter((link) => !link.result || !link.message)
  if (errorLinks.length) {
    console.log(`there are ${errorLinks.length} error links.`)
    Sentry.captureException(new Error(`parse failed links: ${errorLinks.length}`), {
      tags: {
        links: JSON.stringify(errorLinks.map((link) => link.content)),
      },
    })
    errorLinks.forEach((link) => {
      link.result = 'failed'
      link.message = 'unknown error.'
    })
  }

  const successCount = links.filter((link) => link.result == 'success')
  // update record
  await prisma.excludeListRecord.update({
    where: {
      id: record.id,
    },
    data: {
      result: links,
      status: successCount ? ExcludeListRecordStatus.SUCCESS : ExcludeListRecordStatus.FAILED,
    },
  })
  return links.map((i) => _.omit(i, ['postDetail']))
}

const processContent = (content: string): UploadRecord[] => {
  const rows = content.split('\n')
  const links: UploadRecord[] = []
  rows.forEach((row, idx) => {
    links.push({
      content: row.trim(),
      lineNumber: idx + 1,
    } as UploadRecord)
  })
  return links
}

const processLinks = async (
  userId: string | undefined,
  links: UploadRecord[],
): Promise<UploadRecord[]> => {
  const listWithAccount = await Bluebird.map(
    links,
    async (link) => {
      const externalLink = await newExternalLink(link.content, link.content, 0)
      link.linkType = externalLink.type
      switch (externalLink.type) {
        case LinkType.YOUTUBE_SHORTS:
        case LinkType.YOUTUBE_VIDEO: {
          const video = await (externalLink as YoutubeVideoOrShortsExternalLink).getPost()
          link.platform = KolPlatform.YOUTUBE
          link.postId = video?.videoId
          link.postDetail = video ?? undefined
          const channelId = video?.channelId
          link.platformAccount = channelId
          break
        }
        case LinkType.TIKTOK_VIDEO: {
          try {
            const video = await (externalLink as TiktokVideoExternalLink).getPost()
            if (!video?.id) {
              throw new Error(`failed to parse video ${link.content}`)
            }
            link.postId = video?.id
            link.platform = KolPlatform.TIKTOK
            link.postDetail = video ?? undefined
            link.platformAccount = video?.author.unique_id
          } catch (err) {
            console.error(err)
            console.log(`failed to parse video ${link.content}`)
            if (UrlService.isTiktokUniqueId(externalLink.getLink())) {
              const uniqueId = UrlService.getTiktokUniqueId(externalLink.getLink())
              link.platform = KolPlatform.TIKTOK
              link.platformAccount = uniqueId ?? ''
            }
          }
          break
        }
        case LinkType.INSTAGRAM_POST:
        case LinkType.INSTAGRAM_REEL: {
          const post = await (externalLink as InstagramPostOrReelExternalLink).getPost()
          link.postId = post?.id
          link.postDetail = post ?? undefined
          link.platform = KolPlatform.INSTAGRAM
          link.platformAccount = post?.username
          break
        }
        case LinkType.YOUTUBE: {
          let channelId = undefined
          if (
            UrlService.isYoutubeChannelHandle(externalLink.getLink()) ||
            UrlService.isYoutubeChandle(externalLink.getLink())
          ) {
            const channelHandle = UrlService.getYoutubeChannelHandle(externalLink.getLink())
            if (channelHandle) {
              channelId = await YoutubeApi.getInstance().getYoutubeChannelId(channelHandle)
            }
          } else if (UrlService.isYoutubeChannelIdUrl(externalLink.getLink())) {
            channelId = UrlService.getYoutubeChannelId(externalLink.getLink())
          }
          link.platform = KolPlatform.YOUTUBE
          link.platformAccount = channelId ?? ''
          break
        }
        case LinkType.TIKTOK: {
          if (UrlService.isTiktokUniqueId(externalLink.getLink())) {
            const uniqueId = UrlService.getTiktokUniqueId(externalLink.getLink())
            link.platform = KolPlatform.TIKTOK
            link.platformAccount = uniqueId ?? ''
          }
          break
        }
        case LinkType.INSTAGRAM: {
          if (UrlService.isInstagramUsername(externalLink.getLink())) {
            const username = UrlService.getInstagramUsername(externalLink.getLink())
            link.platform = KolPlatform.INSTAGRAM
            link.platformAccount = username ?? ''
          }
          break
        }
        default: {
          link.platform = undefined
          link.platformAccount = undefined
          link.postId = undefined
          link.result = 'failed'
          link.message = 'unknown link'
        }
      }
      if (!link.result && !link.platformAccount) {
        link.result = 'failed'
        link.message = `Cannot find kol from ${link.content}`
      }
      return link
    },
    { concurrency: 20 },
  )

  await Bluebird.map(
    listWithAccount.filter((i) => i.result != 'failed'),
    async (link) => {
      const id = link.platform == KolPlatform.YOUTUBE ? (link.platformAccount ?? '') : ''
      const handle = link.platform == KolPlatform.YOUTUBE ? '' : (link.platformAccount ?? '')
      if (!link.platform) {
        link.result = 'failed'
        link.message = 'Cannot recognize the link'
        return link
      }
      const kol = await KolService.getInstance().findOrCreateKol(userId, handle, id, link.platform)
      if (!kol) {
        link.result = 'failed'
        link.message = 'Cannot find the kol'
        return link
      }
      try {
        const kol = await KolService.getInstance().findOrCreateKol(
          userId,
          handle,
          id,
          link.platform,
        )
        if (!kol) {
          link.result = 'failed'
          link.message = `cannot find kol: ${link.platformAccount}`
          return link
        }
        link.kolId = kol.id
      } catch (err) {
        console.log(
          `[excludeList]link create kol failed: ${link.content}, id: ${id}, handle: ${handle}`,
        )
        link.result = 'failed'
        link.message = `cannot find kol from ${link.content}`
      }
      return link
    },
    { concurrency: 5 },
  )
  return listWithAccount
}

const getExcludeList = async (userId: string, platform?: KolPlatform): Promise<ExcludeList[]> => {
  const membership = await getUserMembershipAndEnterprise(userId)
  const inputUserId = membership?.enterpriseId ? undefined : userId
  const enterpriseId = membership?.enterpriseId ?? undefined
  const isEnterprise = membership?.type == 'ENTERPRISE'
  return await prisma.excludeList.findMany({
    where: {
      platform: platform,
      enterpriseId: isEnterprise ? enterpriseId : undefined,
      userId: isEnterprise ? undefined : inputUserId,
    },
  })
}

const getExcludeIds = async (userId: string, platform: KolPlatform): Promise<string[]> => {
  const list = await getExcludeList(userId, platform)
  return list.map((i) => i.platformAccount)
}

const getStatistic = async (userId: string): Promise<ExcludeListStatistic> => {
  const membership = await getUserMembershipAndEnterprise(userId)
  const inputUserId = membership?.enterpriseId ? undefined : userId
  const enterpriseId = membership?.enterpriseId ?? undefined
  const isEnterprise = membership?.type == 'ENTERPRISE'
  const count = await prisma.excludeList.count({
    where: {
      enterpriseId: isEnterprise ? enterpriseId : undefined,
      userId: isEnterprise ? undefined : inputUserId,
    },
  })
  return { count: count }
}

const clearList = async (userId: string): Promise<number> => {
  const membership = await getUserMembershipAndEnterprise(userId)
  const inputUserId = membership?.enterpriseId ? undefined : userId
  const enterpriseId = membership?.enterpriseId ?? undefined
  if (!inputUserId && !enterpriseId) {
    return 0 // 不要把表清空了
  }
  const isEnterprise = membership?.type == 'ENTERPRISE'
  return await prisma.$transaction(async (tx) => {
    const result = await tx.excludeList.deleteMany({
      where: {
        enterpriseId: isEnterprise ? enterpriseId : undefined,
        userId: isEnterprise ? undefined : inputUserId,
      },
    })
    const record = {
      data: {
        userId: userId,
        enterpriseId: membership?.enterpriseId ?? '',
        rawContent: 'CLEAR',
        result: { count: result.count },
        status: ExcludeListRecordStatus.SUCCESS,
      },
    }
    await tx.excludeListRecord.create(record)
    return result.count
  })
}

const createKolRelationRecords = async (
  links: UploadRecord[],
  type: KolRelationType,
  userId: string,
  enterpriseId?: string,
): Promise<number> => {
  const insertData = links
    .filter((i) => i.platform && i.platformAccount)
    .map((i) => {
      let extra = null
      let postTime = undefined
      if (i.postId) {
        extra = {
          link: i.content,
          postId: i.postId,
        }
        switch (i.platform) {
          case KolPlatform.YOUTUBE:
            postTime = parsePublishedTime((i.postDetail as VideoDetail).publishedTimeText)
            break
          case KolPlatform.INSTAGRAM:
            postTime = (i.postDetail as IgPost).created_at
            break
          case KolPlatform.TIKTOK:
            postTime = (i.postDetail as TiktokVideoDetail).create_time
            break
        }
      }
      return {
        kolId: i.kolId,
        platform: i.platform!,
        platformAccount: i.platformAccount ?? '',
        userId: userId,
        enterpriseId: enterpriseId ?? '',
        type: type,
        extra: extra as any,
        date: postTime ? new Date(postTime) : undefined,
      }
    })
  const result = await prisma.kolRelationRecord.createMany({
    data: insertData,
    skipDuplicates: true,
  })
  return result.count
}

const exportExcludeListToOSS = async (
  excludeList: ExcludeList[],
  membership: UserMembership,
): Promise<string> => {
  // 获取KOL信息
  const kolIds = excludeList.map((item) => item.kolId).filter(Boolean)
  const kolInfos = await prisma.kolInfo.findMany({
    where: {
      id: {
        in: kolIds,
      },
    },
    include: {
      instagramUser: true,
      tiktokUser: true,
      youtubeChannel: true,
    },
  })

  // 创建KOL信息映射
  const kolInfoMap = new Map(kolInfos.map((kol) => [kol.id, kol]))

  // 格式化导出数据
  const exportData = excludeList.map((item) => {
    const kolInfo = kolInfoMap.get(item.kolId)
    let kolTitle = ''
    let followerCount = ''
    let avgViewCount = ''
    let url = ''
    let country = ''

    if (kolInfo) {
      switch (item.platform) {
        case KolPlatform.YOUTUBE: {
          const youtubeChannel = kolInfo.youtubeChannel
          if (youtubeChannel) {
            kolTitle = youtubeChannel.channelName || ''
            followerCount = youtubeChannel.numericSubscriberCount?.toString() || ''
            avgViewCount = youtubeChannel.videosAverageViewCount?.toString() || ''
            url = youtubeChannel.channelHandle
              ? `https://youtube.com/${youtubeChannel.channelHandle}`
              : `https://youtube.com/channel/${youtubeChannel.channelId}`
            country = youtubeChannel.country || ''
          }
          break
        }
        case KolPlatform.TIKTOK: {
          const tiktokUser = kolInfo.tiktokUser
          if (tiktokUser) {
            kolTitle = tiktokUser.nickname || ''
            followerCount = tiktokUser.followerCount?.toString() || ''
            avgViewCount = tiktokUser.averagePlayCount?.toString() || ''
            url = `https://tiktok.com/@${tiktokUser.uniqueId}`
            country = tiktokUser.region || ''
          }
          break
        }
        case KolPlatform.INSTAGRAM: {
          const instagramUser = kolInfo.instagramUser
          if (instagramUser) {
            kolTitle = instagramUser.fullName || ''
            followerCount = instagramUser.followerCount?.toString() || ''
            avgViewCount = instagramUser.averageLikeCount?.toString() || ''
            url = `https://instagram.com/${instagramUser.username}`
            country = instagramUser.region || ''
          }
          break
        }
      }
    }

    return {
      kolTitle,
      platformAccount: item.platformAccount,
      country,
      platform: item.platform,
      numericSubscriberCount: followerCount,
      videosAverageViewCount: avgViewCount,
      url,
      createdAt: TimezoneService.formatToUserTimezone(
        item.createdAt,
        membership?.timezone || 'Asia/Shanghai',
      ),
    }
  })

  // 按平台分组数据
  const youtubeData = exportData.filter((item) => item.platform === KolPlatform.YOUTUBE)
  const tiktokData = exportData.filter((item) => item.platform === KolPlatform.TIKTOK)
  const instagramData = exportData.filter((item) => item.platform === KolPlatform.INSTAGRAM)

  // 导出列配置
  const excludeListColumns = [
    { header: 'nickname', key: 'kolTitle', width: 20, hyperlink: 'url' },
    { header: '@username', key: 'platformAccount', width: 20 },
    { header: 'Region', key: 'country', width: 15 },
    { header: 'Platform', key: 'platform', width: 15 },
    { header: 'Followers', key: 'numericSubscriberCount', width: 15 },
    { header: 'Avg. Views/Likes', key: 'videosAverageViewCount', width: 15 },
    { header: 'URL', key: 'url', width: 25 },
    { header: 'Added Time', key: 'createdAt', width: 20 },
  ]

  // 准备工作表数据，按平台分sheets
  const sheets = []

  if (youtubeData.length > 0) {
    sheets.push({
      name: 'YouTube',
      data: youtubeData,
      columns: excludeListColumns,
      styles: commonStyle,
    })
  }

  if (tiktokData.length > 0) {
    sheets.push({
      name: 'TikTok',
      data: tiktokData,
      columns: excludeListColumns,
      styles: commonStyle,
    })
  }

  if (instagramData.length > 0) {
    sheets.push({
      name: 'Instagram',
      data: instagramData,
      columns: excludeListColumns,
      styles: commonStyle,
    })
  }

  // 如果没有数据，创建一个空的sheet
  if (sheets.length === 0) {
    sheets.push({
      name: 'No Data',
      data: [],
      columns: excludeListColumns,
      styles: commonStyle,
    })
  }

  // 生成Excel文件
  const buffer = await exportToExcel(sheets)

  // 生成文件名
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const fileName = `exclude-list-${membership.userId}-${timestamp}.xlsx`
  const filePath = `excel/${fileName}`

  // 上传到OSS
  await uploadFileByStream(
    filePath,
    new Readable({
      read() {
        this.push(buffer)
        this.push(null)
      },
    }),
  )
  return fileName
}

export {
  clearList,
  exportExcludeListToOSS,
  getExcludeIds,
  getExcludeList,
  getStatistic,
  processContent,
  processLinks,
  uploadList,
}
