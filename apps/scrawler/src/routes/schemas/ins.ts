import { InstagramSortType } from '@/types/instagram'
import { KolPlatform, TaskReason } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'
import { PaginatedListSchema } from './common'

// 用户信息基础 schema
const InstagramUserSchema = Type.Object({
  id: Type.String(),
  username: Type.String(),
  fullName: Type.String(),
  profilePicUrl: Type.String(),
  isPrivate: Type.Boolean(),
  isVerified: Type.Boolean(),
  mediaCount: Type.Number(),
  followerCount: Type.Number(),
  followingCount: Type.Number(),
  biography: Type.String(),
  externalUrl: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

/******** 创建hashtag任务  ***************/
export const InsHashTagBreakReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  tag: Type.String({ description: '标签名称' }),
  reason: Type.Optional(Type.String({ description: '任务原因' })),
  currentVideoCount: Type.Optional(Type.Number({ description: '当前视频数量', default: 0 })),
  maxVideoCount: Type.Optional(Type.Number({ description: '最大视频数量', default: 100 })),
  sortType: Type.Optional(
    Type.String({
      description: '排序类型',
      enum: Object.values(InstagramSortType),
      default: InstagramSortType.TOP,
    }),
  ),
})

export type InsHashTagBreakReq = Static<typeof InsHashTagBreakReqSchema>
export const InsHashTagBreakResSchema = Type.Object({
  uniqueIds: Type.Array(Type.String()),
  paginationToken: Type.String(),
  hasMore: Type.Boolean(),
  total: Type.Number(),
  message: Type.Optional(Type.String()),
})

/******** 获取hashtag列表  ***************/
export const InsHashTagListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 创建following任务  ***************/
export const InsFollowingListReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  username: Type.String({ description: 'Instagram 用户名' }),
  reason: Type.Optional(Type.String({ description: '任务原因' })),
  currentCount: Type.Optional(Type.Number({ description: '当前数量', default: 0 })),
  maxCount: Type.Optional(Type.Number({ description: '最大数量', default: 100 })),
})

export type InsFollowingListReq = Static<typeof InsFollowingListReqSchema>

/******** 获取following列表  ***************/
export const InsFollowingListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 创建tagged任务  ***************/
export const InsTaggedBreakReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  username: Type.String({ description: 'Instagram 用户名' }),
  reason: Type.Optional(Type.String({ description: '任务原因' })),
  currentVideoCount: Type.Optional(Type.Number({ description: '当前视频数量', default: 0 })),
  maxVideoCount: Type.Optional(Type.Number({ description: '最大视频数量', default: 100 })),
})

export type InsTaggedBreakReq = Static<typeof InsTaggedBreakReqSchema>

export const InsTaggedBreakResSchema = Type.Object({
  uniqueIds: Type.Array(Type.String()),
  paginationToken: Type.String(),
  hasMore: Type.Boolean(),
  total: Type.Number(),
  message: Type.Optional(Type.String()),
})

/******** 获取tagged列表  ***************/
export const InsTaggedListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 创建url-list任务  ***************/
export const InsWebListReqSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  urls: Type.Array(Type.String({ description: 'Instagram URL 列表' })),
  platform: Type.String({ description: '平台', enum: Object.values(KolPlatform) }),
  reason: Type.Optional(Type.String({ description: '任务原因', enum: Object.values(TaskReason) })),
})
export type InsWebListReq = Static<typeof InsWebListReqSchema>

/******** 获取url-list列表  ***************/
export const InsWebListResSchema = PaginatedListSchema(InstagramUserSchema)

/******** 假受众分析  ***************/
// 假受众分析请求 Schema
export const AudienceFakeReqSchema = Type.Object({
  handler: Type.String({
    description: 'Instagram 用户名',
    minLength: 1,
    examples: ['instagram_username'],
  }),
})

export type AudienceFakeReq = Static<typeof AudienceFakeReqSchema>

// 假受众分析响应 Schema
export const AudienceFakeResSchema = Type.Object({
  task: Type.Object({
    id: Type.String({ description: '任务ID' }),
    projectId: Type.String({ description: '项目ID' }),
    status: Type.String({
      description: '任务状态',
      enum: [
        'PENDING',
        'PROCESSING',
        'COMPLETED',
        'FAILED',
        'RESULT_READY',
        'PAUSED',
        'PAUSING',
        'COMPLETING',
      ],
    }),
    params: Type.Any({ description: '任务参数' }),
    result: Type.Optional(Type.Any({ description: '任务结果' })),
    errors: Type.Optional(Type.Any({ description: '任务错误信息' })),
    meta: Type.Optional(Type.Any({ description: '任务元数据' })),
    createdBy: Type.Optional(Type.String({ description: '创建者ID' })),
    createdAt: Type.String({ description: '创建时间' }),
    updatedAt: Type.String({ description: '更新时间' }),
    isTerminated: Type.Boolean({ description: '是否已终止' }),
    reason: Type.String({
      description: '任务原因',
      enum: [
        'SEARCH',
        'LIKE',
        'SUPERLIKE',
        'NEXT_PAGE',
        'AUDIENCE_ANALYSIS',
        'EASYKOL_TRACK',
        'AUDIENCE_FAKE',
        'POST_AUDIENCE',
      ],
    }),
    type: Type.String({
      description: '任务类型',
      enum: [
        'KEYWORD',
        'SIMILAR',
        'HASH_TAG_BREAK',
        'SEARCH_INPUT_BREAK',
        'FOLLOWERS_SIMILAR',
        'FOLLOWING_LIST',
        'AUDIENCE_ANALYSIS',
        'EASYKOL_TRACK',
        'BGM_BREAK',
        'WEB_LIST',
        'TAGGED_BREAK',
        'POST_AUDIENCE',
        'LONG_CRAWLER',
        'AUDIENCE_FAKE',
      ],
    }),
    strategyId: Type.Optional(Type.String({ description: '策略ID' })),
  }),
  fromCache: Type.Boolean({ description: '是否来自缓存' }),
})

export type AudienceFakeRes = Static<typeof AudienceFakeResSchema>

// 假受众分析导出请求 Schema
export const AudienceFakeExportReqSchema = Type.Object({
  taskId: Type.String({
    description: '任务ID',
    minLength: 1,
    examples: ['clxxx123456'],
  }),
})

export type AudienceFakeExportReq = Static<typeof AudienceFakeExportReqSchema>

// 假受众分析导出响应 Schema
export const AudienceFakeExportResSchema = Type.Object({
  url: Type.String({ description: 'Excel 文件下载链接' }),
  fromCache: Type.Boolean({ description: '是否来自缓存' }),
})

export type AudienceFakeExportRes = Static<typeof AudienceFakeExportResSchema>

export const PostLikeCountReqSchema = Type.Object({
  postId: Type.String({ description: '帖子ID' }),
})

export type PostLikeCountReq = Static<typeof PostLikeCountReqSchema>
