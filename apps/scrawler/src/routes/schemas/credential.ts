import { Static, Type } from '@sinclair/typebox'

export const CredentialSignatureReqSchema = Type.Object({
  browserId: Type.Optional(Type.String()),
  extensionId: Type.Optional(Type.String()),
})

export type CredentialSignatureReq = Static<typeof CredentialSignatureReqSchema>

export const CredentialReqSchema = Type.Object({
  accessToken: Type.String({ default: '{{accessToken}}' }),
  refreshToken: Type.String({ default: '{{refreshToken}}' }),
  provider: Type.String({ description: '目前只有GOOGLE，这个是provider表的key' }),
})

export type CredentialReq = Static<typeof CredentialReqSchema>

export const ProviderResponseSchema = Type.Object({
  key: Type.String(),
  type: Type.String(),
  clientId: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

export type ProviderResponse = Static<typeof ProviderResponseSchema>

export const ProviderCredentialResponseSchema = Type.Object({
  id: Type.String(),
  expiresAt: Type.String({ format: 'date-time' }),
  createdBy: Type.String(),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
  Provider: ProviderResponseSchema,
})

export type ProviderCredentialResponse = Static<typeof ProviderCredentialResponseSchema>
