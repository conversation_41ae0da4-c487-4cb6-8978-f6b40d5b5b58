import { Static, Type } from '@sinclair/typebox'

// 获取用户 Google Sheet 参数
export const GetUserGoogleSheetQuerySchema = Type.Object({
  userId: Type.Optional(Type.String({ description: '要查询的用户ID，不传则查询当前用户' })),
})

// 获取用户 Google Sheet 响应
export const GetUserGoogleSheetResponseSchema = Type.Object({
  id: Type.String(),
  userId: Type.String(),
  spreadsheetId: Type.String(),
  sheetUrl: Type.String(),
  title: Type.String(),
  status: Type.String(),
  lastSyncAt: Type.Union([Type.String(), Type.Null()]),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})
export type GetUserGoogleSheetQuery = Static<typeof GetUserGoogleSheetQuerySchema>
export type GetUserGoogleSheetResponse = Static<typeof GetUserGoogleSheetResponseSchema>
