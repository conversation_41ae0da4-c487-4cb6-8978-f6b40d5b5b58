import { Static, Type } from '@sinclair/typebox'

// 企业信息Schema
const EnterpriseSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  contactPerson: Type.Union([Type.String(), Type.Null()]),
  contactPhone: Type.Union([Type.String(), Type.Null()]),
  contactEmail: Type.Union([Type.String(), Type.Null()]),
  address: Type.Union([Type.String(), Type.Null()]),
  industry: Type.Union([Type.String(), Type.Null()]),
  scale: Type.Union([Type.String(), Type.Null()]),
  accountQuota: Type.Number(), // 企业总配额
  usedQuota: Type.Number(),
  dailyUsage: Type.Number(),
  dailyLimit: Type.Number(), // 每日总上限
  memberUsageDailyLimit: Type.Number(), // 企业内单个成员的每日使用额度上限
  description: Type.Union([Type.String(), Type.Null()]),
  status: Type.String(),
  effectiveAt: Type.String(),
  expireAt: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

// 会员信息Schema
const MembershipSchema = Type.Object({
  id: Type.String(),
  userId: Type.String(),
  type: Type.String(),
  status: Type.String(),
  timezone: Type.Union([Type.String(), Type.Null()]),
  effectiveAt: Type.Union([Type.String(), Type.Null()]),
  expireAt: Type.Union([Type.String(), Type.Null()]),
  accountQuota: Type.Number(),
  usedQuota: Type.Number(),
  dailyUsage: Type.Number(),
  lastResetAt: Type.Union([Type.String(), Type.Null()]),
  createdAt: Type.String(),
  updatedAt: Type.String(),
  enterpriseId: Type.Union([Type.String(), Type.Null()]),
  isEnterpriseAdmin: Type.Boolean(),
  enterpriseQuotaDailyLimit: Type.Number(),
  enableEnterpriseQuotaDailyLimit: Type.Boolean(),
  cardSubscriptionStatus: Type.Union([Type.String(), Type.Null()]),
  cardSubscriptionEffectiveAt: Type.Union([Type.String(), Type.Null()]),
  cardSubscriptionExpireAt: Type.Union([Type.String(), Type.Null()]),
  enterprise: Type.Union([EnterpriseSchema, Type.Null()]),
})

// Stripe客户Schema
const StripeCustomerSchema = Type.Object({
  id: Type.String(),
  userId: Type.String(),
  stripeCustomerId: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

// Stripe订阅Schema
const StripeSubscriptionSchema = Type.Object({
  id: Type.String(),
  userId: Type.String(),
  stripeSubscriptionId: Type.String(),
  status: Type.String(),
  planType: Type.String(),
  priceId: Type.String(),
  transitionStatus: Type.String(),
  currentPeriodStart: Type.Union([Type.String(), Type.Null()]),
  currentPeriodEnd: Type.Union([Type.String(), Type.Null()]),
  cancelAtPeriodEnd: Type.Boolean(),
  canceledAt: Type.Union([Type.String(), Type.Null()]),
  paymentScheme: Type.Union([Type.String(), Type.Null()]),
  paymentMethod: Type.Union([Type.String(), Type.Null()]),
  isOneTimePayment: Type.Boolean(),
  currency: Type.String(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

// 可用计划Schema
const PlanSchema = Type.Object({
  id: Type.String(),
  priceId: Type.String(),
  scheme: Type.String(),
  type: Type.String(),
  amount: Type.Number(),
  currency: Type.String(),
  paymentMethods: Type.Array(Type.String()),
  isSubscription: Type.Boolean(),
  description: Type.String(),
  validityPeriod: Type.Number(),
  validityUnit: Type.String(),
  quotaAmount: Type.Number(),
})

// 支付信息Schema
const PaymentInfoSchema = Type.Object({
  recommendedScheme: Type.String(),
  availablePlans: Type.Array(PlanSchema),
  hasHistory: Type.Boolean(),
})

// UserInfo响应Schema
export const UserInfoResponseSchema = Type.Object({
  userId: Type.String(),
  email: Type.Union([Type.String(), Type.Null()]),
  phoneNumber: Type.Union([Type.String(), Type.Null()]),
  avatar: Type.Union([Type.String(), Type.Null()]),
  supabase: Type.Union([Type.Any(), Type.Null()]), // Json field
  createdAt: Type.String(),
  updatedAt: Type.String(),
  membership: Type.Union([MembershipSchema, Type.Null()]),
  stripeCustomer: Type.Union([StripeCustomerSchema, Type.Null()]),
  stripeSubscriptions: Type.Array(StripeSubscriptionSchema),
  isAdmin: Type.Boolean(),
  paymentInfo: PaymentInfoSchema,
})

export type UserInfoResponse = Static<typeof UserInfoResponseSchema>
