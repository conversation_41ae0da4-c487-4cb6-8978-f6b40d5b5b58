import { ProjectKolAttitude, SimilarChannelTaskStatus, TaskReason, TaskType } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'

export const WebExportParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务 ID' }),
})

export type WebExportParams = Static<typeof WebExportParamsSchema>

// 长时间爬取任务的请求schema
// Instagram长时间爬取过滤器 Schema (提取为独立定义)
const InsLongCrawlerFiltersSchema = Type.Object({
  // required
  kolDescription: Type.String({
    description: '内容类别描述',
    default: '',
  }),
  // optional
  followerRange: Type.Optional(
    Type.Object({
      min: Type.Optional(Type.Number({ description: '最小粉丝数', minimum: 0 })),
      max: Type.Optional(Type.Number({ description: '最大粉丝数', minimum: 0 })),
    }),
  ),
  // required
  regions: Type.Array(Type.String({ description: '地区/国家代码' }), {
    description: '地区/国家筛选',
    default: [],
  }),
  // optional
  averageLikeCount: Type.Optional(
    Type.Object({
      min: Type.Optional(Type.Number({ description: '最小平均点赞数', minimum: 0 })),
      max: Type.Optional(Type.Number({ description: '最大平均点赞数', minimum: 0 })),
    }),
  ),
})

// YouTube长时间爬取过滤器 Schema
const YtbLongCrawlerFiltersSchema = Type.Object({
  // required
  kolDescription: Type.String({
    description: '内容类别描述',
    default: '',
  }),
  // optional
  followerRange: Type.Optional(
    Type.Object({
      min: Type.Optional(Type.Number({ description: '最小订阅数', minimum: 0 })),
      max: Type.Optional(Type.Number({ description: '最大订阅数', minimum: 0 })),
    }),
  ),
  // required
  regions: Type.Array(Type.String({ description: '地区/国家代码' }), {
    description: '地区/国家筛选',
    default: [],
  }),
  // optional
  averagePlay: Type.Optional(
    Type.Object({
      min: Type.Optional(Type.Number({ description: '最小平均播放量', minimum: 0 })),
      max: Type.Optional(Type.Number({ description: '最大平均播放量', minimum: 0 })),
    }),
  ),
})

// 通用长时间爬取请求Schema - 使用联合类型支持不同平台
export const LongCrawlerRequestSchema = Type.Union([
  // Instagram平台
  Type.Object({
    platform: Type.Literal('INSTAGRAM'),
    projectId: Type.String({ description: '项目ID' }),
    filters: InsLongCrawlerFiltersSchema,
    seedUsers: Type.Array(Type.String(), {
      description: '种子用户列表',
      minItems: 1,
    }),
    numberOfRuns: Type.Number({
      description: '消耗的任务次数',
      minimum: 1,
      default: 10,
    }),
  }),
  // YouTube平台
  Type.Object({
    platform: Type.Literal('YOUTUBE'),
    projectId: Type.String({ description: '项目ID' }),
    filters: YtbLongCrawlerFiltersSchema,
    seedUsers: Type.Array(Type.String(), {
      description: '种子用户列表',
      minItems: 1,
    }),
    numberOfRuns: Type.Number({
      description: '消耗的任务次数',
      minimum: 1,
      default: 10,
    }),
  }),
])

export type LongCrawlerRequest = Static<typeof LongCrawlerRequestSchema>

// 任务状态查询参数schema
export const TaskStatusQueryParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
})

export type TaskStatusQueryParams = Static<typeof TaskStatusQueryParamsSchema>

// 任务列表查询参数schema
export const TaskListQueryParamsSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  taskType: Type.Optional(Type.String({ description: '任务类型' })),
  page: Type.Optional(Type.Number({ description: '页码', minimum: 1, default: 1 })),
  pageSize: Type.Optional(
    Type.Number({ description: '每页数量', minimum: 1, maximum: 100, default: 10 }),
  ),
})

export type TaskListQueryParams = Static<typeof TaskListQueryParamsSchema>

// 任务进度schema
export const TaskProgressSchema = Type.Object({
  processedUsers: Type.Array(Type.String(), { description: '已处理的用户列表' }),
  pendingUsers: Type.Array(Type.String(), { description: '待处理的用户列表' }),
  matchedUsers: Type.Array(Type.String(), { description: '匹配的用户列表' }),
  totalProcessed: Type.Number({ description: '总处理数量' }),
  totalMatched: Type.Number({ description: '总匹配数量' }),
  lastProcessedAt: Type.String({ description: '最后处理时间' }),
  currentBatch: Type.Number({ description: '当前批次' }),
})

export type TaskProgress = Static<typeof TaskProgressSchema>

export const LongCrawlerExportSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
  type: Type.String({
    description: '导出类型，contentOk表示导出Content-OK用户，perfectFit表示导出Perfect-Fit用户',
    enum: ['contentOk', 'perfectFit'],
  }),
})

export type LongCrawlerExportRequest = Static<typeof LongCrawlerExportSchema>

export const UpdateLongCrawlerParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
  params: Type.Object({
    addNumberOfRuns: Type.Optional(
      Type.Number({
        description: '增加消耗任务次数',
        minimum: 1,
      }),
    ),
    kolDescription: Type.Optional(
      Type.String({
        description: '更新内容类别描述',
      }),
    ),
  }),
})

export type UpdateLongCrawlerParams = Static<typeof UpdateLongCrawlerParamsSchema>

// 长时间爬取任务用户查询参数 Schema
export const LongCrawlerUsersParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
})

export const LongCrawlerUsersQuerySchema = Type.Object({
  page: Type.Optional(Type.Number({ description: '页码', minimum: 1, default: 1 })),
  pageSize: Type.Optional(
    Type.Number({ description: '每页数量', minimum: 1, maximum: 200, default: 100 }),
  ),
  attitude: Type.Optional(
    Type.String({
      description: '用户标记状态过滤',
      enum: ['LIKE', 'DISLIKE', 'NORATE', 'SUPERLIKE'],
    }),
  ),
  type: Type.String({
    enum: ['perfectFit', 'contentOk'],
    description: '用户类型：perfectFit表示Perfect-Fit用户，contentOk表示Content-OK用户',
  }),
})

export type LongCrawlerUsersParams = Static<typeof LongCrawlerUsersParamsSchema>
export type LongCrawlerUsersQuery = Static<typeof LongCrawlerUsersQuerySchema>

// ==================== 响应 Schema 定义 ====================

// 长时间爬取任务创建响应 Schema
export const LongCrawlerTaskResponseSchema = Type.Object({
  id: Type.String({ description: '任务ID' }),
  projectId: Type.String({ description: '项目ID' }),
  status: Type.Enum(SimilarChannelTaskStatus, { description: '任务状态' }),
  params: Type.Any({ description: '任务参数' }),
  result: Type.Optional(Type.Any({ description: '任务结果' })),
  errors: Type.Optional(Type.Any({ description: '错误信息' })),
  meta: Type.Optional(Type.Any({ description: '元数据' })),
  createdBy: Type.Optional(Type.String({ description: '创建者ID' })),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
  reason: Type.Enum(TaskReason, { description: '任务原因' }),
  type: Type.Enum(TaskType, { description: '任务类型' }),
  strategyId: Type.Optional(Type.String({ description: '策略ID' })),
  candidate: Type.Optional(Type.Any({ description: '候选数据' })),
})

export type LongCrawlerTaskResponse = Static<typeof LongCrawlerTaskResponseSchema>

// Instagram 帖子 Schema
const InstagramPostSchema = Type.Object(
  {
    id: Type.Union([Type.String(), Type.Null()], { description: '帖子ID' }),
    comment_count: Type.Number({ description: '评论数', minimum: 0 }),
    like_count: Type.Number({ description: '点赞数', minimum: 0 }),
    play_count: Type.Number({ description: '播放数', minimum: 0 }),
    created_at: Type.Number({ description: '创建时间戳', minimum: 0 }),
    caption: Type.Optional(Type.String({ description: '帖子描述' })),
    thumbnail_url: Type.Optional(Type.String({ description: '缩略图URL' })),
    video_url: Type.Optional(Type.String({ description: '视频URL' })),
    code: Type.Optional(Type.String({ description: '帖子代码' })),
    media_type: Type.Optional(Type.Number({ description: '媒体类型: 1-图片, 2-视频, 8-轮播图' })),
    media_name: Type.Optional(Type.String({ description: '媒体名称: post, reel' })),
    is_pinned: Type.Optional(Type.Boolean({ description: '是否置顶' })),
  },
  {
    description: 'Instagram帖子信息',
    additionalProperties: true,
  },
)

// YouTube 视频 Schema - 基于ChannelVideo类型
const YouTubeVideoSchema = Type.Object(
  {
    type: Type.Optional(Type.String({ description: '视频类型' })),
    videoId: Type.String({ description: '视频ID' }),
    title: Type.String({ description: '视频标题' }),
    lengthText: Type.Optional(Type.String({ description: '视频时长文本' })),
    viewCount: Type.String({ description: '观看次数' }),
    publishedTimeText: Type.Optional(Type.String({ description: '发布时间文本' })),
    publishedAt: Type.String({ description: '发布时间' }),
    thumbnail: Type.Optional(Type.Array(Type.Any(), { description: '缩略图数组' })),
    publishedDate: Type.Optional(Type.String({ description: '发布日期' })),
    description: Type.String({ description: '视频描述' }),
    richThumbnail: Type.Optional(Type.Array(Type.Any(), { description: '高质量缩略图数组' })),
    channelId: Type.Optional(Type.String({ description: '频道ID' })),
  },
  {
    description: 'YouTube视频信息 - 基于ChannelVideo类型',
    additionalProperties: true,
  },
)

// Instagram 用户信息 Schema
const InstagramUserSchema = Type.Object(
  {
    id: Type.String({ description: 'Instagram用户ID' }),
    username: Type.String({ description: 'Instagram用户名' }),
    profilePicUrl: Type.Union([Type.String(), Type.Null()], { description: '头像URL' }),
    fullName: Type.Union([Type.String(), Type.Null()], { description: '全名' }),
    followerCount: Type.Number({ description: '粉丝数', minimum: 0 }),
    averageLikeCount: Type.Number({ description: '平均点赞数', minimum: 0 }),
    averageCommentCount: Type.Number({ description: '平均评论数', minimum: 0 }),
    lastPublishedTime: Type.Number({ description: '最后发布时间戳', minimum: 0 }),
    region: Type.Union([Type.String(), Type.Null()], { description: '地区' }),
    createdAt: Type.String({ description: '数据库创建时间', format: 'date-time' }),
    updatedAt: Type.String({ description: '数据库更新时间', format: 'date-time' }),
    posts: Type.Array(InstagramPostSchema, {
      description: '帖子列表，按创建时间降序排列',
      default: [],
    }),
    accountInfo: Type.Optional(Type.Any({ description: '账户详细信息' })),
    publicationStats: Type.Optional(Type.Any({ description: '发布统计数据' })),
  },
  {
    description: 'Instagram用户详细信息',
    additionalProperties: true,
  },
)

// YouTube 频道信息 Schema - 严格对应数据库YouTubeChannel表
const YouTubeChannelSchema = Type.Object(
  {
    channelId: Type.String({ description: 'YouTube频道ID' }),
    channelName: Type.String({ description: '频道名称' }),
    channelHandle: Type.String({ description: '频道句柄' }),
    channelDescription: Type.String({ description: '频道描述' }),
    numericSubscriberCount: Type.Number({ description: '订阅数', minimum: 0 }),
    country: Type.Union([Type.String(), Type.Null()], { description: '国家/地区' }),
    haveCrawlered: Type.Boolean({ description: '是否已爬取' }),
    videosAverageViewCount: Type.Number({ description: '平均观看数', minimum: 0 }),
    officialEmail: Type.Boolean({ description: '是否有官方邮箱' }),
    createdAt: Type.String({ description: '数据库创建时间', format: 'date-time' }),
    updatedAt: Type.String({ description: '数据库更新时间', format: 'date-time' }),
    lastPublishedTime: Type.Number({ description: '最后发布时间戳', minimum: 0 }),
    videos: Type.Union([Type.Array(YouTubeVideoSchema), Type.Null()], {
      description: '视频列表JSON数据，按发布时间降序排列',
      default: [],
    }),
    publicationStats: Type.Union([Type.Any(), Type.Null()], {
      description: '发布统计数据JSON',
      default: {},
    }),
  },
  {
    description: 'YouTube频道详细信息 - 对应数据库YouTubeChannel表结构',
    additionalProperties: true,
  },
)

// 项目KOL态度 Schema
const ProjectKolSchema = Type.Object(
  {
    attitude: Type.Union(
      [
        Type.Literal('LIKE'),
        Type.Literal('DISLIKE'),
        Type.Literal('NORATE'),
        Type.Literal('SUPERLIKE'),
      ],
      { description: '用户对该KOL的态度评价' },
    ),
  },
  {
    description: '项目中KOL的态度评价信息',
    additionalProperties: true,
  },
)

// KolInfo 完整数据 Schema - 支持多平台
const UserDataSchema = Type.Object(
  {
    id: Type.String({ description: 'KOL信息ID (cuid)' }),
    title: Type.Union([Type.String(), Type.Null()], { description: 'KOL标题/名称' }),
    description: Type.Union([Type.String(), Type.Null()], { description: 'KOL描述/简介' }),
    email: Type.Union([Type.String(), Type.Null()], { description: 'KOL邮箱地址' }),
    emailSource: Type.Union([Type.String(), Type.Null()], { description: '邮箱来源' }),
    historyEmails: Type.Array(Type.String(), { description: '历史邮箱列表', default: [] }),
    platformAccount: Type.Union([Type.String(), Type.Null()], { description: '平台账号名' }),
    platform: Type.Union(
      [
        Type.Literal('INSTAGRAM'),
        Type.Literal('YOUTUBE'),
        Type.Literal('TIKTOK'),
        Type.Literal('TWITTER'),
      ],
      { description: '平台类型' },
    ),
    createdAt: Type.String({ description: 'KOL信息创建时间', format: 'date-time' }),
    updatedAt: Type.String({ description: 'KOL信息更新时间', format: 'date-time' }),
    avatar: Type.Union([Type.String(), Type.Null()], { description: '头像URL' }),
    links: Type.Array(Type.String(), { description: '相关链接列表', default: [] }),
    contacts: Type.Union([Type.Any(), Type.Null()], { description: '联系方式信息 (JSON)' }),
    emailUpdatedAt: Type.Union([Type.String({ format: 'date-time' }), Type.Null()], {
      description: '邮箱更新时间',
    }),
    audienceAnalysis: Type.Union([Type.Any(), Type.Null()], {
      description: '受众分析数据 (JSON)',
      default: {},
    }),
    // 平台特定数据 - 根据platform字段确定哪个字段有值
    instagramUser: Type.Union([InstagramUserSchema, Type.Null()], {
      description: 'Instagram用户详细信息，当platform为INSTAGRAM时有值',
    }),
    youtubeChannel: Type.Union([YouTubeChannelSchema, Type.Null()], {
      description: 'YouTube频道详细信息，当platform为YOUTUBE时有值',
    }),
    ProjectKol: Type.Union([ProjectKolSchema, Type.Null()], {
      description: '该KOL在当前项目和任务中的态度评价信息',
    }),
    reason: Type.Optional(Type.String({ description: 'AI给出的匹配原因' })),
    // YouTube特有字段
    videoIds: Type.Optional(Type.Array(Type.String(), { description: 'YouTube频道的视频ID列表' })),
  },
  {
    description: '匹配到的KOL完整信息 - 支持Instagram和YouTube平台',
    additionalProperties: true,
  },
)

// Instagram长时间爬取批次日志 Schema
const InsLongCrawlerBatchLogsSchema = Type.Object({
  batchNumber: Type.Number({ description: '批次号' }),
  processedUsers: Type.Array(Type.String(), { description: '已处理用户列表' }),
  relatedUserNames: Type.Array(Type.String(), { description: '相关用户总数' }),
  uniqueRelatedUserNames: Type.Array(Type.String(), { description: '去重后的相关用户总数' }),
  perfectFitUserNames: Type.Array(Type.String(), { description: 'AI相似符合用户' }),
  contentOkUserNames: Type.Array(Type.String(), { description: '完全匹配用户' }),
  quotaCost: Type.Number({ description: '消耗的quota次数' }),
  numberOfRuns: Type.Number({ description: '运行次数' }),
  timestamp: Type.String({ description: '时间戳', format: 'date-time' }),
  success: Type.Boolean({ description: '是否成功' }),
  message: Type.Optional(Type.String({ description: '日志消息' })),
})

// YouTube长时间爬取批次日志 Schema
const YtbLongCrawlerBatchLogsSchema = Type.Object({
  batchNumber: Type.Number({ description: '批次号' }),
  processedUsers: Type.Array(Type.String(), { description: '已处理用户列表' }),
  relatedChannelIds: Type.Array(Type.String(), { description: '相关频道总数' }),
  uniqueRelatedChannelIds: Type.Array(Type.String(), { description: '去重后的相关频道总数' }),
  perfectFitChannelIds: Type.Array(Type.String(), { description: 'AI相似符合频道' }),
  contentOkChannelIds: Type.Array(Type.String(), { description: '完全匹配频道' }),
  quotaCost: Type.Number({ description: '消耗的quota次数' }),
  numberOfRuns: Type.Number({ description: '运行次数' }),
  timestamp: Type.String({ description: '时间戳', format: 'date-time' }),
  success: Type.Boolean({ description: '是否成功' }),
  message: Type.Optional(Type.String({ description: '日志消息' })),
})

// Instagram长时间爬取进度 Schema
const InsLongCrawlerProgressSchema = Type.Object({
  processedUsers: Type.Array(Type.String(), { description: '已处理的用户列表' }),
  pendingUsersQueue: Type.Any({ description: '序列化的优先级队列数据' }),
  allPerfectFitUserNames: Type.Array(Type.String(), { description: '所有Perfect-Fit用户列表' }),
  allContentOkUserNames: Type.Array(Type.String(), { description: '所有Content-OK用户列表' }),
  lastProcessedAt: Type.String({ description: '最后处理时间', format: 'date-time' }),
  currentBatch: Type.Number({ description: '当前批次号' }),
  batchLogs: Type.Optional(
    Type.Array(InsLongCrawlerBatchLogsSchema, { description: '批次处理日志' }),
  ),
  hasConsumedQuotaCount: Type.Number({ description: '已消耗的quota次数' }),
})

// YouTube长时间爬取进度 Schema
const YtbLongCrawlerProgressSchema = Type.Object({
  processedUsers: Type.Array(Type.String(), { description: '已处理的用户列表' }),
  pendingUsersQueue: Type.Any({ description: '序列化的优先级队列数据' }),
  allPerfectFitUserNames: Type.Array(Type.String(), { description: '所有Perfect-Fit用户列表' }),
  allContentOkUserNames: Type.Array(Type.String(), { description: '所有Content-OK用户列表' }),
  lastProcessedAt: Type.String({ description: '最后处理时间', format: 'date-time' }),
  currentBatch: Type.Number({ description: '当前批次号' }),
  batchLogs: Type.Optional(
    Type.Array(YtbLongCrawlerBatchLogsSchema, { description: '批次处理日志' }),
  ),
  hasConsumedQuotaCount: Type.Number({ description: '已消耗的quota次数' }),
})

// Instagram长时间爬取任务参数 Schema
const InsLongCrawlerTaskParamsSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  platform: Type.String({ description: '平台', enum: ['instagram'] }),
  filters: InsLongCrawlerFiltersSchema,
  seedUsers: Type.Array(Type.String(), { description: '种子用户列表' }),
  maxQuotaCost: Type.Number({ description: '最大配额消耗' }),
})

// YouTube长时间爬取任务参数 Schema
const YtbLongCrawlerTaskParamsSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  platform: Type.String({ description: '平台', enum: ['youtube'] }),
  filters: YtbLongCrawlerFiltersSchema,
  seedUsers: Type.Array(Type.String(), { description: '种子用户列表' }),
  maxQuotaCost: Type.Number({ description: '最大配额消耗' }),
})

// 任务状态响应 Schema - 使用联合类型支持不同平台
export const TaskStatusResponseSchema = Type.Union([
  // Instagram任务状态响应
  Type.Object({
    taskId: Type.String({ description: '任务ID' }),
    type: Type.Enum(TaskType, { description: '任务类型' }),
    reason: Type.Enum(TaskReason, { description: '任务原因' }),
    status: Type.Enum(SimilarChannelTaskStatus, { description: '任务状态' }),
    meta: Type.Optional(InsLongCrawlerProgressSchema),
    params: InsLongCrawlerTaskParamsSchema,
    latestPerfectFitUsers: Type.Array(UserDataSchema, {
      description: '最新5个Perfect-Fit用户的详细数据',
    }),
    latestContentOkUsers: Type.Array(UserDataSchema, {
      description: '最新5个Content-OK用户的详细数据',
    }),
    createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
    updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
  }),
  // YouTube任务状态响应
  Type.Object({
    taskId: Type.String({ description: '任务ID' }),
    type: Type.Enum(TaskType, { description: '任务类型' }),
    reason: Type.Enum(TaskReason, { description: '任务原因' }),
    status: Type.Enum(SimilarChannelTaskStatus, { description: '任务状态' }),
    meta: Type.Optional(YtbLongCrawlerProgressSchema),
    params: YtbLongCrawlerTaskParamsSchema,
    latestPerfectFitUsers: Type.Array(UserDataSchema, {
      description: '最新5个Perfect-Fit用户的详细数据',
    }),
    latestContentOkUsers: Type.Array(UserDataSchema, {
      description: '最新5个Content-OK用户的详细数据',
    }),
    createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
    updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
  }),
])

export type TaskStatusResponse = Static<typeof TaskStatusResponseSchema>

// 导出响应 Schema
export const LongCrawlerExportResponseSchema = Type.String({
  description: 'Excel文件下载URL',
})

export type LongCrawlerExportResponse = Static<typeof LongCrawlerExportResponseSchema>

// 更新任务参数响应 Schema - 使用联合类型支持不同平台
export const UpdateLongCrawlerParamsResponseSchema = Type.Union([
  // Instagram更新响应
  Type.Object({
    taskId: Type.String({ description: '任务ID' }),
    params: InsLongCrawlerTaskParamsSchema,
    addedQuota: Type.Optional(Type.Number({ description: '增加的配额' })),
    newMaxQuotaCost: Type.Optional(Type.Number({ description: '新的最大配额成本' })),
    updatedKolDescription: Type.Optional(Type.String({ description: '更新的内容类别描述' })),
  }),
  // YouTube更新响应
  Type.Object({
    taskId: Type.String({ description: '任务ID' }),
    params: YtbLongCrawlerTaskParamsSchema,
    addedQuota: Type.Optional(Type.Number({ description: '增加的配额' })),
    newMaxQuotaCost: Type.Optional(Type.Number({ description: '新的最大配额成本' })),
    updatedKolDescription: Type.Optional(Type.String({ description: '更新的内容类别描述' })),
  }),
])

export type UpdateLongCrawlerParamsResponse = Static<typeof UpdateLongCrawlerParamsResponseSchema>

// 长时间爬取任务用户响应 Schema
export const LongCrawlerUsersResponseSchema = Type.Object({
  data: Type.Array(UserDataSchema, { description: '用户数据列表' }),
  total: Type.Number({ description: '总数量' }),
  page: Type.Number({ description: '当前页码' }),
  pageSize: Type.Number({ description: '每页数量' }),
  totalPages: Type.Number({ description: '总页数' }),
  hasMore: Type.Boolean({ description: '是否有更多数据' }),
  attitude: Type.Object(
    {
      like: Type.Number({ description: 'LIKE态度的用户数量' }),
      dislike: Type.Number({ description: 'DISLIKE态度的用户数量' }),
      superlike: Type.Number({ description: 'SUPERLIKE态度的用户数量' }),
      norate: Type.Number({ description: 'NORATE态度的用户数量（包括未评价的用户）' }),
    },
    { description: '用户态度统计' },
  ),
})

export type LongCrawlerUsersResponse = Static<typeof LongCrawlerUsersResponseSchema>

// ==================== 长时间爬取任务评分接口 Schema ====================

// 长时间爬取任务评分请求参数 Schema
export const LongCrawlerRateParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
})

export const LongCrawlerRateBodySchema = Type.Object({
  kolId: Type.String({ description: 'KOL ID' }),
  attitude: Type.Enum(ProjectKolAttitude, { description: '态度评分' }),
})

// 长时间爬取任务评分响应数据 Schema（data 字段的内容）
export const LongCrawlerRateDataSchema = Type.Object({
  id: Type.String({ description: '项目KOL关系ID' }),
  projectId: Type.String({ description: '项目ID' }),
  kolId: Type.String({ description: 'KOL ID' }),
  similarTaskId: Type.String({ description: '相似任务ID' }),
  attitude: Type.Enum(ProjectKolAttitude, { description: '态度评分' }),
  rateBy: Type.String({ description: '评分用户ID' }),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
  isAlreadyProcessed: Type.Boolean({ description: '用户是否已被处理' }),
})

export type LongCrawlerRateParams = Static<typeof LongCrawlerRateParamsSchema>
export type LongCrawlerRateBody = Static<typeof LongCrawlerRateBodySchema>
export type LongCrawlerRateData = Static<typeof LongCrawlerRateDataSchema>
