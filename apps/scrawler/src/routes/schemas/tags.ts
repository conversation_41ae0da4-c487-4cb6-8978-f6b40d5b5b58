import { Static, Type } from '@sinclair/typebox'

// ==================== 标签列表相关 Schema ====================

// 标签列表查询参数 Schema
export const TagListQuerySchema = Type.Object({
  userId: Type.Optional(Type.String({ description: '要查询的用户ID，不传则查询当前用户' })),
})

export type TagListQuery = Static<typeof TagListQuerySchema>

// 单个标签响应 Schema
export const TagResponseSchema = Type.Object({
  id: Type.String({ description: '标签ID' }),
  tagId: Type.String({ description: '标签ID（兼容字段）' }),
  name: Type.String({ description: '标签名称' }),
  color: Type.String({ description: '标签颜色' }),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
})

export type TagResponse = Static<typeof TagResponseSchema>

// 标签列表响应 Schema
export const TagListResponseSchema = Type.Object({
  items: Type.Array(TagResponseSchema, { description: '标签列表' }),
  total: Type.Number({ description: '总数量' }),
})

export type TagListResponse = Static<typeof TagListResponseSchema>
