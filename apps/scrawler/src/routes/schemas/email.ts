import { Static, Type } from '@sinclair/typebox'

export const CreateTemplateRequestSchema = Type.Object({
  name: Type.String({ description: '模板名称' }),
  subject: Type.Optional(Type.String({ description: '邮件主题' })),
  content: Type.Optional(Type.String({ description: '邮件内容' })),
  tags: Type.Optional(Type.Array(Type.String({ description: '标签' }))),
  cc: Type.Optional(Type.Array(Type.String({ description: '抄送邮箱' }))),
})

export type CreateTemplateRequest = Static<typeof CreateTemplateRequestSchema>

export const UpdateTemplateRequestSchema = Type.Object({
  name: Type.Optional(Type.String({ description: '模板名称' })),
  subject: Type.Optional(Type.String({ description: '邮件主题' })),
  content: Type.Optional(Type.String({ description: '邮件内容' })),
  tags: Type.Optional(Type.Array(Type.String({ description: '标签' }))),
  cc: Type.Optional(Type.Array(Type.String({ description: '抄送邮箱' }))),
})

export type UpdateTemplateRequest = Static<typeof UpdateTemplateRequestSchema>

export const EmailTemplateSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  subject: Type.String(),
  content: Type.String(),
  tags: Type.Array(Type.String()),
  createdAt: Type.String(),
  updatedAt: Type.String(),
})

export type EmailTemplate = Static<typeof EmailTemplateSchema>

export const SendEmailRequestSchema = Type.Object({
  email: Type.Optional(Type.String({ description: '收件人邮箱' })),
  templateId: Type.String({ description: '模板ID', examples: ['{{templateId}}'] }),
  kolId: Type.String({ description: 'KOL ID', examples: ['{{kolId}}'] }),
  usingEmail: Type.String({ description: '使用邮箱' }),
  projectId: Type.String({ description: '项目ID', examples: ['{{projectId}}'] }),
})
