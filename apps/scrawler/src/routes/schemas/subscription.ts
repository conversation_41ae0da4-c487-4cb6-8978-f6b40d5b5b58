import { Static, Type } from '@sinclair/typebox'

// ==================== 创建结账会话 ====================
// 请求 Schema
export const CreateCheckoutRequestSchema = Type.Object(
  {
    priceId: Type.String({
      description: '价格ID',
      minLength: 1,
    }),
  },
  {
    additionalProperties: false,
  },
)

export type CreateCheckoutRequest = Static<typeof CreateCheckoutRequestSchema>

// 响应 Schema
export const CreateCheckoutResponseSchema = Type.Object({
  url: Type.String({ description: 'Stripe Checkout会话URL' }),
  sessionId: Type.String({ description: '会话ID' }),
})

export type CreateCheckoutResponse = Static<typeof CreateCheckoutResponseSchema>

// ==================== 取消订阅 ====================
// 响应 Schema
export const CancelSubscriptionResponseSchema = Type.Any()

export type CancelSubscriptionResponse = Static<typeof CancelSubscriptionResponseSchema>

// ==================== 恢复订阅 ====================
// 响应 Schema
export const ResubscribeResponseSchema = Type.Any()

export type ResubscribeResponse = Static<typeof ResubscribeResponseSchema>

// ==================== 订阅状态查询 ====================
// 响应 Schema
export const SubscriptionStatusResponseSchema = Type.Union([
  Type.Object({
    id: Type.String(),
    userId: Type.String(),
    stripeSubscriptionId: Type.String(),
    status: Type.String(),
    planType: Type.String(),
    priceId: Type.String(),
    currentPeriodStart: Type.String(),
    currentPeriodEnd: Type.String(),
    cancelAtPeriodEnd: Type.Boolean(),
    canceledAt: Type.Union([Type.String(), Type.Null()]),
  }),
  Type.Object({
    status: Type.Literal('INACTIVE'),
  }),
])

export type SubscriptionStatusResponse = Static<typeof SubscriptionStatusResponseSchema>

// ==================== 创建客户门户 ====================
// 响应 Schema
export const CreatePortalResponseSchema = Type.Object({
  url: Type.String({ description: '客户门户URL' }),
})

export type CreatePortalResponse = Static<typeof CreatePortalResponseSchema>

// ==================== 验证会话 ====================
// 请求 Schema
export const VerifySessionQuerySchema = Type.Object(
  {
    sessionId: Type.String({ description: 'Checkout会话ID', minLength: 1 }),
  },
  {
    additionalProperties: false,
  },
)

export type VerifySessionQuery = Static<typeof VerifySessionQuerySchema>

// 响应 Schema
export const VerifySessionResponseSchema = Type.Object({
  id: Type.String(),
  object: Type.Literal('checkout.session'),
  amount_subtotal: Type.Number(),
  amount_total: Type.Number(),
  automatic_tax: Type.Object({
    enabled: Type.Boolean(),
    liability: Type.Optional(
      Type.Object({
        type: Type.String(),
      }),
    ),
    provider: Type.Optional(Type.String()),
    status: Type.Optional(Type.String()),
  }),
  cancel_url: Type.String(),
  client_reference_id: Type.Optional(Type.String()),
  created: Type.Number(),
  currency: Type.String(),
  customer: Type.Optional(Type.String()),
  customer_details: Type.Optional(
    Type.Object({
      address: Type.Optional(
        Type.Object({
          city: Type.Optional(Type.Union([Type.String(), Type.Null()])),
          country: Type.Optional(Type.Union([Type.String(), Type.Null()])),
          line1: Type.Optional(Type.Union([Type.String(), Type.Null()])),
          line2: Type.Optional(Type.Union([Type.String(), Type.Null()])),
          postal_code: Type.Optional(Type.Union([Type.String(), Type.Null()])),
          state: Type.Optional(Type.Union([Type.String(), Type.Null()])),
        }),
      ),
      email: Type.Optional(Type.String()),
      name: Type.Optional(Type.String()),
      phone: Type.Optional(Type.Union([Type.String(), Type.Null()])),
      tax_exempt: Type.Optional(Type.String()),
      tax_ids: Type.Optional(Type.Array(Type.Any())),
    }),
  ),
  expires_at: Type.Number(),
  invoice: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  livemode: Type.Boolean(),
  metadata: Type.Optional(Type.Record(Type.String(), Type.String())),
  mode: Type.Union([Type.Literal('payment'), Type.Literal('subscription'), Type.Literal('setup')]),
  payment_status: Type.String(),
  status: Type.String(),
  subscription: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  success_url: Type.String(),
  url: Type.Optional(Type.Union([Type.String(), Type.Null()])),
})

export type VerifySessionResponse = Static<typeof VerifySessionResponseSchema>

// ==================== 订阅状态轮询 ====================
// 请求 Schema
export const SubscriptionPollingQuerySchema = Type.Object(
  {
    subscriptionId: Type.String({ description: '订阅ID' }),
  },
  {
    additionalProperties: false,
  },
)

export type SubscriptionPollingQuery = Static<typeof SubscriptionPollingQuerySchema>

// 响应 Schema
export const SubscriptionPollingResponseSchema = Type.Boolean()

export type SubscriptionPollingResponse = Static<typeof SubscriptionPollingResponseSchema>
