import { KolPlatform } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'
import { FastifySchema } from 'fastify'
import { responseSchema } from './common'

export const addTagToPublicationSchema: FastifySchema = {
  body: Type.Object({
    publicationId: Type.String({ description: '投放ID' }),
    tagId: Type.String({ description: '标签ID' }),
  }),
  response: responseSchema,
}

export const removeTagFromPublicationSchema: FastifySchema = {
  body: Type.Object({
    publicationId: Type.String({ description: '投放ID' }),
    tagId: Type.String({ description: '标签ID' }),
  }),
  response: responseSchema,
}

export const getPublicationTagsSchema: FastifySchema = {
  params: Type.Object({
    publicationId: Type.String({ description: '投放ID' }),
  }),
  response: responseSchema,
}

export const GetTagPublicationsRequestSchema = Type.Object({
  userId: Type.Optional(Type.String({ description: '要查询的用户ID，不传则查询当前用户' })),
  page: Type.Optional(Type.Number({ minimum: 1, description: '页码，从1开始', default: 1 })),
  pageSize: Type.Optional(
    Type.Number({ minimum: 1, maximum: 100, description: '每页数量，最大100', default: 20 }),
  ),
  ids: Type.Optional(Type.Array(Type.String(), { description: '标签ID列表' })),
  platforms: Type.Optional(
    Type.Array(
      Type.Union(
        Object.values(KolPlatform).map((platform) => Type.Literal(platform)),
        { description: '平台列表' },
      ),
    ),
  ),
})

// 标签信息 Schema
export const TagInfoSchema = Type.Object({
  id: Type.String({ description: '标签ID' }),
  name: Type.String({ description: '标签名称' }),
  color: Type.String({ description: '标签颜色' }),
  createdBy: Type.String({ description: '创建者' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
})

// 发布标签关联 Schema
export const PublicationTagInfoSchema = Type.Object({
  id: Type.String({ description: '关联ID' }),
  publicationId: Type.String({ description: '发布ID' }),
  tagId: Type.String({ description: '标签ID' }),
  createdAt: Type.String({ description: '创建时间' }),
  tag: TagInfoSchema,
})

// KOL 信息 Schema
export const KolInfoSchema = Type.Object({
  id: Type.String({ description: 'KOL ID' }),
  platform: Type.String({ description: '平台' }),
  platformAccount: Type.String({ description: '平台账号' }),
  name: Type.Union([Type.String(), Type.Null()], { description: 'KOL 名称' }),
  avatar: Type.Union([Type.String(), Type.Null()], { description: 'KOL 头像' }),
  followers: Type.Union([Type.Number(), Type.Null()], { description: '粉丝数' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
})

// 发布数据项 Schema（用于标签发布数据）
export const TagPublicationItemSchema = Type.Object({
  id: Type.String({ description: '发布ID' }),
  spreadsheetId: Type.String({ description: '表格ID' }),
  videoId: Type.Union([Type.String(), Type.Null()], { description: '视频ID' }),
  postType: Type.Union([Type.String(), Type.Null()], { description: '帖子类型' }),
  authorUniqueId: Type.Union([Type.String(), Type.Null()], { description: '作者唯一ID' }),
  authorUserId: Type.Union([Type.String(), Type.Null()], { description: '作者用户ID' }),
  authorAvatar: Type.Union([Type.String(), Type.Null()], { description: '作者头像' }),
  region: Type.Union([Type.String(), Type.Null()], { description: '地区' }),
  nickName: Type.Union([Type.String(), Type.Null()], { description: '昵称' }),
  contactInformation: Type.Union([Type.String(), Type.Null()], { description: '联系信息' }),
  notes1: Type.Union([Type.String(), Type.Null()], { description: '备注1' }),
  notes2: Type.Union([Type.String(), Type.Null()], { description: '备注2' }),
  publishDate: Type.Union([Type.String(), Type.Null()], { description: '发布日期' }),
  influencer: Type.Union([Type.String(), Type.Null()], { description: '影响者' }),
  followers: Type.Union([Type.Number(), Type.Null()], { description: '粉丝数' }),
  postLink: Type.Union([Type.String(), Type.Null()], { description: '帖子链接' }),
  postThumbnailUrl: Type.Union([Type.String(), Type.Null()], { description: '帖子缩略图' }),
  views: Type.Union([Type.Number(), Type.Null()], { description: '观看数' }),
  likes: Type.Union([Type.Number(), Type.Null()], { description: '点赞数' }),
  comments: Type.Union([Type.Number(), Type.Null()], { description: '评论数' }),
  favorites: Type.Union([Type.Number(), Type.Null()], { description: '收藏数' }),
  shares: Type.Union([Type.Number(), Type.Null()], { description: '分享数' }),
  totalEngagement: Type.Union([Type.Number(), Type.Null()], { description: '总互动数' }),
  engagementRate: Type.Union([Type.Number(), Type.Null()], { description: '互动率' }),
  platform: Type.Union([Type.String(), Type.Null()], { description: '平台' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
  r2ImageKey: Type.String({ description: 'r2ImageKey' }),
  r2ImageUrl: Type.String({ description: 'r2ImageUrl' }),
  tags: Type.Array(PublicationTagInfoSchema, { description: '标签列表' }),
  kol: Type.Union([KolInfoSchema, Type.Null()], { description: 'KOL 信息' }),
})

// 获取标签发布数据响应 Schema
export const GetTagPublicationsResponseSchema = Type.Object({
  data: Type.Array(TagPublicationItemSchema, { description: '发布数据列表' }),
  total: Type.Number({ description: '总数量' }),
  page: Type.Number({ description: '当前页码' }),
  pageSize: Type.Number({ description: '每页数量' }),
  totalPages: Type.Number({ description: '总页数' }),
  hasMore: Type.Boolean({ description: '是否有更多数据' }),
})

export const getTagPostLinksSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String({ description: '标签ID' }),
  }),
  response: responseSchema,
}

export type GetTagPublicationsRequest = Static<typeof GetTagPublicationsRequestSchema>
export type GetTagPublicationsResponse = Static<typeof GetTagPublicationsResponseSchema>

export type TagPublicationItem = Static<typeof TagPublicationItemSchema>
export type TagInfo = Static<typeof TagInfoSchema>
export type PublicationTagInfo = Static<typeof PublicationTagInfoSchema>
export type KolInfo = Static<typeof KolInfoSchema>
