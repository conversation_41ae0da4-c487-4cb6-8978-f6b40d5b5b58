import { Static, Type } from '@sinclair/typebox'

// 获取企业详情请求参数 Schema
export const GetEnterpriseDetailParamsSchema = Type.Object({
  id: Type.String({ description: '企业ID' }),
})

// 企业成员用户信息 Schema (基于数据库 UserInfo 表结构)
export const EnterpriseMemberUserSchema = Type.Object({
  userId: Type.String({ description: '用户ID' }),
  email: Type.String({ description: '用户邮箱' }),
  createdAt: Type.String({ description: '用户创建时间' }),
  updatedAt: Type.String({ description: '用户更新时间' }),
})

// 企业成员信息 Schema (基于数据库 UserMembership 表结构)
export const EnterpriseMemberSchema = Type.Object({
  id: Type.String({ description: '会员ID' }),
  userId: Type.String({ description: '用户ID' }),
  type: Type.String({ description: '会员类型' }),
  timezone: Type.String({ description: '时区' }),
  effectiveAt: Type.String({ description: '会员生效时间' }),
  expireAt: Type.String({ description: '会员过期时间' }),
  accountQuota: Type.Number({ description: '账户总配额' }),
  usedQuota: Type.Number({ description: '已使用配额' }),
  dailyUsage: Type.Number({ description: '日使用量' }),
  status: Type.String({ description: '会员状态' }),
  lastResetAt: Type.String({ description: '上次重置时间' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
  enterpriseId: Type.Union([Type.String(), Type.Null()], { description: '所属企业ID' }),
  isEnterpriseAdmin: Type.Boolean({ description: '是否是企业管理员' }),
  enterpriseQuotaDailyLimit: Type.Number({ description: '企业个人每日配额限制' }),
  enableEnterpriseQuotaDailyLimit: Type.Boolean({ description: '是否开启企业配额每日限制' }),
  cardSubscriptionEffectiveAt: Type.Union([Type.String(), Type.Null()], {
    description: '信息卡包月开始时间',
  }),
  cardSubscriptionExpireAt: Type.Union([Type.String(), Type.Null()], {
    description: '信息卡包月结束时间',
  }),
  cardSubscriptionStatus: Type.String({ description: '信息卡包月状态' }),
  user: EnterpriseMemberUserSchema,
})

// 获取企业详情响应 Schema (基于数据库 Enterprise 表结构)
export const GetEnterpriseDetailResponseSchema = Type.Object({
  id: Type.String({ description: '企业ID' }),
  name: Type.String({ description: '企业名称' }),
  contactPerson: Type.Union([Type.String(), Type.Null()], { description: '联系人' }),
  contactPhone: Type.Union([Type.String(), Type.Null()], { description: '联系电话' }),
  contactEmail: Type.Union([Type.String(), Type.Null()], { description: '联系邮箱' }),
  address: Type.Union([Type.String(), Type.Null()], { description: '企业地址' }),
  industry: Type.Union([Type.String(), Type.Null()], { description: '所属行业' }),
  scale: Type.Union([Type.String(), Type.Null()], { description: '企业规模' }),
  accountQuota: Type.Number({ description: '企业总配额' }),
  usedQuota: Type.Number({ description: '已使用配额' }),
  dailyUsage: Type.Number({ description: '日使用量' }),
  dailyLimit: Type.Number({ description: '每日总上限' }),
  memberUsageDailyLimit: Type.Number({ description: '企业内单个成员的每日使用额度上限' }),
  allowInfoCardAccess: Type.Boolean({ description: '是否允许企业成员访问信息卡功能' }),
  description: Type.Union([Type.String(), Type.Null()], { description: '企业描述' }),
  status: Type.String({ description: '企业状态' }),
  effectiveAt: Type.String({ description: '生效时间' }),
  expireAt: Type.String({ description: '过期时间' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
  members: Type.Array(EnterpriseMemberSchema, { description: '企业成员列表' }),
})

export const BuyInfoCardForMemberSchema = Type.Object({
  enterpriseId: Type.String({ description: '企业ID' }),
  membershipId: Type.String({ description: '成员会员ID' }),
  months: Type.Number({
    description: '购买月数',
    minimum: 1,
    examples: [1, 3, 12],
  }),
})

export const UpdateEnterpriseBasicInfoSchema = Type.Object({
  name: Type.Optional(Type.String({ description: '企业名称' })),
  description: Type.Optional(Type.String({ description: '企业描述' })),
  contactPerson: Type.Optional(Type.String({ description: '联系人' })),
  contactPhone: Type.Optional(Type.String({ description: '联系电话' })),
  contactEmail: Type.Optional(Type.String({ description: '联系邮箱' })),
  industry: Type.Optional(Type.String({ description: '所属行业' })),
  scale: Type.Optional(Type.String({ description: '企业规模' })),
  address: Type.Optional(Type.String({ description: '企业地址' })),
  dailyLimit: Type.Optional(Type.Number({ description: '日额度', minimum: 0 })),
  memberUsageDailyLimit: Type.Optional(
    Type.Number({ description: '成员日额度，-1表示无限制', minimum: -1 }),
  ),
  allowInfoCardAccess: Type.Optional(
    Type.Boolean({ description: '是否允许企业成员访问信息卡功能' }),
  ),
})

export const InfoCardAccessStatusResponseSchema = Type.Object({
  allowAccess: Type.Boolean({ description: '是否允许访问信息卡功能' }),
  reason: Type.String({ description: '允许或拒绝的原因' }),
})

export type InfoCardAccessStatusResponse = Static<typeof InfoCardAccessStatusResponseSchema>

export type GetEnterpriseDetailParams = Static<typeof GetEnterpriseDetailParamsSchema>
export type GetEnterpriseDetailResponse = Static<typeof GetEnterpriseDetailResponseSchema>
export type BuyInfoCardForMemberRequest = Static<typeof BuyInfoCardForMemberSchema>
export type UpdateEnterpriseBasicInfoRequest = Static<typeof UpdateEnterpriseBasicInfoSchema>
