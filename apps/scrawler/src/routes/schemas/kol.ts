import { KolPlatform } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'
import { FastifySchema } from 'fastify'
import { responseSchema } from './common'

export const getKolPlatformInfoSchema: FastifySchema = {
  querystring: Type.Object(
    {
      handler: Type.Optional(Type.String({ description: 'KOL 的平台账号' })),
      id: Type.Optional(Type.String({ description: 'KOL 的 ID' })),
      platform: Type.Union(
        Object.values(KolPlatform)
          .filter((v): v is KolPlatform => typeof v === 'string')
          .map((platform) => Type.Literal(platform)),
        { description: '平台类型' },
      ),
    },
    {
      additionalProperties: false,
      anyOf: [Type.Object({ handler: Type.String() }), Type.Object({ id: Type.String() })],
    },
  ),
  response: responseSchema,
}

// Export request shema
export const ExportRequestSchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  isWeb: Type.Optional(Type.Boolean({ description: '是否为Web版本' })),
  isStop: Type.Optional(Type.Boolean({ description: '是否将停止任务' })),
})

export type ExportRequest = Static<typeof ExportRequestSchema>

export const GetKolIdRequestSchema = Type.Object({
  platform: Type.Union(
    Object.values(KolPlatform)
      .filter((v): v is KolPlatform => typeof v === 'string')
      .map((platform) => Type.Literal(platform)),
    { description: '平台类型' },
  ),
  platformAccount: Type.String({ description: '平台账号' }),
})

export type GetKolIdRequest = Static<typeof GetKolIdRequestSchema>

export const GetKolIdResponseSchema = Type.Object({
  kolId: Type.String({ description: 'KOL ID' }),
  isNew: Type.Boolean({ description: '是否为新空白KOL' }),
})

export type GetKolIdResponse = Static<typeof GetKolIdResponseSchema>
