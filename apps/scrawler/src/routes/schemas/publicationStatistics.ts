import { Static, Type } from '@sinclair/typebox'

// 用户信息 Schema
export const UserInfoSchema = Type.Object({
  userId: Type.String({ description: '用户ID' }),
  email: Type.String({ description: '用户邮箱' }),
})

// 标签信息 Schema
export const TagSchema = Type.Object({
  id: Type.String({ description: '标签ID' }),
  name: Type.String({ description: '标签名称' }),
  color: Type.String({ description: '标签颜色' }),
  createdBy: Type.String({ description: '创建者' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
})

// 投放数据项 Schema (基于 PublicationStatisticsSheetData 表结构)
export const PublicationStatisticsItemSchema = Type.Object({
  id: Type.String({ description: '数据ID' }),
  spreadsheetId: Type.String({ description: '表格ID' }),
  videoId: Type.Union([Type.String(), Type.Null()], { description: '视频ID' }),
  postType: Type.Union([Type.String(), Type.Null()], { description: '帖子类型' }),
  authorUniqueId: Type.Union([Type.String(), Type.Null()], { description: '作者唯一ID' }),
  authorUserId: Type.Union([Type.String(), Type.Null()], { description: '作者用户ID' }),
  authorAvatar: Type.Union([Type.String(), Type.Null()], { description: '作者头像' }),
  region: Type.Union([Type.String(), Type.Null()], { description: '地区' }),
  nickName: Type.Union([Type.String(), Type.Null()], { description: '昵称' }),
  contactInformation: Type.Union([Type.String(), Type.Null()], { description: '联系信息' }),
  notes1: Type.Union([Type.String(), Type.Null()], { description: '备注1' }),
  notes2: Type.Union([Type.String(), Type.Null()], { description: '备注2' }),
  publishDate: Type.Union([Type.String(), Type.Null()], { description: '发布日期' }),
  influencer: Type.Union([Type.String(), Type.Null()], { description: '影响者' }),
  followers: Type.Union([Type.Number(), Type.Null()], { description: '粉丝数' }),
  postLink: Type.Union([Type.String(), Type.Null()], { description: '帖子链接' }),
  postThumbnailUrl: Type.Union([Type.String(), Type.Null()], { description: '帖子缩略图' }),
  views: Type.Union([Type.Number(), Type.Null()], { description: '观看数' }),
  likes: Type.Union([Type.Number(), Type.Null()], { description: '点赞数' }),
  comments: Type.Union([Type.Number(), Type.Null()], { description: '评论数' }),
  favorites: Type.Union([Type.Number(), Type.Null()], { description: '收藏数' }),
  shares: Type.Union([Type.Number(), Type.Null()], { description: '分享数' }),
  totalEngagement: Type.Union([Type.Number(), Type.Null()], { description: '总互动数' }),
  engagementRate: Type.Union([Type.Number(), Type.Null()], { description: '互动率' }),
  platform: Type.Union([Type.String(), Type.Null()], { description: '平台' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
  tags: Type.Array(TagSchema, { description: '标签列表' }),
})

export const AudienceSchema = Type.Object({
  userId: Type.Optional(
    Type.String({
      description: '要查询的用户ID，不传则查询当前用户',
      examples: ['user_123'],
    }),
  ),
  publicationId: Type.String({
    description: '发布统计数据ID',
    minLength: 1,
  }),
})

export type AudienceRequest = Static<typeof AudienceSchema>

export const PluginAudienceSchema = Type.Object({
  url: Type.String({
    description: '视频链接',
    minLength: 1,
  }),
})

export type PluginAudienceRequest = Static<typeof PluginAudienceSchema>

export const PluginAudienceResultParamsSchema = Type.Object({
  taskId: Type.String({
    description: '任务ID',
    minLength: 1,
  }),
})

export type PluginAudienceResultParams = Static<typeof PluginAudienceResultParamsSchema>

export const TrackByPublicationIdsSchema = Type.Object({
  userId: Type.Optional(
    Type.String({
      description: '要查询的用户ID，不传则查询当前用户',
      examples: ['user_123'],
    }),
  ),
  publicationIds: Type.Array(
    Type.String({
      description: '发布统计数据ID',
      minLength: 1,
    }),
    {
      description: '发布统计数据ID数组',
      minItems: 1,
      maxItems: 200,
    },
  ),
})

export type TrackByPublicationIdsRequest = Static<typeof TrackByPublicationIdsSchema>

export const BatchDeletePublicationsSchema = Type.Object({
  publicationIds: Type.Array(
    Type.String({
      description: '发布统计数据ID',
      minLength: 1,
    }),
    {
      description: '要删除的发布统计数据ID数组',
      minItems: 1,
      maxItems: 200,
    },
  ),
})

// 删除单个投放数据路径参数 Schema
export const DeletePublicationParamsSchema = Type.Object({
  publicationId: Type.String({
    description: '要删除的发布统计数据ID',
    minLength: 1,
  }),
})

// 投放数据路径参数 Schema (通用)
export const PublicationParamsSchema = Type.Object({
  publicationId: Type.String({
    description: '发布统计数据ID',
    minLength: 1,
  }),
})

// 更新投放数据请求体 Schema
export const UpdatePublicationDataBodySchema = Type.Object({
  totalCost: Type.Optional(
    Type.Number({
      description: '总成本',
      minimum: 0,
    }),
  ),
  notes1: Type.Optional(
    Type.String({
      description: '备注1',
      maxLength: 1000,
    }),
  ),
  notes2: Type.Optional(
    Type.String({
      description: '备注2',
      maxLength: 1000,
    }),
  ),
  contactInformation: Type.Optional(
    Type.String({
      description: '联系信息',
      maxLength: 500,
    }),
  ),
})

// 追踪投放数据查询参数 Schema
export const TrackPublicationQuerySchema = Type.Object({
  userId: Type.Optional(
    Type.String({
      description: '要追踪的用户ID，不传则追踪当前用户数据',
    }),
  ),
})

export const TrackPublicationResponseSchema = Type.Object({
  id: Type.String({ description: '数据ID' }),
  spreadsheetId: Type.String({ description: '表格ID' }),
  videoId: Type.Union([Type.String(), Type.Null()], { description: '视频ID' }),
  postType: Type.Union([Type.String(), Type.Null()], { description: '帖子类型' }),
  authorUniqueId: Type.Union([Type.String(), Type.Null()], { description: '作者唯一ID' }),
  authorUserId: Type.Union([Type.String(), Type.Null()], { description: '作者用户ID' }),
  authorAvatar: Type.Union([Type.String(), Type.Null()], { description: '作者头像' }),
  region: Type.Union([Type.String(), Type.Null()], { description: '地区' }),
  nickName: Type.Union([Type.String(), Type.Null()], { description: '昵称' }),
  contactInformation: Type.Union([Type.String(), Type.Null()], { description: '联系信息' }),
  notes1: Type.Union([Type.String(), Type.Null()], { description: '备注1' }),
  notes2: Type.Union([Type.String(), Type.Null()], { description: '备注2' }),
  publishDate: Type.Union([Type.String({ format: 'date-time' }), Type.Null()], {
    description: '发布日期',
  }),
  influencer: Type.Union([Type.String(), Type.Null()], { description: '影响者' }),
  followers: Type.Union([Type.Number(), Type.Null()], { description: '粉丝数' }),
  postLink: Type.Union([Type.String(), Type.Null()], { description: '帖子链接' }),
  postThumbnailUrl: Type.Union([Type.String(), Type.Null()], { description: '帖子缩略图' }),
  views: Type.Union([Type.Number(), Type.Null()], { description: '观看数' }),
  likes: Type.Union([Type.Number(), Type.Null()], { description: '点赞数' }),
  comments: Type.Union([Type.Number(), Type.Null()], { description: '评论数' }),
  favorites: Type.Union([Type.Number(), Type.Null()], { description: '收藏数' }),
  shares: Type.Union([Type.Number(), Type.Null()], { description: '分享数' }),
  totalEngagement: Type.Union([Type.Number(), Type.Null()], { description: '总互动数' }),
  engagementRate: Type.Union([Type.Number(), Type.Null()], { description: '互动率' }),
  totalCost: Type.Union([Type.Number(), Type.Null()], { description: '总成本' }),
  cpm: Type.Union([Type.Number(), Type.Null()], { description: 'CPM' }),
  countryData: Type.Union([Type.Any(), Type.Null()], { description: '国家数据' }),
  kolId: Type.Union([Type.String(), Type.Null()], { description: 'KOL ID' }),
  platform: Type.Union(
    [
      Type.Enum({
        YOUTUBE: 'YOUTUBE',
        TIKTOK: 'TIKTOK',
        INSTAGRAM: 'INSTAGRAM',
        DOUYIN: 'DOUYIN',
        XHS: 'XHS',
        TWITTER: 'TWITTER',
      }),
      Type.Null(),
    ],
    { description: '平台' },
  ),
  easykolTrackStrategyId: Type.Union([Type.String(), Type.Null()], { description: '追踪策略ID' }),
  createdAt: Type.String({ format: 'date-time', description: '创建时间' }),
  updatedAt: Type.String({ format: 'date-time', description: '更新时间' }),
})

// 获取统计数据请求参数 Schema
export const GetPublicationSummaryQuerySchema = Type.Object({
  userId: Type.Optional(Type.String({ description: '要查询的用户ID，不传则查询当前用户' })),
  startDate: Type.Optional(
    Type.String({
      description: '开始日期，格式：YYYY-MM-DD，不传则查询所有数据',
      pattern: '^\\d{4}-\\d{2}-\\d{2}$',
      examples: ['2025-08-01'],
    }),
  ),
  endDate: Type.Optional(
    Type.String({
      description: '结束日期，格式：YYYY-MM-DD，不传则查询所有数据',
      pattern: '^\\d{4}-\\d{2}-\\d{2}$',
      examples: ['2025-09-30'],
    }),
  ),
})

// 统计数据响应 Schema
export const GetPublicationSummaryResponseSchema = Type.Object({
  totalVideos: Type.Number({ description: '视频总数' }),
  totalViews: Type.Number({ description: '播放总数' }),
  cost: Type.Number({ description: '总成本' }),
  cpm: Type.Number({ description: 'CPM（千次展示成本）' }),
  videosWithCost: Type.Number({ description: '存在成本的视频数' }),
  viewsWithCost: Type.Number({ description: '存在成本的视频的总播放量' }),
  dateRange: Type.Object({
    startDate: Type.Union([Type.String(), Type.Null()], {
      description: '开始日期，null表示查询所有数据',
    }),
    endDate: Type.Union([Type.String(), Type.Null()], {
      description: '结束日期，null表示查询所有数据',
    }),
  }),
})

export type BatchDeletePublicationsRequest = Static<typeof BatchDeletePublicationsSchema>
export type DeletePublicationParams = Static<typeof DeletePublicationParamsSchema>
export type PublicationParams = Static<typeof PublicationParamsSchema>
export type UpdatePublicationDataBody = Static<typeof UpdatePublicationDataBodySchema>
export type TrackPublicationQuery = Static<typeof TrackPublicationQuerySchema>
export type TrackPublicationResponse = Static<typeof TrackPublicationResponseSchema>

export type PublicationStatisticsItem = Static<typeof PublicationStatisticsItemSchema>
export type UserInfo = Static<typeof UserInfoSchema>
export type Tag = Static<typeof TagSchema>

// 追踪最近数据请求体 Schema
export const TrackRecentDataRequestSchema = Type.Object({
  userId: Type.Optional(
    Type.String({
      description: '要查询的用户ID，不传则查询当前用户',
      examples: ['user_123'],
    }),
  ),
  page: Type.Optional(
    Type.Integer({
      minimum: 1,
      description: '页码，默认为1',
      default: 1,
      examples: [1, 2, 3],
    }),
  ),
  pageSize: Type.Optional(
    Type.Integer({
      minimum: 1,
      maximum: 100,
      description: '每页数量，默认为20，最大100',
      default: 20,
      examples: [10, 20, 50],
    }),
  ),
})

// 追踪最近数据响应 Schema
export const TrackRecentDataResponseSchema = Type.Object({
  id: Type.String({ description: '任务ID' }),
  projectId: Type.String({ description: '项目ID' }),
  status: Type.String({ description: '任务状态' }),
  params: Type.Object({
    spreadsheetId: Type.String({ description: 'Google Sheet ID' }),
    tiktok: Type.Object({
      videosIds: Type.Array(Type.String()),
      urls: Type.Array(Type.String()),
    }),
    youtube: Type.Object({
      videosIds: Type.Array(Type.String()),
      shortsIds: Type.Array(Type.String()),
      urls: Type.Array(Type.String()),
    }),
    instagram: Type.Object({
      reelsIds: Type.Array(Type.String()),
      postsIds: Type.Array(Type.String()),
      urls: Type.Array(Type.String()),
    }),
    douyin: Type.Object({
      videosIds: Type.Array(Type.String()),
      noteIds: Type.Array(Type.String()),
      urls: Type.Array(Type.String()),
    }),
    xhs: Type.Object({
      noteIds: Type.Array(Type.String()),
      urls: Type.Array(Type.String()),
    }),
  }),
  result: Type.Union([Type.Any(), Type.Null()], { description: '任务结果' }),
  errors: Type.Union([Type.Any(), Type.Null()], { description: '错误信息' }),
  meta: Type.Union([Type.Any(), Type.Null()], { description: '元数据' }),
  createdBy: Type.String({ description: '创建者ID' }),
  createdAt: Type.String({ description: '创建时间' }),
  updatedAt: Type.String({ description: '更新时间' }),
  isTerminated: Type.Boolean({ description: '是否已终止' }),
  reason: Type.String({ description: '任务原因' }),
  type: Type.String({ description: '任务类型' }),
  strategyId: Type.Union([Type.String(), Type.Null()], { description: '策略ID' }),
  candidate: Type.Object({}, { description: '候选数据', additionalProperties: true }),
  isCompleted: Type.Boolean({ description: '是否已完成' }),
  isFailed: Type.Boolean({ description: '是否失败' }),
  isYoutube: Type.Boolean({ description: '是否为YouTube任务' }),
  isTiktok: Type.Boolean({ description: '是否为TikTok任务' }),
  isTwitter: Type.Boolean({ description: '是否为Twitter任务' }),
  isInstagram: Type.Boolean({ description: '是否为Instagram任务' }),
  isPaginationTask: Type.Boolean({ description: '是否为分页任务' }),
  isSingleTask: Type.Boolean({ description: '是否为单个任务' }),
})

export type GetPublicationSummaryQuery = Static<typeof GetPublicationSummaryQuerySchema>
export type GetPublicationSummaryResponse = Static<typeof GetPublicationSummaryResponseSchema>
export type TrackRecentDataRequest = Static<typeof TrackRecentDataRequestSchema>
export type TrackRecentDataResponse = Static<typeof TrackRecentDataResponseSchema>
