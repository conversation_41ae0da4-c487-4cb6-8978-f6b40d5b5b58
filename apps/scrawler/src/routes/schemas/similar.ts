import { InsMode, TtMode, TwitterMode, YtbMode } from '@/enums/TaskMode'
import { KolPlatform, TaskReason } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'
import { FastifySchema } from 'fastify'
import { responseSchema } from './common'

export const analysisUserVeticalSchema: FastifySchema = {
  querystring: Type.Intersect([
    Type.Object({
      platform: Type.Union(
        Object.values(KolPlatform)
          .filter((v): v is KolPlatform => typeof v === 'string')
          .map((platform) => Type.Literal(platform)),
        { description: '平台类型' },
      ),
      language: Type.Optional(
        Type.String({
          description: '语言',
          default: 'zh-CN',
        }),
      ),
    }),
    Type.Union([
      Type.Object({
        handler: Type.String({
          description: 'KOL 的平台账号',
          minLength: 1,
        }),
        id: Type.Optional(Type.String({ description: 'KOL 的 ID' })),
      }),
      Type.Object({
        handler: Type.Optional(Type.String({ description: 'KOL 的平台账号' })),
        id: Type.String({
          description: 'KOL 的 ID',
          minLength: 1,
        }),
      }),
    ]),
  ]),
  response: responseSchema,
}

export const SimilarTaskQueryRequestSchema = Type.Object({
  taskId: Type.String({ description: '任务 ID' }),
})

export type SimilarTaskQueryRequest = Static<typeof SimilarTaskQueryRequestSchema>

export const SimilarTaskRequestSchema = Type.Object({
  projectId: Type.String({ description: '项目 ID' }),
  source: Type.String({ description: '来源' }),
  platform: Type.String({ description: '平台' }),
  reason: Type.Union([Type.Literal(TaskReason.SEARCH), Type.Literal(TaskReason.SUPERLIKE)], {
    description: '任务原因',
  }),

  // 一些硬性条件设置
  regions: Type.Optional(Type.Array(Type.String({ description: '地区列表' }))),
  minSubscribers: Type.Optional(Type.Number({ description: '最小订阅者' })),
  maxSubscribers: Type.Optional(Type.Number({ description: '最大订阅者' })),
  lastPublishedDays: Type.Optional(Type.Number({ description: '最近发布天数' })),
  videosAverageViews: Type.Optional(Type.Number({ description: '视频平均浏览量' })),
  maxVideosAverageViews: Type.Optional(Type.Number({ description: '最大视频平均浏览量' })),
  minAverageLikeCount: Type.Optional(Type.Number({ description: '最小平均点赞数' })),
  maxAverageLikeCount: Type.Optional(Type.Number({ description: '最大平均点赞数' })),

  // 三要素
  banList: Type.Optional(Type.Array(Type.String({ description: '禁止列表' }))),
  allowList: Type.Optional(Type.Array(Type.String({ description: '允许列表' }))),
  kolDescription: Type.Optional(Type.String({ description: 'KOL 描述' })),

  taskRound: Type.Optional(
    Type.Union(
      [
        Type.Number({ description: 'INS 设定的任务轮次（数字类型）' }),
        Type.String({ description: 'INS 设定的任务轮次（字符串类型）' }),
      ],
      { description: 'INS 设定的任务轮次' },
    ),
  ),

  // tiktok mode
  ttMode: Type.Optional(Type.Enum(TtMode, { description: 'TT 模式' })),
  ttModeReason: Type.Optional(Type.String({ description: 'TT 模式原因' })),

  // youtube mode
  ytbMode: Type.Optional(Type.Enum(YtbMode, { description: 'YTB 模式' })),
  ytbVideoIds: Type.Optional(
    Type.Array(Type.String({ description: 'YTB 选中用来 similar 的博主视频 ID' })),
  ),
  // ins mode
  insMode: Type.Optional(Type.Enum(InsMode, { description: 'INS 模式' })),
  // twitter mode
  twitterMode: Type.Optional(Type.Enum(TwitterMode, { description: 'Twitter 模式' })),
  twitterUserNames: Type.Optional(
    Type.Array(Type.String({ description: 'Twitter 选中用来 similar 的博主用户名' })),
  ),
})

export type SimilarTaskRequest = Static<typeof SimilarTaskRequestSchema>

export const SingleSearchRequestSchema = Type.Object({
  taskId: Type.String({ description: '任务 ID' }),
  hasPost: Type.Optional(Type.Boolean({ description: '是否包含帖子数据', default: false })),
})
export type SingleSearchRequest = Static<typeof SingleSearchRequestSchema>

export const UnionSearchRequestSchema = Type.Object({
  projectId: Type.String({ description: '项目 ID' }),
  hasPost: Type.Optional(Type.Boolean({ description: '是否包含帖子数据', default: false })),
})
export type UnionSearchRequest = Static<typeof UnionSearchRequestSchema>
