import { handleUnknownError } from '@/common/errorHandler'
import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import { errorResponse, serverErrorResponse, successResponse } from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import Sentry from '@/infras/sentry'
import { needSupabaseAuth, withAuth } from '@/middlewares/auth'
import { versionStatistic } from '@/middlewares/versionStatistic'
import {
  createProjectWithUser,
  deleteProject,
  findProjectById,
  getAllAvailableProjectByUser,
  hasAuthForProject,
  resetProject,
  terminateProjectByTaskType,
} from '@/services/project'
import { ProjectConfig } from '@/types/project'
import { prisma } from '@repo/database'
import { Type } from '@sinclair/typebox'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import { TerminateByTaskTypeBody, TerminateByTaskTypeBodySchema } from '../schemas/project'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post(
    '/',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { title, description } = request.body as any
        if (!title) {
          return reply.status(400).send({ error: 'title is required' })
        }
        const user = (request as any).user
        const project = await createProjectWithUser(user.id, {
          title,
          description,
        })
        return reply.status(201).send(project)
      } catch (error) {
        return reply.status(500).send({ error: 'An error occurred while creating the project.' })
      }
    },
  )

  fastify.get(
    '/:id',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { id } = request.params as any
        const { user } = request as any

        const project = await findProjectById(id)

        if (!project) {
          return reply.status(404).send({ error: 'Project not found or deleted' })
        }

        if (!(await hasAuthForProject(user.id, id))) {
          return reply.status(403).send({ error: 'Forbidden' })
        }

        return reply.send(project)
      } catch (error) {
        return reply.status(500).send({ error: 'An error occurred while retrieving the project.' })
      }
    },
  )

  fastify.get('/', withAuth(versionStatistic), async (request, reply) => {
    try {
      const { user } = request as any
      const projects = await getAllAvailableProjectByUser(user.id)
      return reply.send({
        success: true,
        data: projects,
      })
    } catch (error) {
      return reply.status(500).send({ error: 'An error occurred while retrieving the projects.' })
    }
  })
  // 根据任务类型终止任务
  fastify.post<{ Body: TerminateByTaskTypeBody }>(
    '/terminateByTaskType',
    {
      schema: {
        tags: ['project'],
        summary: '根据任务类型终止任务',
        description: '终止指定项目中指定类型的任务并清理相关数据',
        body: TerminateByTaskTypeBodySchema,
        response: FullResponseSchema(Type.String()),
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Body: TerminateByTaskTypeBody }>, reply) => {
      const { projectId, taskType } = request.body
      const user = (request as any).user
      if (!(await hasAuthForProject(user.id, projectId))) {
        throwError(StatusCodes.FORBIDDEN, 'No permission to access this project')
      }
      await terminateProjectByTaskType(projectId, taskType)
      return reply.status(200).send(successResponse('Task terminated successfully'))
    },
  )

  fastify.delete('/:projectId', { preHandler: needSupabaseAuth }, async (request, reply) => {
    try {
      const { projectId } = request.params as any
      const { user } = request as any
      await deleteProject(projectId, user.id)
      return reply.status(200).send(successResponse('delete project success'))
    } catch (error) {
      return reply.status(500).send(error)
    }
  })
  fastify.post('/reset', { preHandler: needSupabaseAuth }, async (request, reply) => {
    const { projectId } = request.body as any
    const { user } = request as any
    if (!projectId) {
      return reply.status(400).send({ error: 'projectId is required' })
    }
    if (!(await hasAuthForProject(user.id, projectId))) {
      return reply.status(403).send({ error: 'Forbidden' })
    }
    try {
      await resetProject(projectId)
      return reply.status(200).send(successResponse('reset project success'))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.patch('/:projectId', { preHandler: needSupabaseAuth }, async (request, reply) => {
    try {
      const { projectId } = request.params as any
      const config = request.body as ProjectConfig
      if (!projectId) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, '', ''))
      }
      const project = await prisma.project.findUnique({
        where: {
          id: projectId,
          deletedAt: null,
        },
      })
      if (!project) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, '', 'Project not found'))
      }
      const pureConfig = {
        allowList: config.allowList ?? undefined,
        banList: config.banList ?? undefined,
        kolDescription: config.kolDescription ?? undefined,
      } as ProjectConfig
      await prisma.project.update({
        where: {
          id: projectId,
        },
        data: {
          config: pureConfig,
        },
      })
      return reply.status(200).send(successResponse('success'))
    } catch (err) {
      Sentry.captureException(err)
      console.error(err)
      return reply.status(500).send(errorResponse)
    }
  })
}

export default router
