// apps/scrawler/src/routes/api/label.ts
import {
  StatusCodes,
  errorResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import { LabelColor } from '@/enums/LabelColor'
import { withAuth } from '@/middlewares/auth'
import { TagListQuery, TagListQuerySchema, TagListResponseSchema } from '@/routes/schemas/tags'
import { EnterpriseService } from '@/services/enterprise.service'
import { tagService } from '@/services/tag.service'
import { CreateTagParams } from '@/types/tag'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get<{
    Querystring: TagListQuery
  }>(
    '/list',
    {
      schema: {
        tags: ['tags'],
        summary: '获取标签列表',
        description:
          '获取用户的所有标签列表。支持查询企业内其他成员的标签数据，不传userId则查询当前用户数据',
        querystring: TagListQuerySchema,
        response: FullResponseSchema(TagListResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any
      const { userId: targetUserId } = request.query

      const currentUserId = user.id

      if (targetUserId && targetUserId !== currentUserId) {
        await EnterpriseService.getInstance().validateEnterprisePermission(
          currentUserId,
          targetUserId,
        )
      }

      const effectiveUserId = targetUserId || currentUserId

      const labels = await tagService.getTags(effectiveUserId)
      return reply.send(successResponse(labels))
    },
  )

  fastify.post('/', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { name, color } = request.body as CreateTagParams

      // 验证颜色是否在枚举中
      if (!Object.keys(LabelColor).includes(color)) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, 'Invalid tag color'))
      }

      const tag = await tagService.createTag(user.id, { name, color })
      return reply.send(successResponse(tag))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Internal server error'

      if (
        errorMessage.includes('Tag limit exceeded') ||
        errorMessage.includes('Tag name too long') ||
        errorMessage.includes('Tag name already exists')
      ) {
        return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, errorMessage))
      }

      return reply.status(500).send(serverErrorResponse(errorMessage))
    }
  })

  fastify.delete('/:id', withAuth(), async (request, reply) => {
    try {
      const { user } = request as any
      const { id } = request.params as { id: string }
      if (!id) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'Tag ID cannot be empty'))
      }
      await tagService.deleteTag(user.id, id)
      return reply.send(successResponse(null, 'Tag deleted successfully'))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Internal server error'

      if (errorMessage.includes('Tag not found or access denied')) {
        return reply.status(403).send(errorResponse(StatusCodes.FORBIDDEN, errorMessage))
      }

      return reply.status(500).send(serverErrorResponse(errorMessage))
    }
  })
}

export default router
