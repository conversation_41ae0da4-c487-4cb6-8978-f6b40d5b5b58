import YoutubeApi from '@/api/youtube'
import { handleUnknownError } from '@/common/errorHandler'
import {
  errorResponse,
  serverErrorResponse,
  StatusCodes,
  successResponse,
} from '@/common/response/response'
import { withAuth } from '@/middlewares/auth'
import { dynamicCheckQuota } from '@/middlewares/quota'
import {
  YoutubeHashTagBreakRequest,
  YoutubeHashTagBreakRequestSchema,
  YoutubeInputSearchBreakRequest,
  YoutubeInputSearchBreakRequestSchema,
} from '@/routes/schemas/ytb'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { getProjectCandidatesWithPostsByTaskType } from '@/services/project'
import TaskService from '@/services/task'
import { getYoutubeijsService } from '@/services/youtubeijs.service'
import { SearchHashtagProjectCandidateMeta, SearchInputProjectCandidateMeta } from '@/types/project'
import { YoutubeHashTagBreakTaskParams, YoutubeSearchInputBreakTaskParams } from '@/types/task'
import { getUser } from '@/utils/auth'
import { KolPlatform, prisma, QuotaType, TaskReason, TaskType } from '@repo/database'
import assert, { AssertionError } from 'assert'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import { PaginateReqSchema } from '../schemas/common'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get('/pageType', async (request, reply) => {
    const { url } = request.query as { url: string }
    const service = await getYoutubeijsService()
    const info = await service.resolveURL(url)
    return reply.status(200).send(successResponse(info))
  })

  fastify.post<{ Body: YoutubeHashTagBreakRequest }>(
    '/hashTag',
    {
      schema: {
        tags: ['youtube'],
        summary: '创建 hashtag 任务',
        description: '创建 hashtag 任务',
        body: YoutubeHashTagBreakRequestSchema,
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculateHashTagSearchQuota,
          QuotaType.YOUTUBE_HASHTAG_BREAK,
        ),
      ),
    },
    async (req, reply) => {
      const { projectId, reason, currentVideoCount = 0, maxVideoCount = 100 } = req.body
      let { tag } = req.body
      tag = tag.replace('#', '')
      tag = tag.trim()
      tag = tag.replace(/ /g, '')
      tag = tag.toLowerCase()
      const user = getUser(req)

      assert(projectId, 'projectId is required!')
      assert(tag && tag.trim() !== '', 'tag is required and cannot be empty!')

      const project = await prisma.project.findFirst({
        where: { id: projectId, deletedAt: null },
      })
      assert(project, 'Project not found or has been deleted!')

      const hashTagInfo = await YoutubeApi.getInstance().getHashtag(tag)
      assert(hashTagInfo && hashTagInfo.meta.videoCount, `Tag '${tag}' not found!`)

      const total = hashTagInfo.meta.videoCount

      let paginationToken: string = ''
      if (reason === TaskReason.NEXT_PAGE) {
        const projectCandidate = await prisma.projectCandidate.findUnique({
          where: {
            projectId_type: {
              projectId,
              type: TaskType.HASH_TAG_BREAK,
            },
          },
        })
        const meta = (projectCandidate?.meta as unknown as SearchHashtagProjectCandidateMeta) || {}
        paginationToken = meta.paginationToken || ''
      }

      const params: YoutubeHashTagBreakTaskParams = {
        projectId,
        platform: KolPlatform.YOUTUBE,
        tag,
        paginationToken,
        currentVideoCount,
        maxVideoCount,
        total: total,
      }
      const task = await TaskService.getInstance().createTask(
        projectId,
        params,
        user.id,
        reason || TaskReason.SEARCH,
        TaskType.HASH_TAG_BREAK,
      )
      return reply.status(200).send(successResponse(task))
    },
  )

  fastify.get(
    '/hashtag',
    {
      schema: {
        summary: '获取 hashtag 任务结果',
        tags: ['youtube'],
        querystring: PaginateReqSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest, reply) => {
      const { projectId, page, pageSize } = request.query as any
      assert(projectId, 'projectId is required!')
      assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')
      try {
        const result = await getProjectCandidatesWithPostsByTaskType(
          projectId,
          TaskType.HASH_TAG_BREAK,
          KolPlatform.YOUTUBE,
          {
            page,
            pageSize,
          },
        )
        return reply.status(200).send(successResponse(result))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.post<{ Body: YoutubeInputSearchBreakRequest }>(
    '/searchInput',
    {
      schema: {
        tags: ['youtube'],
        summary: '创建 input search 任务',
        body: YoutubeInputSearchBreakRequestSchema,
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculateInputSearchQuota,
          QuotaType.YOUTUBE_SEARCH_INPUT_BREAK,
        ),
      ),
    },
    async (req, reply) => {
      try {
        const {
          projectId,
          searchInput,
          reason,
          currentVideoCount = 0,
          maxVideoCount = 100,
        } = req.body
        let { geo, lang, duration = 'long', sort_by, upload_date } = req.body
        const user = (req as any).user

        assert(projectId, 'projectId is required!')
        assert(searchInput && searchInput.trim() !== '', 'keyword is required and cannot be empty!')

        const project = await prisma.project.findUnique({
          where: { id: projectId, deletedAt: null },
        })
        assert(project, 'Project not found or has been deleted!')

        const searchInfo = await YoutubeApi.getInstance().searchVideo(searchInput, {
          geo,
          lang,
          duration,
          sort_by,
          upload_date,
        })
        assert(searchInfo && searchInfo.count, `search keyword '${searchInfo}' not found!`)

        let total = searchInfo.count

        let paginationToken: string = ''
        if (reason === TaskReason.NEXT_PAGE) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.SEARCH_INPUT_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as unknown as SearchInputProjectCandidateMeta) || {}
          paginationToken = meta.token || ''
          total = meta.total || total
          geo = meta.geo || geo
          lang = meta.lang || lang
          duration = meta.duration || duration
          sort_by = meta.sort_by || sort_by
          upload_date = meta.upload_date || upload_date
        }

        const params: YoutubeSearchInputBreakTaskParams = {
          projectId,
          platform: KolPlatform.YOUTUBE,
          searchInput,
          paginationToken,
          currentVideoCount,
          maxVideoCount,
          total: total,
          geo,
          lang,
          duration,
          sort_by,
          upload_date,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.SEARCH_INPUT_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.get(
    '/searchInput',
    {
      schema: {
        summary: '获取 input 任务结果',
        tags: ['youtube'],
        querystring: PaginateReqSchema,
      },
      ...withAuth(),
    },
    async (request: FastifyRequest, reply) => {
      const { projectId, page, pageSize } = request.query as any
      assert(projectId, 'projectId is required!')
      assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')
      try {
        const result = await getProjectCandidatesWithPostsByTaskType(
          projectId,
          TaskType.SEARCH_INPUT_BREAK,
          KolPlatform.YOUTUBE,
          {
            page,
            pageSize,
          },
        )
        return reply.status(200).send(successResponse(result))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )
}

export default router
