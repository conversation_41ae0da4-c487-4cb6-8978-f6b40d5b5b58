import { uploadFileByStream } from '@/api/aliyun'
import Tik<PERSON><PERSON><PERSON> from '@/api/tiktok'
import YoutubeApi from '@/api/youtube'
import { handleUnknownError } from '@/common/errorHandler.ts'
import { throwError } from '@/common/errors/statusCodes'
import { serverErrorResponse, StatusCodes, successResponse } from '@/common/response/response.ts'
import {
  HOST,
  INS_MODE_DEFAULT,
  TT_MODE_DEFAULT,
  TWITTER_MODE_DEFAULT,
  YTB_MODE_DEFAULT,
} from '@/config/env'
import { QuotaCost } from '@/enums/QuotaCost'
import Sentry from '@/infras/sentry'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { twitter241Api } from '@/lib/twitter241Api'
import { needSupabaseAuth, withAuth } from '@/middlewares/auth'
import { checkQuota, dynamicCheckQuota, handleSearchQuota } from '@/middlewares/quota'
import { sameTaskLimit } from '@/middlewares/sameTaskLimit'
import { superlikeLimit } from '@/middlewares/superlikeLimit'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { getProjectCandidates, getProjectCandidatesWithPostsByTaskType } from '@/services/project'
import {
  analysisUserKolDescription,
  findLatestByTaskType,
  findTasksByTaskType,
} from '@/services/similar.ts'
import TaskService from '@/services/task.ts'
import { KolInfoWithScore } from '@/types/kol'
import { ERROR_MESSAGES } from '@/types/memberShip'
import { ProjectConfig } from '@/types/project'
import { createTaskRequest, SimilarTaskRequest } from '@/types/request/similar.request'
import { SINGL_TASK_TYPE, SINGL_TASK_TYPE_ENUM } from '@/types/task'
import Logger from '@/utils/logger'
import { retryUtil } from '@/utils/retry'
import { Transform as ParseTransform } from '@json2csv/node'
import {
  KolPlatform,
  prisma,
  ProjectKolAttitude,
  QuotaType,
  SimilarChannelTaskStatus,
  TaskReason,
  TaskType,
} from '@repo/database'
import assert from 'assert'
import { FastifyPluginAsync } from 'fastify'
import countries from 'i18n-iso-countries'
import { Readable } from 'stream'
import { v4 as uuidv4 } from 'uuid'
import { responseSchema } from '../schemas/common'
import {
  analysisUserVeticalSchema,
  SimilarTaskQueryRequest,
  SimilarTaskQueryRequestSchema,
  SimilarTaskRequestSchema,
  SingleSearchRequestSchema,
  UnionSearchRequest,
  UnionSearchRequestSchema,
} from '../schemas/similar'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post<{
    Body: SimilarTaskRequest
  }>(
    '/create',
    {
      schema: {
        summary: 'Similar Task',
        description: '创建 Similar任务',
        tags: ['similar'],
        body: SimilarTaskRequestSchema,
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculateSimilarSearchQuota,
          QuotaType.SIMILAR_SEARCH,
        ),
        // instagramLimit(),
        sameTaskLimit,
      ),
    },
    async (request, reply) => {
      let {
        projectId,
        source,
        platform,
        regions,
        minSubscribers,
        maxSubscribers,
        lastPublishedDays,
        videosAverageViews,
        maxVideosAverageViews,
        minAverageLikeCount,
        maxAverageLikeCount,
        reason,
        banList,
        allowList,
        kolDescription,
        taskRound,
        ttMode,
        ttModeReason,
        ytbMode,
        ytbVideoIds,
        insMode,
        twitterMode,
        twitterUserNames,
      } = request.body as SimilarTaskRequest
      const { user } = request as any
      try {
        const project = await prisma.project.findFirstOrThrow({
          where: { id: projectId, deletedAt: null },
        })

        if (regions) {
          regions = regions
            .filter(
              (region) =>
                typeof region === 'string' && region.length === 2 && countries.isValid(region),
            )
            .map((region) => region.trim().toUpperCase())
        }

        if (!kolDescription?.length && project.config) {
          const config = project.config as ProjectConfig
          kolDescription = config.kolDescription
        }

        const createTaskRequest: createTaskRequest = {
          projectId,
          source,
          platform,
          regions: regions?.length ? regions : undefined,
          minSubscribers,
          maxSubscribers,
          lastPublishedDays,
          videosAverageViews,
          maxVideosAverageViews,
          minAverageLikeCount,
          maxAverageLikeCount,
          banList: banList?.length ? banList : undefined,
          allowList: allowList?.length ? allowList : undefined,
          kolDescription: kolDescription !== '' ? kolDescription : undefined,
          taskRound: Number(taskRound ?? 1),
          ttMode:
            ttMode !== undefined && ttMode !== null ? Number(ttMode) : Number(TT_MODE_DEFAULT),
          ttModeReason: ttModeReason ?? 'default Embedding Mode',
          ytbMode:
            ytbMode !== undefined && ytbMode !== null ? Number(ytbMode) : Number(YTB_MODE_DEFAULT),
          ytbVideoIds: ytbVideoIds?.length ? ytbVideoIds : [],
          insMode:
            insMode !== undefined && insMode !== null ? Number(insMode) : Number(INS_MODE_DEFAULT),
          twitterMode:
            twitterMode !== undefined && twitterMode !== null
              ? Number(twitterMode)
              : Number(TWITTER_MODE_DEFAULT),
          twitterUserNames: twitterUserNames?.length ? twitterUserNames : [],
        }

        // check source
        switch (platform) {
          case KolPlatform.TIKTOK: {
            if (!createTaskRequest.ttMode) {
              throwError(StatusCodes.BAD_REQUEST, 'ttMode is invalid')
            }
            if (createTaskRequest.source.startsWith('@')) {
              createTaskRequest.source = createTaskRequest.source.slice(1)
            }
            const userDetail = await TiktokApi.getInstance().getUserDetail({
              unique_id: createTaskRequest.source.toLowerCase(),
            })
            createTaskRequest.source = userDetail
              ? userDetail.user.uniqueId
              : createTaskRequest.source.toLowerCase()
            break
          }
          case KolPlatform.INSTAGRAM: {
            try {
              if (!createTaskRequest.insMode) {
                throwError(StatusCodes.BAD_REQUEST, 'insMode is invalid')
              }
              const related = await retryUtil.retryWithValidator(
                async () => {
                  return await InstagramRapidApiV3.getInstance().getUserRelatedUsers({
                    username: source,
                  })
                },
                (result) => result && result.length > 0, // 验证结果是否有效
                3, // 最多重试3次
                1000, // 初始延迟1000ms
                2, // 指数退避因子
                (error: any, retryCount: number, nextDelay: number) => {
                  console.warn(
                    `获取Instagram相关用户失败，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试`,
                    error,
                  )
                },
              )
              if (!related?.length) {
                throwError(StatusCodes.BAD_REQUEST, ERROR_MESSAGES.NO_RELATED_KOL)
              }
            } catch (err) {
              console.error('create task failed:', err)
              throwError(StatusCodes.BAD_REQUEST, ERROR_MESSAGES.NO_RELATED_KOL)
            }
            break
          }
          case KolPlatform.YOUTUBE: {
            try {
              if (!createTaskRequest.ytbMode) {
                throwError(StatusCodes.BAD_REQUEST, 'ytbMode is invalid')
              }
              const channelId = await YoutubeApi.getInstance().getYoutubeChannelId(source)
              createTaskRequest.source = channelId ?? source
              createTaskRequest.channelHandle = source
              const videos = await YoutubeApi.getInstance().getChannelVideos(channelId ?? source)
              if (!videos?.length) {
                throwError(StatusCodes.BAD_REQUEST, 'YOUTUBE_NO_VIDEO')
              }
            } catch (err) {
              console.error('YouTube API error:', err)
              Sentry.captureException(err)
              throwError(StatusCodes.BAD_REQUEST, 'YOUTUBE_API_ERROR')
            }
            break
          }
          case KolPlatform.TWITTER: {
            if (!createTaskRequest.twitterMode) {
              throwError(StatusCodes.BAD_REQUEST, 'twitterMode is invalid')
            }

            try {
              const sourceUser = await retryUtil.retryWithValidator(
                async () => {
                  return await twitter241Api.getUserByScreenName(source)
                },
                (result) => !!result,
                3, // 最多重试3次
                1000, // 初始延迟1000ms
                2, // 指数退避因子
                (result, error, retryCount, nextDelay) => {
                  if (error) {
                    console.warn(
                      `[Twitter] 获取源用户失败，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试`,
                      error,
                    )
                  } else if (!result) {
                    console.warn(
                      `[Twitter] 获取源用户返回空结果，将在 ${nextDelay}ms 后进行第 ${retryCount} 次重试`,
                    )
                  }
                },
              )

              if (!sourceUser) {
                throwError(StatusCodes.BAD_REQUEST, '找不到Twitter源用户')
              }
            } catch (error) {
              console.error('Twitter API error:', error)
              Sentry.captureException(error)
              throwError(StatusCodes.BAD_REQUEST, 'TWITTER_API_ERROR')
            }

            break
          }
          default:
            break
        }

        Logger.info('createTaskRequest', createTaskRequest)
        const newTask = await TaskService.getInstance().createTask(
          projectId,
          createTaskRequest,
          user.id,
          reason as TaskReason,
          TaskType.SIMILAR,
        )
        return reply.status(201).send(successResponse(newTask))
      } catch (error) {
        console.error('create task fail:', error)
        Sentry.captureException(error)
        Sentry.captureEvent({
          message: 'createTaskFailed',
          tags: {
            userId: user.id,
          },
        })
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.get(
    '/',
    {
      schema: {
        summary: 'Similar Task',
        description: '获取 Similar 任务状态',
        tags: ['similar'],
        query: SimilarTaskQueryRequestSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.query as SimilarTaskQueryRequest
      const { user } = request as any
      if (!taskId) {
        return reply.status(400).send({ error: 'taskId is required' })
      }

      try {
        const task = await prisma.similarChannelTask.findUniqueOrThrow({
          where: {
            id: taskId,
          },
        })

        if (task.createdBy !== user.id) {
          return reply.status(403).send({ error: 'Forbidden' })
        }

        if (
          task.status === SimilarChannelTaskStatus.PENDING ||
          task.status === SimilarChannelTaskStatus.PROCESSING ||
          task.status === SimilarChannelTaskStatus.RESULT_READY
        ) {
          return reply.status(200).send({
            status: task.status,
            message: 'Task is still processing',
          })
        }
        const platform = (task.params as createTaskRequest).platform as KolPlatform
        let message = ''
        if (task.status === SimilarChannelTaskStatus.FAILED) {
          switch (platform) {
            case KolPlatform.INSTAGRAM:
              message = `task ${task.id} failed`
              break
            default:
              message = `task ${task.id} failed,this task will not consume quota`
              break
          }
          return reply.send({
            status: task.status,
            message: message,
          })
        }
        return reply.send({
          status: task.status,
          message: task.status,
        })
      } catch (error) {
        console.error('Error fetching task:', error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.get(
    '/:taskId',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { taskId } = request.params as any
      const { user } = request as any
      if (!taskId) {
        return reply.status(400).send({ error: 'taskId is required' })
      }

      try {
        const task = await prisma.similarChannelTask.findUniqueOrThrow({
          where: {
            id: taskId,
          },
        })

        if (task.createdBy !== user.id) {
          return reply.status(403).send({ error: 'Forbidden' })
        }

        if (
          task.status === SimilarChannelTaskStatus.PENDING ||
          task.status === SimilarChannelTaskStatus.PROCESSING ||
          task.status === SimilarChannelTaskStatus.RESULT_READY
        ) {
          return reply.status(200).send({
            status: task.status,
            message: 'Task is still processing',
          })
        }
        const platform = (task.params as createTaskRequest).platform as KolPlatform
        let message = ''
        if (task.status === SimilarChannelTaskStatus.FAILED) {
          switch (platform) {
            case KolPlatform.INSTAGRAM:
              message = `task ${task.id} failed`
              break
            default:
              message = `task ${task.id} failed,this task will not consume quota`
              break
          }
          return reply.status(200).send({
            status: task.status,
            message: message,
          })
        }
        return reply.status(200).send({
          status: task.status,
          message: task.status,
        })
      } catch (error) {
        console.error('Error fetching task:', error)
        return reply.status(500).send({ error: 'An error occurred while fetching the task.' })
      }
    },
  )

  fastify.post(
    '/rate',
    withAuth(checkQuota(QuotaCost.CARD_QUERY_CHECK), superlikeLimit),
    async (request, reply) => {
      try {
        const { projectId, kolId, attitude } = request.body as any
        if (!Object.values(ProjectKolAttitude).includes(attitude)) {
          return reply.status(500).send('Unsupported attitude: ' + attitude)
        }
        if (!projectId || !kolId) {
          return reply.status(400).send({
            success: false,
            message: 'projectId and kolId are required',
          })
        }

        const exist = await prisma.projectKol.findFirst({
          where: {
            projectId,
            kolId,
          },
        })
        if (exist) {
          const updated = await prisma.projectKol.update({
            where: {
              id: exist.id,
            },
            data: {
              attitude: attitude,
              rateBy: (request as any).user.id,
            },
          })
          return reply.send({
            success: true,
            message: 'Attitude updated to ' + attitude,
            relation: updated,
          })
        } else {
          const created = await prisma.projectKol.create({
            data: {
              projectId,
              kolId,
              similarTaskId: '',
              attitude: attitude,
              rateBy: (request as any).user.id,
            },
          })
          return reply.send({
            success: true,
            message: 'Record created with attitude set to ' + attitude,
            relation: created,
          })
        }
      } catch (error) {
        return reply.status(500).send({
          success: false,
          message: handleUnknownError(error),
        })
      }
    },
  )

  fastify.post('/rateBatch', withAuth(), async (request, reply) => {
    try {
      const {
        projectId,
        kolIds,
        attitude,
      }: { projectId: string; kolIds: string[]; attitude: ProjectKolAttitude } = request.body as any
      assert(attitude === 'LIKE', new Error('Unsupported attitude: ' + attitude))
      //  如果字段为空，则返回错误
      if (!projectId || !kolIds?.length) {
        return reply.status(400).send({
          success: false,
          message: 'projectId and kolId are required',
        })
      }
      let created = undefined
      let updated = undefined
      // 使用 findFirst 查找现有记录
      const exists = await prisma.projectKol.findMany({
        where: {
          projectId,
          kolId: {
            in: kolIds,
          },
        },
        select: {
          id: true,
          kolId: true,
        },
      })
      if (exists.length) {
        updated = await prisma.projectKol.updateMany({
          where: {
            id: {
              in: exists.map((exist) => exist.id),
            },
          },
          data: {
            attitude: attitude as ProjectKolAttitude,
            rateBy: (request as any).user.id,
          },
        })
      }
      const existKolIds = exists.map((exist) => exist.kolId)
      const notExists: string[] = Array.from(
        new Set(kolIds.filter((kolId: string) => kolId && !existKolIds.includes(kolId))),
      )
      if (notExists.length) {
        const inserts = notExists.map((kolId: string) => ({
          projectId,
          kolId: kolId as string,
          similarTaskId: '', // 设置为空的 similarTaskId
          attitude: attitude as ProjectKolAttitude,
          rateBy: (request as any).user.id,
        }))
        created = await prisma.projectKol.createMany({
          data: inserts,
        })
      }
      return reply
        .status(200)
        .send(
          successResponse(`create ${created?.count} records and update ${updated?.count} records`),
        )
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/projectKol/likes', { preHandler: needSupabaseAuth }, async (request, reply) => {
    try {
      const { projectId } = request.query as any
      if (!projectId) {
        return reply.status(400).send(serverErrorResponse('projectId is required'))
      }
      // 根据 projectId 找到所有的 likes，并查出相关的 kolInfo 信息，只返回指定字段
      const projectKols = await prisma.projectKol.findMany({
        where: {
          projectId,
          attitude: {
            in: [ProjectKolAttitude.LIKE, ProjectKolAttitude.SUPERLIKE],
          },
        },
        include: {
          KolInfo: {
            select: {
              id: true,
              title: true,
              description: true,
              email: true,
              platformAccount: true,
              platform: true,
            },
          },
        },
      })

      // 提取并返回需要的结果
      const results = projectKols.map((projectKol) => ({
        projectKolId: projectKol.id,
        kolInfo: projectKol.KolInfo, // 只包含 select 中的字段
      }))

      return reply.status(200).send(successResponse(results, 'success'))
    } catch (error) {
      console.error('Error fetching likes:', handleUnknownError(error))
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get(
    '/unionSearch',
    {
      schema: {
        summary: 'Union Search',
        description: '为 TT Similar Search 专用的 union search 类型',
        tags: ['similar'],
        query: UnionSearchRequestSchema,
        response: responseSchema,
      },
      ...withAuth(handleSearchQuota(QuotaCost.FAILED_SEARCH, QuotaType.FAILED_SEARCH)),
    },
    async (request, reply) => {
      const { projectId, hasPost } = request.query as UnionSearchRequest
      assert(projectId, new Error('projectId is required'))

      const result = hasPost
        ? await getProjectCandidatesWithPostsByTaskType<KolInfoWithScore[]>(
            projectId,
            TaskType.SIMILAR,
            KolPlatform.TIKTOK,
          )
        : await getProjectCandidates(projectId, TaskType.SIMILAR, KolPlatform.TIKTOK)

      console.log(`搜索完成，找到 ${result?.length} 个KOL`)
      const NoKolMessage = 'no suitable kol found, Please consider loosening your criteria'
      if (result.length === 0) {
        throwError(StatusCodes.BAD_REQUEST, NoKolMessage)
      }
      return reply.status(200).send(successResponse(result, 'success'))
    },
  )

  // 展示候选队列，以及对应任务的状态
  fastify.get('/task/record/:taskId', { preHandler: needSupabaseAuth }, async (request, reply) => {
    const { taskId } = request.params as any
    try {
      if (!taskId) {
        return reply.status(400).send(serverErrorResponse('taskId is required'))
      }

      const lastTask = await prisma.similarChannelTask.findUnique({
        where: {
          id: taskId,
        },
      })
      if (!lastTask) {
        return reply.status(404).send(serverErrorResponse('task not found'))
      }

      let result
      if (typeof lastTask.result === 'string') {
        try {
          result = JSON.parse(lastTask.result)
        } catch (parseError) {
          console.error('JSON解析错误:', parseError)
          return reply.status(500).send(serverErrorResponse('无法解析任务结果'))
        }
      } else if (typeof lastTask.result === 'object') {
        result = lastTask.result
      } else {
        return reply.status(500).send(serverErrorResponse('任务结果格式不正确'))
      }

      const chineseHeaders: { [key: string]: string } = {
        source: '源频道',
        taskId: '任务ID',
        uniqueIds: '返回任务所有的作者',
        count: '作者数量',
        time: '处理时间(秒)',
        hashtags: '标签',
        searchWords: '搜索词',
      }

      const json2csv = new ParseTransform({
        fields: Object.keys(chineseHeaders).map((key) => ({
          label: chineseHeaders[key],
          value: (row: any) => {
            if (key === 'uniqueIds' || key === 'hashtags' || key === 'searchWords') {
              return Array.isArray(row[key]) ? row[key].join('\n') : ''
            } else if (key === 'time') {
              return (Number(row[key]) / 1000).toFixed(2) // 转换为秒，保留两位小数
            } else {
              return row[key] !== undefined ? String(row[key]) : ''
            }
          },
        })),
        withBOM: true,
      })

      const dataStream = Readable.from(JSON.stringify([result]), {
        encoding: 'utf8',
      })

      const fileId = uuidv4()
      const fileName = `${fileId}.csv`

      const transform = dataStream.pipe(json2csv)

      await uploadFileByStream(`csv/${fileName}`, transform)

      return reply.send({
        url: `${HOST ?? ''}/files/csv/${fileName}`,
      })
    } catch (error) {
      console.error('处理请求时发生错误:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.post(
    '/singleSearch',
    {
      schema: {
        summary: 'Single Search',
        description: '给 YOUTUBE 和 INSTAGRAM 用的 Similar 搜索',
        tags: ['similar'],
        body: SingleSearchRequestSchema,
        response: responseSchema,
      },
      ...withAuth(handleSearchQuota(QuotaCost.FAILED_SEARCH, QuotaType.FAILED_SEARCH)),
    },
    async (request, reply) => {
      const { taskId, hasPost } = request.body as { taskId: string; hasPost: boolean }
      if (!taskId) {
        throwError(StatusCodes.BAD_REQUEST, 'taskId is required')
      }
      try {
        const task = await TaskService.getInstance().getTaskByTaskId(taskId)
        const { params } = task
        const platform = (params as createTaskRequest).platform as KolPlatform
        const queryResult = hasPost
          ? await getProjectCandidatesWithPostsByTaskType<KolInfoWithScore[]>(
              task.projectId,
              task.type,
              platform,
            )
          : await getProjectCandidates(task.projectId, task.type, platform)

        if (!queryResult?.length) {
          const message = `no suitable kol found, Please consider loosening your criteria.`
          return reply.status(400).send(serverErrorResponse(message))
        }
        // 临时逻辑
        if (platform == KolPlatform.YOUTUBE) {
          queryResult.forEach(
            (result) => (result.platformAccount = 'channel/' + result.platformAccount),
          )
        }
        return reply.status(200).send(successResponse(queryResult, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  /**
   * @description 查询某个项目下某种任务的最新一个已经完成的任务，用户切换项目的时候做历史任务恢复
   */
  fastify.get(
    '/latestTask',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { projectId, taskType } = request.query as { projectId: string; taskType: TaskType }
        const { user } = request as any
        assert(projectId, new Error('projectId is required'))
        assert(
          taskType && Object.values(TaskType).includes(taskType),
          new Error('taskType is required'),
        )

        const taskTypeEnum = TaskType[taskType as keyof typeof TaskType]

        console.log('taskTypeEnum', taskTypeEnum)
        assert(taskTypeEnum, new Error('Invalid task type'))

        if (taskTypeEnum && SINGL_TASK_TYPE_ENUM.includes(taskTypeEnum as SINGL_TASK_TYPE)) {
          console.log(`find latest valid task for single task ${taskTypeEnum}`)
          const latestValidTask = await findLatestByTaskType(projectId, taskTypeEnum)
          if (latestValidTask && latestValidTask.createdBy !== user.id) {
            return reply.status(403).send(serverErrorResponse('Permission denied'))
          }
          return reply.status(200).send(successResponse(latestValidTask))
        } else {
          console.log(`find latest valid task for page task ${taskTypeEnum}`)
          const tasks = await findTasksByTaskType(projectId, taskTypeEnum)
          if (tasks.length > 0 && tasks[0].createdBy !== user.id) {
            return reply.status(403).send(serverErrorResponse('Permission denied'))
          }
          return reply.status(200).send(successResponse(tasks))
        }
      } catch (error) {
        console.error('Error in getting latest task:', error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 获取用户垂类信息
  fastify.get(
    '/analysis/userVertical',
    {
      schema: analysisUserVeticalSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const { id, handler, platform, language } = request.query as {
        id: string
        handler: string
        platform: KolPlatform
        language: string
      }
      const allowList: string[] = []
      const banList: string[] = []

      const result = await analysisUserKolDescription(id, handler, platform, language)
      return reply
        .status(200)
        .send(successResponse({ kolDescription: result, allowList, banList }, 'success'))
    },
  )
}

export default router
