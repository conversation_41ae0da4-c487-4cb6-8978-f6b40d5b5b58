import { StatusC<PERSON>, throwError } from '@/common/errors/statusCodes'
import { successResponse } from '@/common/response/response'
import { SCHEME_NAMES, getPaymentPlanByPriceId } from '@/config/stripe/plans'
import { STRIPE_WEBHOOK_SECRET } from '@/config/stripe/stripe'
import { FullResponseSchema } from '@/config/swagger'
import { withAuth } from '@/middlewares/auth'
import { subscriptionLimit } from '@/middlewares/subscriptionLimit'
import {
  cancelSubscription,
  createCheckoutSession,
  createPortalSession,
  findUserActiveSubscription,
  findUserFirstSubscription,
  handleWebhookEvent,
  retrieveCheckoutSession,
  stripeClient,
} from '@/services/stripe'
import { SubscriptionStatus, TransitionStatus, prisma } from '@repo/database'
import { FastifyPluginAsync, FastifyReply, FastifyRequest } from 'fastify'
import {
  CancelSubscriptionResponseSchema,
  CreateCheckoutRequest,
  CreateCheckoutRequestSchema,
  CreateCheckoutResponseSchema,
  CreatePortalResponseSchema,
  ResubscribeResponseSchema,
  SubscriptionPollingQuery,
  SubscriptionPollingQuerySchema,
  SubscriptionPollingResponseSchema,
  SubscriptionStatusResponseSchema,
  VerifySessionQuery,
  VerifySessionQuerySchema,
  VerifySessionResponseSchema,
} from '../schemas/subscription'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // create checkout session
  fastify.post<{ Body: CreateCheckoutRequest }>(
    '/create-checkout',
    {
      schema: {
        tags: ['subscription'],
        summary: '创建支付会话',
        description: '创建Stripe Checkout支付会话',
        body: CreateCheckoutRequestSchema,
        response: FullResponseSchema(CreateCheckoutResponseSchema),
      },
      ...withAuth(subscriptionLimit),
    },
    async (request, reply) => {
      const { priceId } = request.body
      const user = (request as any).user
      const paymentPlan = getPaymentPlanByPriceId(priceId)

      if (!paymentPlan) {
        throwError(StatusCodes.BAD_REQUEST, `invalid price id: ${priceId}`)
      }

      const plan = paymentPlan as NonNullable<typeof paymentPlan>
      // get the first subscription
      const firstSubscription = await findUserFirstSubscription(user.id)

      if (firstSubscription) {
        // allow plans
        const allowPlans = getPaymentPlanByPriceId(firstSubscription.priceId)

        // check scheme isolation
        if (allowPlans && allowPlans.scheme !== plan.scheme) {
          const allowSchemeName = SCHEME_NAMES[allowPlans.scheme]
          const newSchemeName = SCHEME_NAMES[plan.scheme]
          throwError(
            StatusCodes.CONFLICT,
            `you are using ${allowSchemeName}, can not switch to ${newSchemeName}.`,
          )
        }

        // subscription check
        if (plan.isSubscription) {
          const activeSubscription = await findUserActiveSubscription(user.id)

          if (activeSubscription && activeSubscription.planType === plan.type) {
            if (activeSubscription.status === SubscriptionStatus.PAST_DUE) {
              throwError(
                StatusCodes.PAYMENT_REQUIRED,
                'you have a past due subscription, please update your payment method or cancel the existing subscription first.',
              )
            }

            if (activeSubscription.cancelAtPeriodEnd) {
              throwError(
                StatusCodes.CONFLICT,
                'your subscription has been canceled, please resubscribe.',
              )
            }

            throwError(StatusCodes.CONFLICT, 'you have already subscribed to this plan.')
          }
        }
      }

      const session = await createCheckoutSession(user.id, user.email, priceId)

      return reply.status(200).send(
        successResponse({
          url: session.url,
          sessionId: session.id,
        }),
      )
    },
  )
  // cancel
  fastify.post(
    '/cancel',
    {
      schema: {
        tags: ['subscription'],
        summary: '取消订阅',
        description: '取消当前活跃的订阅',
        response: FullResponseSchema(CancelSubscriptionResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user

      const subscription = await findUserActiveSubscription(user.id)

      if (!subscription) {
        throwError(StatusCodes.NO_ACTIVE_SUBSCRIPTION, 'no active subscription')
      }

      if (subscription.cancelAtPeriodEnd) {
        throwError(StatusCodes.SUBSCRIPTION_INVALID, 'subscription has been canceled')
      }

      await prisma.stripeSubscription.update({
        where: { id: subscription.id },
        data: { transitionStatus: TransitionStatus.CANCELED_IN_PROGRESS },
      })

      const canceledSubscription = await cancelSubscription(subscription.stripeSubscriptionId)

      fastify.log.info(
        `user ${user.id} has canceled subscription ${subscription.stripeSubscriptionId}`,
      )
      return reply.status(200).send(successResponse(canceledSubscription, 'subscription canceling'))
    },
  )
  // resubscribe
  fastify.post(
    '/resubscribe',
    {
      schema: {
        tags: ['subscription'],
        summary: '恢复订阅',
        description: '恢复已取消但未到期的订阅',
        response: FullResponseSchema(ResubscribeResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user

      const existingSubscription = await findUserActiveSubscription(user.id)

      if (!existingSubscription) {
        throwError(StatusCodes.NO_ACTIVE_SUBSCRIPTION, 'no active subscription')
      }

      if (
        !(
          (existingSubscription.status === 'ACTIVE' ||
            existingSubscription.status === 'PAST_DUE') &&
          existingSubscription.cancelAtPeriodEnd === true
        )
      ) {
        let errorMessage = 'only canceled but not expired subscription can be restored'
        if (
          existingSubscription.status === 'CANCELED' ||
          existingSubscription.status === 'INACTIVE'
        ) {
          errorMessage =
            'your subscription has been fully expired, please create a new subscription'
        } else if (
          (existingSubscription.status === 'ACTIVE' ||
            existingSubscription.status === 'PAST_DUE') &&
          !existingSubscription.cancelAtPeriodEnd
        ) {
          errorMessage = 'your subscription is active, no need to restore'
        }
        throwError(StatusCodes.SUBSCRIPTION_INVALID, errorMessage)
      }

      await prisma.stripeSubscription.update({
        where: { id: existingSubscription.id },
        data: { transitionStatus: TransitionStatus.RESTORING },
      })

      const reSubscription = await stripeClient.subscriptions.update(
        existingSubscription.stripeSubscriptionId,
        {
          cancel_at_period_end: false,
        },
      )

      fastify.log.info(
        `user ${user.id} has restored subscription ${existingSubscription.stripeSubscriptionId}`,
      )
      return reply.status(200).send(successResponse(reSubscription, 'subscription restoring'))
    },
  )
  // status
  fastify.get(
    '/status',
    {
      schema: {
        tags: ['subscription'],
        summary: '查询订阅状态',
        description: '查询当前用户的订阅状态',
        response: FullResponseSchema(SubscriptionStatusResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any

      const subscription = await findUserActiveSubscription(user.id)

      return reply.status(200).send(
        successResponse(
          subscription || {
            status: 'INACTIVE',
          },
        ),
      )
    },
  )
  // portal
  fastify.post(
    '/portal',
    {
      schema: {
        tags: ['subscription'],
        summary: '创建客户门户会话',
        description: '创建Stripe客户门户会话，用于管理订阅',
        response: FullResponseSchema(CreatePortalResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user
      const customer = await prisma.stripeCustomer.findUnique({
        where: { userId: user.id },
      })

      if (!customer) {
        throwError(StatusCodes.NOT_FOUND, 'no stripe customer found')
      }

      const session = await createPortalSession(customer.stripeCustomerId)

      if (!session) {
        throwError(StatusCodes.SERVER_ERROR, 'create portal session failed')
      }

      return reply.status(200).send(
        successResponse({
          url: session.url,
        }),
      )
    },
  )

  // verify checkout session
  fastify.get<{ Querystring: VerifySessionQuery }>(
    '/session/verify',
    {
      schema: {
        tags: ['subscription'],
        summary: '验证支付会话',
        description: '验证Stripe Checkout会话状态',
        querystring: VerifySessionQuerySchema,
        response: FullResponseSchema(VerifySessionResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { sessionId } = request.query

      const session = await retrieveCheckoutSession(sessionId)
      if (!session) {
        throwError(StatusCodes.NOT_FOUND, 'session not found')
      }

      return reply.status(200).send(successResponse(session))
    },
  )
  // webhook
  fastify.post('/webhook', async (request: FastifyRequest, reply: FastifyReply) => {
    const signature = request.headers['stripe-signature']
    const rawBody = request.rawBody

    const quickResponse = () => reply.status(200).send(successResponse({ received: true }))
    if (!signature || !rawBody) {
      fastify.log.error('missing necessary information:', {
        hasSignature: !!signature,
        hasRawBody: !!rawBody,
      })
      return quickResponse()
    }

    try {
      const strSignature = Array.isArray(signature) ? signature[0] : signature
      const event = stripeClient.webhooks.constructEvent(
        rawBody,
        strSignature,
        STRIPE_WEBHOOK_SECRET as string,
      )

      fastify.log.info(`事件验证成功: ${event.type} [${event.id}]`)

      quickResponse()

      handleWebhookEvent(event)
        .then(() => fastify.log.info(`event handled successfully: ${event.id}`))
        .catch((err) => fastify.log.error(`event handled failed: ${err.message}`))
    } catch (err: any) {
      fastify.log.error('webhook handled failed:', err.message)
      return quickResponse()
    }
  })

  // 订阅状态轮询
  fastify.get<{ Querystring: SubscriptionPollingQuery }>(
    '/subscription-polling',
    {
      schema: {
        tags: ['subscription'],
        summary: '订阅状态轮询',
        description: '轮询订阅的处理状态',
        querystring: SubscriptionPollingQuerySchema,
        response: FullResponseSchema(SubscriptionPollingResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { subscriptionId } = request.query

      const subscription = await prisma.stripeSubscription.findUnique({
        where: { stripeSubscriptionId: subscriptionId },
      })

      if (!subscription) {
        throwError(StatusCodes.NOT_FOUND, 'subscription not found')
      }
      const isProcessingComplete = subscription.transitionStatus === TransitionStatus.NORMAL
      return reply.status(200).send(successResponse(isProcessingComplete))
    },
  )
}

export default router
