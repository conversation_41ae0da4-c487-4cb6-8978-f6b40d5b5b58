import { handleUnknownError } from '@/common/errorHandler'
import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import {
  serverErrorResponse,
  successResponse,
  successResponseWithoutData,
} from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import { withAuth } from '@/middlewares/auth'
import { EnterpriseService } from '@/services/enterprise.service'
import GoogleSheetService from '@/services/googleSheet.service'
import {
  getUserGoogleSheet,
  initUserGoogleSheet,
  userGoogleSheetHasInited,
} from '@/services/userGoogleSheet.service'
import { FastifyPluginAsync } from 'fastify'
import {
  GetUserGoogleSheetQuerySchema,
  GetUserGoogleSheetResponseSchema,
} from '../schemas/googleSheet'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post('/update', async (request, reply) => {
    const { spreadsheetId } = request.body as { spreadsheetId: string }
    if (!spreadsheetId) {
      return reply.status(400).send(serverErrorResponse('spreadsheetId 不能为空'))
    }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      await googleSheetService.updateSheet(spreadsheetId.trim())

      return reply.send(successResponseWithoutData())
    } catch (error) {
      console.error('更新 Google Sheet 失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get(
    '/get',
    {
      schema: {
        tags: ['googleSheet'],
        summary: '获取用户 Google Sheet',
        description:
          '获取用户的 Google Sheet 数据，支持查询企业内其他成员的数据。不传userId则查询当前用户数据',
        querystring: GetUserGoogleSheetQuerySchema,
        response: FullResponseSchema(GetUserGoogleSheetResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user
      const { userId } = request.query as { userId?: string }
      const targetUserId = userId || user.id

      if (targetUserId !== user.id) {
        await EnterpriseService.getInstance().validateEnterprisePermission(user.id, targetUserId)
        const userGoogleSheet = await getUserGoogleSheet(targetUserId)
        if (!userGoogleSheet) {
          throwError(StatusCodes.NOT_FOUND, 'this user has not initialized a google sheet')
        }
        return reply.send(successResponse(userGoogleSheet))
      } else {
        if (!(await userGoogleSheetHasInited(user))) {
          await initUserGoogleSheet(user)
        }

        const userGoogleSheet = await getUserGoogleSheet(targetUserId)
        if (!userGoogleSheet) {
          throwError(StatusCodes.NOT_FOUND, '用户 Google Sheet 不存在')
        }

        return reply.send(successResponse(userGoogleSheet))
      }
    },
  )

  // 获取 Sheet 数据，并更新表格格式和数据
  fastify.get('/schema-data-render/:spreadsheetId', withAuth(), async (request, reply) => {
    const { spreadsheetId } = request.params as { spreadsheetId: string }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      // 更新表格格式，先搭台子后唱戏
      await googleSheetService.updateSheet(spreadsheetId.trim())
      // 再更新 Post Data 表格数据
      await googleSheetService.updateAllSheetsData(spreadsheetId)

      return reply.send(successResponseWithoutData())
    } catch (error) {
      console.error('获取 Sheet 数据失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/get-spreadsheet-info/:spreadsheetId', withAuth(), async (request, reply) => {
    try {
      const { spreadsheetId } = request.params as { spreadsheetId: string }
      const googleSheetService = GoogleSheetService.getInstance()
      const spreadsheetInfo = await googleSheetService.getSpreadsheetInfo(spreadsheetId)
      return reply.send(successResponse(spreadsheetInfo))
    } catch (error) {
      console.error('获取表格信息失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取表格版本历史记录
  fastify.get('/version-history/:spreadsheetId', withAuth(), async (request, reply) => {
    const { spreadsheetId } = request.params as { spreadsheetId: string }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      const history = await googleSheetService.getSheetHistory(spreadsheetId)

      return reply.send(
        successResponse({
          history: history.revisions.map((revision) => ({
            time: new Date(revision.time).toLocaleString('zh-CN', {
              timeZone: 'Asia/Shanghai',
            }),
            user: revision.user,
            revisionId: revision.revisionId,
          })),
        }),
      )
    } catch (error) {
      console.error('获取表格历史记录失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取特定版本的内容
  fastify.get('/version-content/:spreadsheetId/:revisionId', withAuth(), async (request, reply) => {
    const { spreadsheetId, revisionId } = request.params as {
      spreadsheetId: string
      revisionId: string
    }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      const versionContent = await googleSheetService.getSheetRevisionContent(
        spreadsheetId,
        revisionId,
      )

      return reply.send(successResponse({ content: versionContent }))
    } catch (error) {
      console.error('获取表格版本内容失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 同步成本数据
  fastify.post('/sync-publication-data/:spreadsheetId', withAuth(), async (request, reply) => {
    const { spreadsheetId } = request.params as { spreadsheetId: string }
    if (!spreadsheetId) {
      return reply.status(400).send(serverErrorResponse('spreadsheetId 不能为空'))
    }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      await googleSheetService.syncPublicationCostData(spreadsheetId)

      return reply.status(200).send(successResponseWithoutData())
    } catch (error) {
      console.error('同步成本数据失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取所有 Google Sheets
  fastify.get(
    '/list-all',
    {
      schema: {
        tags: ['googleSheet'],
        summary: '获取所有 Google Sheets',
        description: '管理员接口：获取系统中所有的 Google Sheets',
      },
      ...withAuth(),
    },
    async (_, reply) => {
      const googleSheetService = GoogleSheetService.getInstance()
      const result = await googleSheetService.getAllSpreadsheets()

      return reply.send(
        successResponse({
          total: result.total,
          spreadsheets: result.spreadsheets.map((sheet) => ({
            id: sheet.id,
            name: sheet.name,
            createdTime: new Date(sheet.createdTime).toLocaleString('zh-CN', {
              timeZone: 'Asia/Shanghai',
            }),
            modifiedTime: new Date(sheet.modifiedTime).toLocaleString('zh-CN', {
              timeZone: 'Asia/Shanghai',
            }),
            webViewLink: sheet.webViewLink,
            owners: sheet.owners,
            editors: sheet.editors,
          })),
        }),
      )
    },
  )

  // 只更新不可编辑的数据
  fastify.get('/update-readonly/:spreadsheetId', withAuth(), async (request, reply) => {
    const { spreadsheetId } = request.params as { spreadsheetId: string }
    if (!spreadsheetId) {
      return reply.status(400).send(serverErrorResponse('spreadsheetId 不能为空'))
    }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      // 同步成本数据
      await googleSheetService.syncPublicationCostData(spreadsheetId)
      //重新渲染表格
      await googleSheetService.updateSheet(spreadsheetId)
      // 更新 Post Data 表格数据
      await googleSheetService.updatePostSheetData(spreadsheetId)
      return reply.status(200).send(successResponseWithoutData())
    } catch (error) {
      console.error('更新只读数据失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 设置多个用户的写入权限
  fastify.post('/set-writers/:spreadsheetId', withAuth(), async (request, reply) => {
    const { spreadsheetId } = request.params as { spreadsheetId: string }
    const { emails } = request.body as { emails: string[] }

    if (!spreadsheetId) {
      return reply.status(400).send(serverErrorResponse('spreadsheetId 不能为空'))
    }

    if (!Array.isArray(emails) || emails.length === 0) {
      return reply.status(400).send(serverErrorResponse('emails 必须是非空数组'))
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const invalidEmails = emails.filter((email) => !emailRegex.test(email))
    if (invalidEmails.length > 0) {
      return reply
        .status(400)
        .send(serverErrorResponse(`以下邮箱格式不正确: ${invalidEmails.join(', ')}`))
    }

    try {
      const googleSheetService = GoogleSheetService.getInstance()
      await googleSheetService.setMultipleWriterAccess(spreadsheetId, emails)

      return reply.status(200).send(successResponseWithoutData())
    } catch (error) {
      console.error('设置用户写入权限失败:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })
}

export default router
