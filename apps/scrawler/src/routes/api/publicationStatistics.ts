import { handleUnknownError } from '@/common/errorHandler'
import { StatusCodes, throwError } from '@/common/errors/statusCodes'
import { serverErrorResponse, successResponse } from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import { QuotaCost } from '@/enums/QuotaCost'
import Sentry from '@/infras/sentry'
import { withAuth } from '@/middlewares/auth'
import { checkQuota, dynamicCheckQuota, handleQuotaWithResponse } from '@/middlewares/quota'
import { publicationService } from '@/services/PublicationStatistics.service'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { EnterpriseService } from '@/services/enterprise.service'
import { publicationTagService } from '@/services/publicationTag.service'
import TaskService from '@/services/task'
import { OVERSEAS_PLATFORMS } from '@/types/kol'
import {
  PluginPublicationAudienceTaskParams,
  PublicationAudienceTaskParams,
  TrackEasyKOLTaskRequestParams,
  UpdateVideoDataRequestParams,
} from '@/types/publicationStatistics'
import { TrackEasyKOLTaskParams } from '@/types/task'
import Logger from '@/utils/logger'
import { PaginationService } from '@/utils/pagination'
import { parseUrlUtils } from '@/utils/parseUrl'
import { KolPlatform, QuotaType, TaskReason, TaskType, prisma } from '@repo/database'
import { FastifyPluginAsync } from 'fastify'
import {
  AudienceRequest,
  AudienceSchema,
  BatchDeletePublicationsRequest,
  BatchDeletePublicationsSchema,
  DeletePublicationParams,
  DeletePublicationParamsSchema,
  GetPublicationSummaryQuery,
  GetPublicationSummaryQuerySchema,
  GetPublicationSummaryResponseSchema,
  PluginAudienceRequest,
  PluginAudienceResultParams,
  PluginAudienceResultParamsSchema,
  PluginAudienceSchema,
  PublicationParams,
  PublicationParamsSchema,
  TrackByPublicationIdsRequest,
  TrackByPublicationIdsSchema,
  TrackPublicationQuery,
  TrackPublicationQuerySchema,
  TrackPublicationResponseSchema,
  TrackRecentDataRequest,
  TrackRecentDataRequestSchema,
  TrackRecentDataResponseSchema,
  UpdatePublicationDataBody,
  UpdatePublicationDataBodySchema,
} from '../schemas/publicationStatistics'
import {
  GetTagPublicationsRequest,
  GetTagPublicationsRequestSchema,
  GetTagPublicationsResponseSchema,
} from '../schemas/publicationTag'

const router: FastifyPluginAsync = async (fastify): Promise<void> => {
  fastify.post(
    '/task/track-new',
    withAuth(
      handleQuotaWithResponse(QuotaCost.EASYKOL_DATA_TRACK_URL, QuotaType.EASYKOL_DATA_TRACK_URL),
    ),
    async (request, reply) => {
      const { tiktok, youtube, instagram, douyin, xhs, tagIds } =
        request.body as TrackEasyKOLTaskRequestParams
      const user = (request as any).user

      const platformData = {
        tiktok: {
          ids: [...(tiktok?.videosIds || [])].filter((id) => id && id.trim() !== ''),
          urls: (tiktok?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        youtube: {
          ids: [...(youtube?.videosIds || []), ...(youtube?.shortsIds || [])].filter(
            (id) => id && id.trim() !== '',
          ),
          urls: (youtube?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        instagram: {
          ids: [...(instagram?.postsIds || []), ...(instagram?.reelsIds || [])].filter(
            (id) => id && id.trim() !== '',
          ),
          urls: (instagram?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        douyin: {
          ids: [...(douyin?.videosIds || []), ...(douyin?.noteIds || [])].filter(
            (id) => id && id.trim() !== '',
          ),
          urls: (douyin?.urls || []).filter((url) => url && url.trim() !== ''),
        },
        xhs: {
          ids: [...(xhs?.noteIds || [])].filter((id) => id && id.trim() !== ''),
          urls: (xhs?.urls || []).filter((url) => url && url.trim() !== ''),
        },
      }

      const hasData = Object.values(platformData).some(
        (platform) => platform.ids.length > 0 || platform.urls.length > 0,
      )

      if (!hasData) {
        return reply
          .status(400)
          .send(serverErrorResponse('at least one platform video data is required'))
      }

      try {
        const userGoogleSheet = await prisma.userGoogleSheet.findFirst({
          where: {
            userId: user.id,
          },
        })

        if (!userGoogleSheet) {
          return reply.status(404).send(serverErrorResponse('no google sheet found'))
        }

        const params: TrackEasyKOLTaskParams = {
          spreadsheetId: userGoogleSheet.spreadsheetId,
          tiktok,
          youtube,
          instagram,
          douyin,
          xhs,
          tagIds,
        }

        const projectId = `EasyKOL_Track:${userGoogleSheet.spreadsheetId}`
        console.log(`[task/track-new] 用户 ${user.id} 创建任务 ${projectId}`)

        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          TaskReason.EASYKOL_TRACK,
          TaskType.EASYKOL_TRACK,
        )

        return reply
          .status(200)
          .send(
            successResponse(
              task,
              'create task success, we will handle your request in the background',
            ),
          )
      } catch (error) {
        console.error('[task/track-new] 创建任务失败:', error)
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.post(
    '/task/track-recent',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '追踪最近发布数据',
        description:
          '创建任务来追踪用户最近发布的视频数据，支持多个平台（TikTok、YouTube、Instagram、抖音、小红书）。支持查询企业内其他成员的数据，不传userId则查询当前用户数据',
        body: TrackRecentDataRequestSchema,
        response: FullResponseSchema(TrackRecentDataResponseSchema),
      },
      ...withAuth(
        handleQuotaWithResponse(QuotaCost.EASYKOL_DATA_TRACK_URL, QuotaType.EASYKOL_DATA_TRACK_URL),
      ),
    },
    async (request, reply) => {
      const { user } = request as any
      const { userId, page = 1, pageSize = 20 } = request.body as TrackRecentDataRequest

      const targetUserId = userId || user.id

      if (targetUserId !== user.id) {
        await EnterpriseService.getInstance().validateEnterprisePermission(user.id, targetUserId)
      }

      const userGoogleSheet = await prisma.userGoogleSheet.findUniqueOrThrow({
        where: { userId: targetUserId },
      })

      const { skip } = PaginationService.handlePagination({ page, pageSize })
      const result = await prisma.publicationStatisticsSheetData.findMany({
        where: {
          spreadsheetId: userGoogleSheet.spreadsheetId,
        },
        orderBy: { publishDate: 'desc' },
        skip,
        take: pageSize,
      })

      if (result.length === 0) {
        throwError(StatusCodes.BAD_REQUEST, 'no recent posts found')
      }

      const postRequestParams: UpdateVideoDataRequestParams = {
        spreadsheetId: userGoogleSheet.spreadsheetId,
        tiktok: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter(
              (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
            )
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        youtube: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_VIDEO',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          shortsIds: result
            .filter(
              (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_SHORT',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.YOUTUBE)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        instagram: {
          reelsIds: result
            .filter(
              (item) =>
                item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_REEL',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          postsIds: result
            .filter(
              (item) =>
                item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_POST',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.INSTAGRAM)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        douyin: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_VIDEO',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          noteIds: result
            .filter(
              (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_NOTE',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.DOUYIN)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        xhs: {
          noteIds: result
            .filter((item) => item.platform === KolPlatform.XHS)
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.XHS)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
      }

      const tiktokCount = postRequestParams.tiktok?.urls?.length || 0
      const youtubeCount = postRequestParams.youtube?.urls?.length || 0
      const instagramCount = postRequestParams.instagram?.urls?.length || 0
      const douyinCount = postRequestParams.douyin?.urls?.length || 0
      const xhsCount = postRequestParams.xhs?.urls?.length || 0

      const totalItemsToTrack = tiktokCount + youtubeCount + instagramCount + douyinCount + xhsCount

      if (totalItemsToTrack === 0) {
        throwError(StatusCodes.BAD_REQUEST, 'no video data found')
      }

      const params: TrackEasyKOLTaskParams = {
        spreadsheetId: userGoogleSheet.spreadsheetId,
        tiktok: postRequestParams.tiktok,
        youtube: postRequestParams.youtube,
        instagram: postRequestParams.instagram,
        douyin: postRequestParams.douyin,
        xhs: postRequestParams.xhs,
      }

      const projectId = `EasyKOL_Track:${userGoogleSheet.spreadsheetId}`
      const task = await TaskService.getInstance().createTask(
        projectId,
        params,
        user.id,
        TaskReason.EASYKOL_TRACK,
        TaskType.EASYKOL_TRACK,
      )

      return reply
        .status(200)
        .send(
          successResponse(
            task,
            'create task success, we will handle your request in the background',
          ),
        )
    },
  )

  // 通过publicationIds数组追踪数据
  fastify.post<{ Body: TrackByPublicationIdsRequest }>(
    '/task/update-batch',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '通过发布ID数组创建追踪任务',
        description:
          '创建任务来追踪指定ID的发布数据，支持查询企业内其他成员的数据，不传userId则查询当前用户数据',
        body: TrackByPublicationIdsSchema,
        response: FullResponseSchema(TrackRecentDataResponseSchema),
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculatePublicationStatisticsUpdateBatch,
          QuotaType.EASYKOL_DATA_TRACK_URL,
        ),
      ),
    },
    async (request, reply) => {
      const { user } = request as any
      const { userId, publicationIds } = request.body

      const targetUserId = userId || user.id

      if (targetUserId !== user.id) {
        await EnterpriseService.getInstance().validateEnterprisePermission(user.id, targetUserId)
      }

      const userGoogleSheet = await prisma.userGoogleSheet.findUniqueOrThrow({
        where: { userId: targetUserId },
      })

      const result = await prisma.publicationStatisticsSheetData.findMany({
        where: {
          id: { in: publicationIds },
          spreadsheetId: userGoogleSheet.spreadsheetId,
        },
      })

      if (result.length === 0) {
        throwError(StatusCodes.NOT_FOUND, 'no publications found with provided ids')
      }

      const postRequestParams: UpdateVideoDataRequestParams = {
        spreadsheetId: userGoogleSheet.spreadsheetId,
        tiktok: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter(
              (item) => item.platform === KolPlatform.TIKTOK && item.postType === 'TIKTOK_POST',
            )
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        youtube: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_VIDEO',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          shortsIds: result
            .filter(
              (item) => item.platform === KolPlatform.YOUTUBE && item.postType === 'YOUTUBE_SHORT',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.YOUTUBE)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        instagram: {
          reelsIds: result
            .filter(
              (item) =>
                item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_REEL',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          postsIds: result
            .filter(
              (item) =>
                item.platform === KolPlatform.INSTAGRAM && item.postType === 'INSTAGRAM_POST',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.INSTAGRAM)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        douyin: {
          videosIds: result
            .filter(
              (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_VIDEO',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          noteIds: result
            .filter(
              (item) => item.platform === KolPlatform.DOUYIN && item.postType === 'DOUYIN_NOTE',
            )
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.DOUYIN)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
        xhs: {
          noteIds: result
            .filter((item) => item.platform === KolPlatform.XHS)
            .map((item) => item.videoId)
            .filter((item) => item !== null) as string[],
          urls: result
            .filter((item) => item.platform === KolPlatform.XHS)
            .map((item) => item.postLink)
            .filter((item) => item !== null) as string[],
        },
      }

      const tiktokCount = postRequestParams.tiktok?.urls?.length || 0
      const youtubeCount = postRequestParams.youtube?.urls?.length || 0
      const instagramCount = postRequestParams.instagram?.urls?.length || 0
      const douyinCount = postRequestParams.douyin?.urls?.length || 0
      const xhsCount = postRequestParams.xhs?.urls?.length || 0

      const totalItemsToTrack = tiktokCount + youtubeCount + instagramCount + douyinCount + xhsCount

      if (totalItemsToTrack === 0) {
        throwError(StatusCodes.NOT_FOUND, 'no video data found')
      }

      const params: TrackEasyKOLTaskParams = {
        spreadsheetId: userGoogleSheet.spreadsheetId,
        tiktok: postRequestParams.tiktok,
        youtube: postRequestParams.youtube,
        instagram: postRequestParams.instagram,
        douyin: postRequestParams.douyin,
        xhs: postRequestParams.xhs,
      }

      const projectId = `EasyKOL_Track:${userGoogleSheet.spreadsheetId}`
      const task = await TaskService.getInstance().createTask(
        projectId,
        params,
        user.id,
        TaskReason.EASYKOL_TRACK,
        TaskType.EASYKOL_TRACK,
      )

      return reply
        .status(200)
        .send(
          successResponse(
            task,
            'create task success, we will handle your request in the background',
          ),
        )
    },
  )

  fastify.delete<{ Params: DeletePublicationParams }>(
    '/:publicationId',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '删除投放数据',
        description: '删除指定的投放视频数据',
        params: DeletePublicationParamsSchema,
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId } = request.params
        const result = await publicationService.deleteUserPostData(user.id, publicationId)
        return reply.send(successResponse(result))
      } catch (error) {
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.delete<{ Body: BatchDeletePublicationsRequest }>(
    '/delete-batch',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '批量删除投放数据',
        description: '批量删除多个投放视频数据',
        body: BatchDeletePublicationsSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any
      const { publicationIds } = request.body

      const result = await publicationService.batchDeleteUserPostData(user.id, publicationIds)
      return reply.send(successResponse(result, '批量删除成功'))
    },
  )

  fastify.put<{ Params: PublicationParams; Querystring: TrackPublicationQuery }>(
    '/track/:publicationId',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '追踪投放数据',
        description:
          '追踪指定的投放视频数据，支持查询企业内其他成员的数据，不传userId则追踪当前用户数据',
        params: PublicationParamsSchema,
        querystring: TrackPublicationQuerySchema,
        response: FullResponseSchema(TrackPublicationResponseSchema),
      },
      ...withAuth(checkQuota(QuotaCost.EASYKOL_DATA_TRACK_URL, QuotaType.EASYKOL_DATA_TRACK_URL)),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId } = request.params
        const { userId } = request.query

        const targetUserId = userId || user.id

        if (targetUserId !== user.id) {
          await EnterpriseService.getInstance().validateEnterprisePermission(user.id, targetUserId)
        }

        const result = await publicationService.trackExsitingUserPostData(
          targetUserId,
          publicationId,
        )
        return reply.send(successResponse(result))
      } catch (error) {
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.put<{ Params: PublicationParams; Body: UpdatePublicationDataBody }>(
    '/update/:publicationId',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '更新投放数据',
        description: '更新指定投放数据的成本、备注和联系信息',
        params: PublicationParamsSchema,
        body: UpdatePublicationDataBodySchema,
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId } = request.params
        const { totalCost, notes1, notes2, contactInformation } = request.body

        const result = await publicationService.updatePublicationData(user.id, publicationId, {
          totalCost,
          notes1,
          notes2,
          contactInformation,
        })
        return reply.send(successResponse(result))
      } catch (error) {
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // post audience
  fastify.post<{ Body: AudienceRequest }>(
    '/audience',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '创建受众分析任务',
        description:
          '创建任务来分析发布数据的受众，支持查询企业内其他成员的数据，不传userId则查询当前用户数据',
        body: AudienceSchema,
        response: FullResponseSchema(TrackRecentDataResponseSchema),
      },
      ...withAuth(checkQuota(QuotaCost.POST_AUDIENCE, QuotaType.POST_AUDIENCE)),
    },
    async (request, reply) => {
      const { userId, publicationId } = request.body
      const currentUserId = (request as any).user?.id

      const targetUserId = userId || currentUserId

      if (targetUserId !== currentUserId) {
        await EnterpriseService.getInstance().validateEnterprisePermission(
          currentUserId,
          targetUserId,
        )
      }

      const publication = await prisma.publicationStatisticsSheetData.findUniqueOrThrow({
        where: { id: publicationId },
        include: {
          userGoogleSheet: true,
        },
      })

      if (publication.userGoogleSheet.userId !== targetUserId) {
        throwError(StatusCodes.FORBIDDEN, 'no permission')
      }

      // post audience task
      const taskParams: PublicationAudienceTaskParams = {
        publicationId,
        googleSheetId: publication.userGoogleSheet.spreadsheetId,
        platform: publication.platform!,
        postLink: publication.postLink!,
        videoId: publication.videoId!,
      }

      // only support overseas platforms
      if (!OVERSEAS_PLATFORMS.includes(publication.platform! as any)) {
        throwError(StatusCodes.BAD_REQUEST, 'no support platform')
      }

      const task = await TaskService.getInstance().createTask(
        `PostAudience:${publicationId}`, // fake projectId
        taskParams,
        currentUserId,
        TaskReason.SEARCH,
        TaskType.POST_AUDIENCE,
      )

      return reply.status(200).send(successResponse(task))
    },
  )

  // post audience
  fastify.post<{ Body: PluginAudienceRequest }>(
    '/plugin/audience',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '创建受众分析任务',
        body: PluginAudienceSchema,
      },
      ...withAuth(checkQuota(QuotaCost.POST_AUDIENCE, QuotaType.POST_AUDIENCE)),
    },
    async (request, reply) => {
      const { url } = request.body
      const userId = (request as any).user?.id
      const videoId = parseUrlUtils.extractVideoId(url)
      const platform = parseUrlUtils.getPlatformFromUrl(url)

      Logger.info(`plugin post audience url: ${url}, videoId: ${videoId}, platform: ${platform}`)

      if (!videoId) {
        throwError(StatusCodes.NOT_FOUND, 'the video id is not found')
      }

      if (!platform || !OVERSEAS_PLATFORMS.includes(platform)) {
        throwError(StatusCodes.BAD_REQUEST, 'no support platform')
      }

      // post audience task
      const taskParams: PluginPublicationAudienceTaskParams = {
        videoId: videoId,
        platform: platform,
        postLink: url,
      }

      // TODO: check the videoId already exists. to invoke the rapid api

      const task = await TaskService.getInstance().createTask(
        `Plugin PostAudience:${videoId}`, // fake projectId
        taskParams,
        userId,
        TaskReason.SEARCH,
        TaskType.POST_AUDIENCE,
      )

      return reply.status(200).send(successResponse(task))
    },
  )

  // get plugin audience task result
  fastify.get(
    '/plugin/audience/:taskId',
    {
      schema: {
        params: PluginAudienceResultParamsSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.params as PluginAudienceResultParams

      const task = await prisma.similarChannelTask.findUnique({
        where: { id: taskId },
        select: {
          result: true,
        },
      })

      if (!task) {
        throwError(StatusCodes.NOT_FOUND, 'task not found')
      }

      return reply.status(200).send(successResponse(task.result))
    },
  )

  // 获取投放数据统计
  fastify.get(
    '/summary',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '获取投放数据统计',
        description:
          '获取投放数据统计，包括视频总数、播放总数、总成本、CPM。不传日期参数则查询所有数据',
        querystring: GetPublicationSummaryQuerySchema,
        response: FullResponseSchema(GetPublicationSummaryResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user
      const { userId, startDate, endDate } = request.query as GetPublicationSummaryQuery

      const result = await publicationService.getPublicationSummary(
        user.id,
        startDate,
        endDate,
        userId,
      )
      return reply.status(200).send(successResponse(result))
    },
  )

  fastify.post<{ Body: GetTagPublicationsRequest }>(
    '/publications',
    {
      schema: {
        tags: ['publicationStatistics'],
        summary: '获取标签发布数据',
        description:
          '获取用户的标签发布数据，支持查询企业内其他成员的数据。不传userId则查询当前用户数据',
        body: GetTagPublicationsRequestSchema,
        response: FullResponseSchema(GetTagPublicationsResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { user } = request as any
      const { userId, page = 1, pageSize = 20, ids, platforms } = request.body

      const targetUserId = userId || user.id

      if (targetUserId !== user.id) {
        await EnterpriseService.getInstance().validateEnterprisePermission(user.id, targetUserId)
      }

      const result = await publicationTagService.getTagPublications(targetUserId, {
        page,
        pageSize,
        ids,
        platforms,
      })
      return reply.send(successResponse(result))
    },
  )
}
export default router
