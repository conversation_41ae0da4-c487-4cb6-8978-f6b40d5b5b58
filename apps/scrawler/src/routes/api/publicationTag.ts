import { serverErrorResponse, successResponse } from '@/common/response/response'
import { withAuth } from '@/middlewares/auth'
import { publicationTagService } from '@/services/publicationTag.service'
import {
  AddTagToPublicationParams,
  GetPublicationTagsResponse,
  RemoveTagFromPublicationParams,
} from '@/types/publicationTag'
import { FastifyPluginAsync } from 'fastify'
import {
  addTagToPublicationSchema,
  getPublicationTagsSchema,
  getTagPostLinksSchema,
  removeTagFromPublicationSchema,
} from '../schemas/publicationTag'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post<{
    Body: AddTagToPublicationParams
  }>(
    '/',
    {
      schema: addTagToPublicationSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId, tagId } = request.body
        await publicationTagService.validatePublicationOwnership(user.id, publicationId)

        const result = await publicationTagService.addTagToPublication(
          user.id,
          publicationId,
          tagId,
        )
        return reply.send(successResponse(result, 'Tag added successfully'))
      } catch (error) {
        return reply
          .status(500)
          .send(
            serverErrorResponse(error instanceof Error ? error.message : 'Internal server error'),
          )
      }
    },
  )

  fastify.delete<{
    Body: RemoveTagFromPublicationParams
  }>(
    '/',
    {
      schema: removeTagFromPublicationSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId, tagId } = request.body

        // 验证 publicationId 是否属于当前用户
        await publicationTagService.validatePublicationOwnership(user.id, publicationId)

        await publicationTagService.removeTagFromPublication(user.id, publicationId, tagId)
        return reply.send(successResponse(null, 'Tag removed successfully'))
      } catch (error) {
        return reply
          .status(500)
          .send(
            serverErrorResponse(error instanceof Error ? error.message : 'Internal server error'),
          )
      }
    },
  )

  fastify.get<{
    Params: { publicationId: string }
  }>(
    '/:publicationId',
    {
      schema: getPublicationTagsSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      try {
        const { user } = request as any
        const { publicationId } = request.params
        const tags: GetPublicationTagsResponse = await publicationTagService.getPublicationTags(
          user.id,
          publicationId,
        )
        return reply.send(successResponse(tags, 'get publication tags successfully'))
      } catch (error) {
        return reply
          .status(500)
          .send(
            serverErrorResponse(error instanceof Error ? error.message : 'Internal server error'),
          )
      }
    },
  )

  fastify.get<{
    Params: { id: string }
  }>(
    '/:id/post-links',
    {
      schema: getTagPostLinksSchema,
      ...withAuth(),
    },
    async (request, reply) => {
      const user = (request as any).user
      const { id } = request.params
      const result = await publicationTagService.getAllPostLinks(user.id, id)
      return reply.send(successResponse(result))
    },
  )
}

export default router
