import { throwError } from '@/common/errors/statusCodes'
import {
  errorResponse,
  serverErrorResponse,
  StatusCodes,
  successResponse,
} from '@/common/response/response'
import { CommonResponseSchema, FullResponseSchema } from '@/config/swagger'
import Sentry from '@/infras/sentry'
import { needSupabaseAuth, withAuth } from '@/middlewares/auth'
import EmailService from '@/services/email'
import { getUser, isWeb, verifyDeviceBan } from '@/utils/auth'
import { populateProviderCrential } from '@/utils/populate'
import { prisma, ProviderType, UserSignatureLog } from '@repo/database'
import { FastifyPluginAsync } from 'fastify'
import {
  CredentialReq,
  CredentialReqSchema,
  CredentialSignatureReq,
  CredentialSignatureReqSchema,
  ProviderCredentialResponseSchema,
} from '../schemas/credential'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post(
    '/',
    {
      schema: {
        summary: '添加授权信息',
        tags: ['credentials'],
        body: CredentialReqSchema,
        response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { accessToken, refreshToken, provider: providerKey } = request.body as CredentialReq
      const user = getUser(request)

      const provider = await prisma.provider.findFirst({
        where: {
          key: providerKey,
        },
      })

      if (!provider) {
        throwError(StatusCodes.BAD_REQUEST, 'Invalid provider')
      }

      // validate token scopes
      if (providerKey === ProviderType.GOOGLE) {
        const tokenInfo = await EmailService.getInstance().getCurrentScopes(
          accessToken,
          refreshToken,
        )
        if (tokenInfo.error) {
          throwError(StatusCodes.BAD_REQUEST, tokenInfo.error)
        }

        if (!tokenInfo.scopes?.includes('https://www.googleapis.com/auth/gmail.send')) {
          return reply.send(successResponse(null, 'success without gmail.send scope'))
        }
      }
      const credential = await prisma.providerCredential.upsert({
        where: {
          providerId_createdBy: {
            createdBy: user.id,
            providerId: provider.id,
          },
        },
        create: {
          providerId: provider.id,
          createdBy: user.id,
          accessToken,
          refreshToken,
        },
        update: {
          accessToken,
          refreshToken,
        },
        include: {
          Provider: true,
        },
      })

      // await prepareCredential(credential);

      return reply.status(201).send(populateProviderCrential(credential))
    },
  )

  fastify.get(
    '/',
    {
      schema: {
        summary: '获取授权信息',
        tags: ['credentials'],
        response: FullResponseSchema(ProviderCredentialResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const user = getUser(request)
      const credential = await prisma.providerCredential.findMany({
        where: {
          createdBy: user.id,
        },
        include: {
          Provider: true,
        },
      })
      return reply.send(successResponse(credential.map(populateProviderCrential)))
    },
  )

  fastify.post(
    '/signature',
    {
      schema: {
        summary: '用户浏览器签名',
        tags: ['credential'],
        body: CredentialSignatureReqSchema,
        response: CommonResponseSchema,
      },
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      try {
        const { browserId, extensionId } = request.body as CredentialSignatureReq
        const { easykolversion } = request.headers
        const user = (request as any).user
        if (!browserId && !extensionId) {
          return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST))
        }
        await prisma.userSignatureLog.create({
          data: {
            userId: user.id,
            browserId: browserId ?? '',
            extensionId: extensionId ?? '',
            version: easykolversion ?? '',
          } as UserSignatureLog,
        })
        // web端不封禁
        verifyDeviceBan(user.id, browserId ?? '', await isWeb(request))
        return reply.status(200).send(successResponse('success'))
      } catch (err) {
        Sentry.captureException(err)
        return reply.status(500).send(serverErrorResponse)
      }
    },
  )
}

export default router
