import { handleUnknownError } from '@/common/errorHandler'
import { StatusC<PERSON>, throwError } from '@/common/errors/statusCodes'
import {
  notFoundResponse,
  serverErrorResponse,
  successResponse,
  successResponseWithoutData,
} from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import { withAuth } from '@/middlewares/auth'
import {
  BuyInfoCardForMemberRequest,
  BuyInfoCardForMemberSchema,
  GetEnterpriseDetailParams,
  GetEnterpriseDetailParamsSchema,
  GetEnterpriseDetailResponseSchema,
  UpdateEnterpriseBasicInfoRequest,
  UpdateEnterpriseBasicInfoSchema,
} from '@/routes/schemas/enterprise'
import { EnterpriseService } from '@/services/enterprise.service'
import {
  AddEnterpriseMembersParams,
  CreateEnterpriseParams,
  GetEnterprisesParams,
  UpdateEnterpriseBasicInfoParams,
  UpdateEnterpriseParams,
} from '@/types/enterprise'
import { ERROR_MESSAGES } from '@/types/memberShip'
import Logger from '@/utils/logger'
import { FastifyPluginAsync } from 'fastify'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  //TODO 管理员权限
  fastify.post('/', withAuth(), async (request, reply) => {
    const params = request.body as CreateEnterpriseParams
    const operatorId = (request as any).user?.id || 'admin'

    try {
      const enterprise = await EnterpriseService.getInstance().createEnterprise(params, operatorId)
      return reply.send(successResponse(enterprise))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 更新企业
  fastify.put('/:id', withAuth(), async (request, reply) => {
    const { id } = request.params as { id: string }
    const params = request.body as UpdateEnterpriseParams
    const operatorId = (request as any).user?.id || 'admin'

    try {
      const enterprise = await EnterpriseService.getInstance().updateEnterprise(
        id,
        params,
        operatorId,
      )
      return reply.send(successResponse(enterprise))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 更新企业基本信息，不包括额度、过期时间等。
  fastify.put<{ Params: GetEnterpriseDetailParams; Body: UpdateEnterpriseBasicInfoRequest }>(
    '/:id/basicInfo',
    {
      schema: {
        tags: ['enterprise'],
        summary: '更新企业基本信息',
        description: '更新企业基本信息，包括名称、描述、联系方式、信息卡访问权限等',
        params: GetEnterpriseDetailParamsSchema,
        body: UpdateEnterpriseBasicInfoSchema,
        response: FullResponseSchema(GetEnterpriseDetailResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { id } = request.params as { id: string }
      const params = request.body
      const operatorId = (request as any).user?.id || 'admin'

      Logger.info(`更新企业基本信息，params: ${JSON.stringify(params)}`)
      const enterprise = await EnterpriseService.getInstance().updateEnterpriseBasicInfo(
        id,
        params as UpdateEnterpriseBasicInfoParams,
        operatorId,
      )
      return reply.send(successResponse(enterprise))
    },
  )

  // 获取企业详情
  fastify.get<{ Params: GetEnterpriseDetailParams }>(
    '/:id',
    {
      schema: {
        tags: ['enterprise'],
        summary: '获取企业详情',
        description: '根据企业ID获取企业详细信息，包括成员列表',
        params: GetEnterpriseDetailParamsSchema,
        response: FullResponseSchema(GetEnterpriseDetailResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { id } = request.params

      const enterprise = await EnterpriseService.getInstance().getEnterpriseDetail(id)
      if (!enterprise) {
        throwError(StatusCodes.ENTERPRISE_NOT_FOUND)
      }
      return reply.status(200).send(successResponse(enterprise))
    },
  )

  // 获取企业列表
  fastify.get('/', withAuth(), async (request, reply) => {
    const params = request.query as GetEnterprisesParams

    try {
      const enterprises = await EnterpriseService.getInstance().getEnterprises(params)
      return reply.send(successResponse(enterprises))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 添加企业成员
  fastify.post('/:id/members', withAuth(), async (request, reply) => {
    const { id } = request.params as { id: string }
    const params = request.body as AddEnterpriseMembersParams
    const operatorId = (request as any).user?.id || 'admin'

    // 将邮箱全部转换为小写
    params.emails = params.emails.map((email) => email.toLowerCase())

    try {
      const result = await EnterpriseService.getInstance().addEnterpriseMembers(
        id,
        params,
        operatorId,
      )
      return reply.send(successResponse(result))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 设置企业某用户为管理员
  fastify.post('/:enterpriseId/members/:memberShipId/admin', withAuth(), async (request, reply) => {
    const { enterpriseId, memberShipId } = request.params as {
      enterpriseId: string
      memberShipId: string
    }
    if (!enterpriseId || !memberShipId) {
      return reply.status(400).send(notFoundResponse('enterpriseId and memberShipId are required'))
    }

    const operatorId = (request as any).user?.id

    try {
      const result = await EnterpriseService.getInstance().setEnterpriseAdmin(
        enterpriseId,
        memberShipId,
        operatorId,
      )
      return reply.send(successResponse(result))
    } catch (error) {
      if (error instanceof Error) {
        switch (error.message) {
          case ERROR_MESSAGES.ENTERPRISE_NOT_FOUND:
          case ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND:
            return reply.status(404).send(notFoundResponse(error.message))
          case 'No permission to set enterprise admin':
          case 'User is already an enterprise admin':
            return reply.status(400).send(serverErrorResponse(error.message))
          default:
            return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
        }
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 删除企业成员
  fastify.delete('/:id/members/:userId', withAuth(), async (request, reply) => {
    const { id, userId } = request.params as { id: string; userId: string }
    const operatorId = (request as any).user?.id || 'admin'

    try {
      const result = await EnterpriseService.getInstance().removeEnterpriseMember(
        id,
        userId,
        operatorId,
      )
      return reply.send(successResponse(result))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 删除企业
  fastify.delete('/:enterpriseId', withAuth(), async (request, reply) => {
    const { enterpriseId } = request.params as { enterpriseId: string }
    const operatorId = (request as any).user?.id || 'admin'

    try {
      await EnterpriseService.getInstance().deleteEnterprise(enterpriseId, operatorId)
      return reply.send(successResponseWithoutData('删除企业成功'))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 开启企业成员配额限制
  fastify.post(
    '/:enterpriseId/members/:userId/enable-quota-limit',
    withAuth(),
    async (request, reply) => {
      const { enterpriseId, userId } = request.params as { enterpriseId: string; userId: string }
      const operatorId = (request as any).user?.id

      try {
        const result = await EnterpriseService.getInstance().enableMemberQuotaLimit(
          enterpriseId,
          userId,
          operatorId,
        )
        return reply.send(successResponse(result))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 关闭企业成员配额限制
  fastify.post(
    '/:enterpriseId/members/:userId/disable-quota-limit',
    withAuth(),
    async (request, reply) => {
      const { enterpriseId, userId } = request.params as { enterpriseId: string; userId: string }
      const operatorId = (request as any).user?.id

      try {
        const result = await EnterpriseService.getInstance().disableMemberQuotaLimit(
          enterpriseId,
          userId,
          operatorId,
        )
        return reply.send(successResponse(result))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 更新企业成员配额限制
  fastify.put('/:enterpriseId/members/:userId/quota-limit', withAuth(), async (request, reply) => {
    const { enterpriseId, userId } = request.params as { enterpriseId: string; userId: string }
    const { dailyLimit } = request.body as { dailyLimit: number }
    const operatorId = (request as any).user?.id

    try {
      const result = await EnterpriseService.getInstance().updateMemberQuotaLimit(
        enterpriseId,
        userId,
        dailyLimit,
        operatorId,
      )
      return reply.send(successResponse(result))
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 企业管理员给企业成员购买信息卡会员
  fastify.post<{ Body: BuyInfoCardForMemberRequest }>(
    '/infoCard/buy',
    {
      schema: {
        tags: ['enterprise'],
        summary: '企业管理员给企业成员购买信息卡会员',
        body: BuyInfoCardForMemberSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const params = request.body
      const operatorId = (request as any).user?.id

      const result = await EnterpriseService.getInstance().buyInfoCardForMember(
        params.enterpriseId,
        params.membershipId,
        params.months,
        operatorId,
      )
      return reply.send(successResponse(result))
    },
  )
}

export default router
