import { errorResponse, StatusCodes, successResponse } from '@/common/response/response'
import { CommonResponseSchema, FullResponseSchema } from '@/config/swagger'
import { FeishuTrackRes } from '@/constants/feishu'
import { isAdmin, withAuth } from '@/middlewares/auth'
import { feishuShortcutApiKeyMiddleware } from '@/middlewares/feishuQuota'
import FeishuTrackService from '@/services/feishu/index'
import Logger from '@/utils/logger'
import { prisma } from '@repo/database'
import { FastifyPluginAsync, FastifyReply, FastifyRequest } from 'fastify'
import {
  FeishuApiKeyStatsQuerySchema,
  FeishuApiKeyStatsResponseSchema,
  FeishuLogParamsSchema,
  FeishuLogSchema,
  FeishuLogsQuerySchema,
  FeishuLogsResponseSchema,
  FeishuStatisticsQuerySchema,
  FeishuStatisticsResponseSchema,
  FeishuTrackReqSchema,
} from '../schemas/feishu'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.post(
    '/track',
    {
      preHandler: [feishuShortcutApiKeyMiddleware],
      schema: {
        summary: '飞书快捷短链接解析',
        tags: ['feishu'],
        body: FeishuTrackReqSchema,
        response: CommonResponseSchema,
      },
      preSerialization: async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
        (request as any).responsePayload = payload
      },
      onResponse: async (request: FastifyRequest, reply: FastifyReply) => {
        const payload = (request as any).responsePayload as FeishuTrackRes
        if (payload.useQuota > 0) {
          await prisma.feishuShortcutApiKey.update({
            where: { apiKey: payload.apiKey },
            data: { remainingQuota: { decrement: payload.useQuota } },
          })
        }
      },
    },
    async (request, reply) => {
      const body = JSON.parse(request.body as string)
      const { url, api_key } = body as { url: string; api_key: string }

      Logger.info('feishu track request', {
        bodyLength: (request.body as string)?.length || 0,
      })

      if (!url || !api_key) {
        return reply
          .status(200)
          .send(errorResponse(StatusCodes.BAD_REQUEST, `参数不能为空 ${url ? 'url' : 'api_key'}`))
      }

      try {
        // 获取客户端信息
        const clientInfo = {
          ipAddress:
            request.ip ||
            (request.headers['x-forwarded-for'] as string) ||
            (request.headers['x-real-ip'] as string) ||
            'unknown',
          userAgent: request.headers['user-agent'] || 'unknown',
        }

        Logger.info('feishu track client info', {
          ip: clientInfo.ipAddress,
          userAgent: clientInfo.userAgent,
          url: url,
        })

        const result = await FeishuTrackService.track(url, api_key, clientInfo)
        return reply.status(200).send(successResponse(result))
      } catch (error) {
        Logger.error('feishu track error', error as Error)

        return reply
          .status(200)
          .send(errorResponse(StatusCodes.BAD_REQUEST, (error as Error).message || '链接解析失败'))
      }
    },
  )

  // 获取日志列表 - 仅管理员
  fastify.get(
    '/logs',
    {
      schema: {
        tags: ['feishu'],
        summary: '获取飞书追踪日志列表',
        description: '获取飞书追踪请求的日志列表，支持按API Key、平台、状态等筛选',
        security: [{ bearerAuth: [] }],
        querystring: FeishuLogsQuerySchema,
        response: FullResponseSchema(FeishuLogsResponseSchema),
      },
      ...withAuth(isAdmin),
    },
    async (request, reply) => {
      try {
        const query = request.query as any
        const params = {
          apiKey: query.apiKey,
          platform: query.platform,
          status: query.status,
          startDate: query.startDate ? new Date(query.startDate) : undefined,
          endDate: query.endDate ? new Date(query.endDate) : undefined,
          limit: Math.min(query.limit || 50, 100), // 限制最大值
          offset: query.offset || 0,
        }

        const result = await FeishuTrackService.getLogs(params)
        return reply.send(successResponse(result))
      } catch (error) {
        Logger.error('Failed to get feishu logs:', error as Error)
        return reply.status(500).send(errorResponse(StatusCodes.SERVER_ERROR, '获取日志失败'))
      }
    },
  )

  // 获取日志详情 - 仅管理员
  fastify.get(
    '/logs/:requestId',
    {
      schema: {
        tags: ['feishu'],
        summary: '获取飞书追踪日志详情',
        description: '根据请求ID获取飞书追踪请求的详细日志信息',
        security: [{ bearerAuth: [] }],
        params: FeishuLogParamsSchema,
        response: FullResponseSchema(FeishuLogSchema),
      },
      ...withAuth(isAdmin),
    },
    async (request, reply) => {
      try {
        const { requestId } = request.params as { requestId: string }
        const log = await FeishuTrackService.getLogDetail(requestId)

        if (!log) {
          return reply.status(404).send(errorResponse(StatusCodes.NOT_FOUND, '日志不存在'))
        }

        return reply.send(successResponse(log))
      } catch (error) {
        Logger.error('Failed to get feishu log detail:', error as Error)
        return reply.status(500).send(errorResponse(StatusCodes.SERVER_ERROR, '获取日志详情失败'))
      }
    },
  )

  // 获取统计信息 - 仅管理员
  fastify.get(
    '/statistics',
    {
      schema: {
        tags: ['feishu'],
        summary: '获取飞书追踪统计信息',
        description: '获取飞书追踪请求的统计信息，包括成功率、错误率、平台分布等',
        security: [{ bearerAuth: [] }],
        querystring: FeishuStatisticsQuerySchema,
        response: FullResponseSchema(FeishuStatisticsResponseSchema),
      },
      ...withAuth(isAdmin),
    },
    async (request, reply) => {
      try {
        const query = request.query as any
        const params = {
          apiKey: query.apiKey,
          startDate: query.startDate ? new Date(query.startDate) : undefined,
          endDate: query.endDate ? new Date(query.endDate) : undefined,
        }

        const stats = await FeishuTrackService.getStatistics(params)
        return reply.send(successResponse(stats))
      } catch (error) {
        Logger.error('Failed to get feishu statistics:', error as Error)
        return reply.status(500).send(errorResponse(StatusCodes.SERVER_ERROR, '获取统计信息失败'))
      }
    },
  )

  // 获取 API Key 使用统计 - 仅管理员
  fastify.get(
    '/api-key-stats',
    {
      schema: {
        tags: ['feishu'],
        summary: '获取API Key使用统计',
        description: '获取各个API Key的使用统计信息，包括请求次数和配额消耗',
        security: [{ bearerAuth: [] }],
        querystring: FeishuApiKeyStatsQuerySchema,
        response: FullResponseSchema(FeishuApiKeyStatsResponseSchema),
      },
      ...withAuth(isAdmin),
    },
    async (request, reply) => {
      try {
        const query = request.query as any
        const startDate = query.startDate ? new Date(query.startDate) : undefined
        const endDate = query.endDate ? new Date(query.endDate) : undefined

        const stats = await FeishuTrackService.getApiKeyStats(startDate, endDate)
        return reply.send(successResponse(stats))
      } catch (error) {
        Logger.error('Failed to get API key stats:', error as Error)
        return reply
          .status(500)
          .send(errorResponse(StatusCodes.SERVER_ERROR, '获取API Key统计失败'))
      }
    },
  )
}

export default router
