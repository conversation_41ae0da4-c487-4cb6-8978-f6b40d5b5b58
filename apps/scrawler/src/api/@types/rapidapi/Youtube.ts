export type YoutubeType = 'video' | 'shorts' | 'channel' | 'playlist' | undefined

export type Thumbnail = {
  url: string
  width: number
  height: number
}

export type Banner = Thumbnail

export type Link = {
  title: string
  link: string
}

export interface QueryResponse<T, M> {
  continuation: string
  data: T[]
  msg: string
  meta: M
}

export type Video = {
  type: YoutubeType
  videoId: string
  title: string
  lengthText: string
  viewCount: string
  publishedTimeText: string
  publishedAt: string
  thumbnail: Thumbnail[]
  publishedDate?: string
}

export type ChannelVideo = Video & {
  description: string
  richThumbnail: Thumbnail[]
  channelId?: string
}

export type RelatedVideo = Video & {
  channelTitle?: string
  channelId?: string
  authorThumbnail?: Thumbnail[]
  channelHandle?: string
}

export type SearchVideoResult = {
  token?: string
  count: number
  data: RelatedVideo[]
  msg: string
}

export type VideoDetail = Video & {
  description: string
  channelId: string
  channelTitle?: string
  channelHandle?: string
  commentCount: string
  likeCount: string
}

export interface ChannelHome {
  channelId: string
  title: string
  description: string
  avatar: Thumbnail[]
  banner: Banner[]
  channelHandle: string
  subscriberCountText: string
  subscriberCount: number
  videosCountText: string
  videosCount: number
  keywords: string[]
  isFamilySafe: boolean
  isUnlisted: boolean
  tabs: string[]
  availableCountries: string[]
}

export interface Channel {
  channelId: string
  title: string
  description: string
  channelHandle: string
  banner: Banner[]
  tvBanner: Banner[]
  mobileBanner: Banner[]
  avatar: Thumbnail[]
  subscriberCountText: string
  subscriberCount: number
  videosCountText: string
  videosCount: number
  isVerified: boolean
  keywords: string[]
  isFamilySafe: boolean
  availableCountries: string[]
  joinedDate: string
  viewCount: string
  links: Link[]
  tabs: string[]
  videos: ChannelVideo[]
  country?: string
  hasEmail?: boolean
  email?: string | null

  // calculate fields
  embedding?: number[]
  emailSource?: string
  lastUpdatedAt?: number
  videosAverageViewCount?: number
}

// 评论
export interface VideoComment {
  commentId: string
  authorText: string
  authorChannelId: string
  authorThumbnail: Thumbnail[]
  textDisplay: string
  publishedTimeText: string
  publishDate: string
  publishedAt: string
  likesCount: string
  replyCount: string
  replyToken?: string
  authorIsChannelOwner: boolean
  isVerified: boolean
  isArtist: boolean
  isCreator: boolean
}

// 评论响应
export interface CommentResponse {
  commentsCount: string
  continuation: string
  data: VideoComment[]
  msg: string
}

// 频道视频响应
export interface ChannelVideosResponse {
  continuation: string
  data: ChannelVideo[]
  msg: string
}

// 频道Shorts响应
export interface ChannelShortsResponse {
  continuation: string
  data: ChannelVideo[]
  msg: string
}

export interface YoutubeHashTagBreakRes {
  meta: {
    hashtag: string
    videoCount: number
    channelCount: number
  }
  continuation: string
  data: {
    videoId: string
    title: string
    channelTitle: string
    channelId: string
    description: string
    viewCount: number
    publishedText: string
    lengthText: string
    thumbnail: Thumbnail[]
    richThumbnail: null | Thumbnail[]
    channelThumbnail: Thumbnail[]
  }[]
}

export interface ResolveResponse {
  webPageType: string
  isVanityUrl: boolean
  browseId: string
  params: string
}
