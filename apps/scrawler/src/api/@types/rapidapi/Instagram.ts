import { EmailSourceType } from '@/types/email'
import { extractEmail } from '@/utils/email'
import countries from 'i18n-iso-countries'

export type IgUserPostRequest = {
  username_or_id: string
  count?: number
  max_id?: number // Leave max_id empty for first request then provide next_max_id received from previous request to get more items.
}

export type PostType = 'post' | 'reel'

export type IgPost = {
  id: string
  type?: PostType
  thumbnail_url?: string
  comment_count: number
  like_count: number
  play_count: number
  caption: {
    text: string
  }
  created_at: number
  like_and_view_counts_disabled: boolean
  tags?: string[]
  username?: string
  userId?: string
  code?: string
  media_type?: number // 1: 图片, 2: 视频, 8: 轮播图
  media_name?: string // 'post', 'reel'
  video_url?: string
  is_pinned?: boolean
}

// 评论相关的数据结构
export interface IgComment {
  id: string
  text: string
  created_at: number
  created_at_utc: number
  like_count: number
  user_id: string
  username: string
  full_name: string
  profile_pic_url: string
  is_verified: boolean
  is_private: boolean
  has_liked_comment: boolean
  mentions?: string[]
  hashtags?: string[]
}

export interface IgCommentResponse {
  count: number
  total: number
  pagination_token?: string
  comments: IgComment[]
}

export function igComment(obj: any): IgComment {
  return {
    id: obj.id,
    text: obj.text || '',
    created_at: obj.created_at || 0,
    created_at_utc: obj.created_at_utc || 0,
    like_count: obj.like_count || 0,
    user_id: obj.user_id || '',
    username: obj.user?.username || '',
    full_name: obj.user?.full_name || '',
    profile_pic_url: obj.user?.profile_pic_url || '',
    is_verified: obj.user?.is_verified || false,
    is_private: obj.user?.is_private || false,
    has_liked_comment: obj.has_liked_comment || false,
    mentions: obj.mentions || [],
    hashtags: obj.hashtags || [],
  }
}

// Reels相关的数据结构
export interface IgReel {
  id: string
  code: string
  thumbnail_url: string
  video_url: string
  play_count: number
  like_count: number
  comment_count: number
  caption: {
    text: string
  }
  created_at: number
  duration: number
  user_id: string
  username: string
  hashtags?: string[]
  is_pinned?: boolean
  music_info?: {
    title: string
    artist: string
    audio_url?: string
  }
}

export interface IgReelsResponse {
  count: number
  total: number
  pagination_token?: string
  reels: IgReel[]
}

export function igReel(obj: any): IgReel {
  return {
    id: obj.id || '',
    code: obj.code || '',
    thumbnail_url: obj.thumbnail_url || obj.image_versions?.items?.[0]?.url || '',
    video_url: obj.video_url || obj.video_versions?.[0]?.url || '',
    play_count: obj.play_count || obj.ig_play_count || 0,
    like_count: obj.like_count || 0,
    comment_count: obj.comment_count || 0,
    caption: {
      text: obj.caption?.text || '',
    },
    is_pinned: obj.is_pinned || false,
    created_at: obj.taken_at || 0,
    duration: obj.video_duration || 0,
    user_id: obj.user?.id || '',
    username: obj.user?.username || '',
    hashtags: obj.caption?.hashtags || [],
    music_info: obj.clips_metadata?.music_info
      ? {
          title: obj.clips_metadata.music_info.music_asset_info?.title || '',
          artist: obj.clips_metadata.music_info.music_asset_info?.display_artist || '',
          audio_url: obj.clips_metadata.music_info.music_asset_info?.progressive_download_url || '',
        }
      : undefined,
  }
}

// Posts相关的数据结构（详细的帖子信息）
export interface IgPostDetail {
  id: string
  code: string
  media_type: number // 1: 图片, 2: 视频, 8: 轮播图
  media_name: string // 'post', 'reel', 等
  thumbnail_url: string
  image_versions?: {
    items: Array<{
      height: number
      width: number
      url: string
    }>
  }
  video_url?: string
  like_count: number
  comment_count: number
  caption: {
    text: string
    hashtags?: string[]
    mentions?: string[]
  }
  created_at: number
  location?: {
    id: string
    name: string
    address?: string
    lat?: number
    lng?: number
  }
  user_id: string
  username: string
  is_video: boolean
  tagged_users?: Array<{
    username: string
    full_name: string
    id: string
    profile_pic_url: string
  }>
  is_pinned?: boolean
  music_info?: {
    title: string
    artist: string
    audio_url?: string
  }
}

export interface IgPostsResponse {
  count: number
  total: number
  pagination_token?: string
  posts: IgPostDetail[]
}

export function igPostDetail(obj: any): IgPostDetail {
  return {
    id: obj.id || '',
    code: obj.code || '',
    media_type: obj.media_type || 1,
    media_name: obj.media_name || 'post',
    thumbnail_url: obj.thumbnail_url || obj.image_versions?.items?.[0]?.url || '',
    image_versions: obj.image_versions
      ? {
          items: obj.image_versions.items.map((item: any) => ({
            height: item.height || 0,
            width: item.width || 0,
            url: item.url || '',
          })),
        }
      : undefined,
    video_url: obj.video_url || obj.video_versions?.[0]?.url || '',
    like_count: obj.like_count || 0,
    comment_count: obj.comment_count || 0,
    caption: {
      text: obj.caption?.text || '',
      hashtags: obj.caption?.hashtags || [],
      mentions: obj.caption?.mentions || [],
    },
    created_at: obj.taken_at || 0,
    location: obj.location
      ? {
          id: obj.location.id || '',
          name: obj.location.name || '',
          address: obj.location.address || '',
          lat: obj.location.lat || 0,
          lng: obj.location.lng || 0,
        }
      : undefined,
    user_id: obj.user?.id || '',
    username: obj.user?.username || '',
    is_video: obj.is_video || false,
    tagged_users: obj.tagged_users
      ? obj.tagged_users.map((user: any) => ({
          username: user.user?.username || '',
          full_name: user.user?.full_name || '',
          id: user.user?.id || '',
          profile_pic_url: user.user?.profile_pic_url || '',
        }))
      : undefined,
    is_pinned: obj.is_pinned || false,
    music_info: obj.music_metadata?.music_info
      ? {
          title: obj.music_metadata.music_info.music_asset_info?.title || '',
          artist: obj.music_metadata.music_info.music_asset_info?.display_artist || '',
          audio_url: obj.music_metadata.music_info.music_asset_info?.progressive_download_url || '',
        }
      : undefined,
  }
}

export type IgUser = {
  id: string
  username: string
  profilePicUrl: string
  fullName: string
  followerCount: number
  biography: string
  biographyEmail: string | null
  posts?: IgPost[] | null
  embedding?: number[]
  publicEmail?: string
  region?: string
  lastPublishedTime?: number
  email?: string
  emailSource?: string
  category?: string
  accountType?: number
  followingCount?: number
  isBusiness?: boolean
  isVerified?: boolean
  isPrivate?: boolean
  isAnonymousProfilePicture?: boolean
}

export type IgAboutFull = {
  about_this_account_country: string
  about_this_account_country_visibility: boolean
  user_id: string
  date_joined: string
  date_verified: string
  former_usernames_count: string
  accounts_with_shared_followers_count: string
}

// 粉丝相关的数据结构
export interface IgFollower {
  id: string
  username: string
  full_name: string
  profile_pic_url: string
  is_private: boolean
  is_verified: boolean
  latest_story_ts: number | null
}

export interface IgFollowersResponse {
  count: number
  total: number
  pagination_token?: string
  followers: IgFollower[]
}

export function igFollower(obj: any): IgFollower {
  return {
    id: obj.id || '',
    username: obj.username || '',
    full_name: obj.full_name || '',
    profile_pic_url: obj.profile_pic_url || '',
    is_private: obj.is_private || false,
    is_verified: obj.is_verified || false,
    latest_story_ts: obj.latest_story_ts || null,
  }
}

export function igUser(obj: any): IgUser | null {
  if (!obj) {
    return null
  }
  let email = undefined
  let source = undefined
  if (extractEmail(obj.public_email)) {
    email = extractEmail(obj.public_email)
    source = EmailSourceType.RAPID_EMAIL_BUTTON
  } else {
    email = extractEmail(obj.biography)
    if (email) {
      source = EmailSourceType.BIO_EMAIL
    }
  }
  return {
    id: obj.id,
    username: obj.username,
    fullName: obj.full_name,
    profilePicUrl: obj.profile_pic_url,
    followerCount: obj?.follower_count ?? 0,
    biography: obj.biography ?? '',
    biographyEmail: obj.biography_email ?? undefined,
    publicEmail: obj.public_email ?? '',
    region: obj.about?.country ? countries.getAlpha2Code(obj.about?.country, 'en') : '',
    email: email,
    emailSource: source,
    category: obj.category,
    accountType: obj.account_type,
    followingCount: obj.following_count ?? 0,
    isBusiness: obj.is_business,
    isVerified: obj.about?.is_verified ?? obj.bio_link?.is_verified ?? false,
    isPrivate: obj.is_private ?? false,
    isAnonymousProfilePicture: obj.has_anonymous_profile_picture ?? false,
  } as IgUser
}

export function igPost(obj: any): IgPost {
  return {
    id: obj.id,
    comment_count: obj.comment_count,
    like_count: obj.like_count,
    play_count: obj.play_count,
    caption: {
      text: removeEmoji(obj?.caption?.text ?? ''),
    },
    created_at: obj.taken_at,
    like_and_view_counts_disabled: obj?.like_and_view_counts_disabled,
    tags: obj?.caption?.hashtags ?? [],
    thumbnail_url: obj?.thumbnail_url ?? '',
    code: obj?.code,
    media_type: obj?.media_type,
    media_name: obj?.media_name,
    video_url: obj?.video_url,
    is_pinned: obj?.is_pinned,
  }
}

function removeEmoji(input: string) {
  return input.replace(/[\p{Emoji_Presentation}\p{Emoji}\u200d]+/gu, '')
}

// 点赞相关的数据结构
export interface IgLike {
  full_name: string
  id: string
  is_new: boolean
  is_private: boolean
  is_verified: boolean
  latest_reel_media: number
  profile_pic_id: string
  profile_pic_url: string
  username: string
}

export interface IgLikesResponse {
  count: number
  total: number
  likes: IgLike[]
}

export function igLike(obj: any): IgLike {
  return {
    full_name: obj.full_name || '',
    id: obj.id || '',
    is_new: obj.is_new || false,
    is_private: obj.is_private || false,
    is_verified: obj.is_verified || false,
    latest_reel_media: obj.latest_reel_media || 0,
    profile_pic_id: obj.profile_pic_id || '',
    profile_pic_url: obj.profile_pic_url || '',
    username: obj.username || '',
  }
}

/**
 * hashtag response
 */
export interface IgHashTagVideosResponse {
  additional_data: any
  count: number
  items: any[]
  total: number
  pagination_token?: string
}

// 关注相关的数据结构
export interface IgFollowing {
  id: string
  username: string
  full_name: string
  profile_pic_url: string
  is_private: boolean
  is_verified: boolean
  latest_reel_media: number
}

export interface IgFollowingsResponse {
  count: number
  pagination_token?: string
  followings: IgFollowing[]
}

export function igFollowing(obj: any): IgFollowing {
  return {
    id: obj.id || '',
    username: obj.username || '',
    full_name: obj.full_name || '',
    profile_pic_url: obj.profile_pic_url || '',
    is_private: obj.is_private || false,
    is_verified: obj.is_verified || false,
    latest_reel_media: obj.latest_reel_media || 0,
  }
}

// Tagged用户相关的数据结构
export interface IgTaggedUser {
  id: string
  username: string
  full_name: string
  profile_pic_url: string
  is_private: boolean
  is_verified: boolean
}

export interface IgTaggedUsersResponse {
  total: number
  count: number
  items: IgTaggedUser[]
  pagination_token?: string
}

export function igTaggedUser(obj: any): IgTaggedUser {
  const userObj = obj?.user || obj
  return {
    id: userObj.id || '',
    username: userObj.username || '',
    full_name: userObj.full_name || '',
    profile_pic_url: userObj.profile_pic_url || '',
    is_private: userObj.is_private || false,
    is_verified: userObj.is_verified || false,
  }
}
