import { newGoogleOAuth2Client, sendEmailByGmail, verifyGoogleTokens } from '@/api/gmail.ts'
import { prisma } from '@repo/database'
import { describe, expect, it } from 'vitest'

describe('gmail', () => {
  it(
    'should send email to yourself',
    async () => {
      const email = '<EMAIL>'
      const user = await prisma.userInfo.findFirst({
        where: {
          email: email,
        },
        select: {
          userId: true,
        },
      })
      expect(user).toBeTruthy()
      expect(user?.userId).toBeTruthy()
      const credential = await prisma.providerCredential.findFirst({
        where: {
          createdBy: user!.userId,
        },
      })
      expect(credential).toBeTruthy()
      expect(credential?.accessToken).toBeDefined()
      expect(credential?.refreshToken).toBeDefined()
      const gmailClinet = newGoogleOAuth2Client(credential as any)
      const info = await verifyGoogleTokens(gmailClinet)
      expect(info?.email).eq(email)
      const res = await sendEmailByGmail({
        client: gmailClinet,
        from: email,
        to: email,
        subject: '[test]easykol',
        text: 'hello',
      })
      return res.data
    },
    { timeout: 1000000 },
  )

  it(
    'should send email with CC and BCC',
    async () => {
      const email = '<EMAIL>'
      const user = await prisma.userInfo.findFirst({
        where: {
          email: email,
        },
        select: {
          userId: true,
        },
      })
      expect(user).toBeTruthy()
      expect(user?.userId).toBeTruthy()
      const credential = await prisma.providerCredential.findFirst({
        where: {
          createdBy: user!.userId,
        },
      })
      expect(credential).toBeTruthy()
      expect(credential?.accessToken).toBeDefined()
      expect(credential?.refreshToken).toBeDefined()
      const gmailClinet = newGoogleOAuth2Client(credential as any)
      const info = await verifyGoogleTokens(gmailClinet)
      expect(info?.email).eq(email)

      // 测试带有CC和BCC的邮件发送
      const res = await sendEmailByGmail({
        client: gmailClinet,
        from: email,
        to: email,
        subject: '[test]easykol with CC/BCC',
        text: 'hello with cc and bcc',
        cc: [email], // 抄送给自己
        bcc: [email], // 密送给自己
      })
      expect(res.data).toBeTruthy()
      return res.data
    },
    { timeout: 1000000 },
  )
})
