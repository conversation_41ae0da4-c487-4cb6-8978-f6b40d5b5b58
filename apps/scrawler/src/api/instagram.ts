import {
  IgHashTagVideosResponse,
  IgPost,
  IgReelsResponse,
  IgUser,
} from '@/api/@types/rapidapi/Instagram.ts'
import RapidApiStatsCollector from '@/api/ApiCallStat.ts'
import { UnimplementedError } from '@/common/errors/baseError'
import Sentry from '@/infras/sentry'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
async function downgradable(fn: string, ...args: any[]) {
  try {
    let result = undefined
    console.log(`using v3 api for ${fn}`)
    result = await (InstagramRapidApiV3.getInstance()[fn as keyof InstagramApi] as Function)(
      ...args,
    )
    // 相似用户推荐如果是空的，尝试另一个接口
    if (fn == 'getUserRelatedUsers' && result?.length == 0) {
      throw new Error('getUserRelatedUsers empty result ,try use another method')
    }
    if (fn == 'getUser' && result == null) {
      throw new Error('getUser empty result ,try use another method')
    }
    return result
  } catch (error) {
    console.error(error)
    if (!(error instanceof UnimplementedError)) {
      Sentry.captureException(error, {
        tags: {
          transaction: 'Instagram API service',
          api_version: 'v3',
          args: JSON.stringify(args),
        },
      })
    }
  }
}

class InstagramApi {
  private static instance: InstagramApi

  public static getInstance() {
    if (!InstagramApi.instance) {
      InstagramApi.instance = new InstagramApi()
    }
    return InstagramApi.instance
  }

  public async getPosts(id: string, collector?: RapidApiStatsCollector): Promise<IgPost[]> {
    return downgradable('getPosts', { username: id }, collector)
  }

  public async getUserWithPosts(
    username: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser> {
    const user = await downgradable('getUserWithPosts', { username }, collector)
    return user
  }

  public async getUser(
    username: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser | null> {
    return downgradable('getUser', { username }, collector)
  }

  public async getUserRelatedUsers(
    username: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgUser[]> {
    return downgradable('getUserRelatedUsers', { username: username }, collector)
  }

  public async getPost(
    postId: string,
    collector?: RapidApiStatsCollector,
  ): Promise<IgPost | undefined> {
    return InstagramRapidApiV3.getInstance().getPost(postId, collector)
  }

  public async getReels(
    username: string,
    options?: {
      pagination_token?: string
      url_embed_safe?: boolean
    },
    collector?: RapidApiStatsCollector,
  ): Promise<IgReelsResponse> {
    return InstagramRapidApiV3.getInstance().getReels({ username }, options, collector)
  }

  public async getHashTagVideos(
    hashtag: string,
    options?: { pagination_token?: string },
    collector?: RapidApiStatsCollector,
  ): Promise<IgHashTagVideosResponse> {
    return InstagramRapidApiV3.getInstance().getHashTagVideos(hashtag, options, collector)
  }

  public async getLikes(postId: string, collector?: RapidApiStatsCollector): Promise<number> {
    const result = await InstagramRapidApiV3.getInstance().getLikes(postId, collector)
    return result.count || 0
  }
}

export default InstagramApi
