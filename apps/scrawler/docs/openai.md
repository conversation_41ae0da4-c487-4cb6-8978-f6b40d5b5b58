# openAI 的多模型使用说明

## 前提条件

本项目接入了 aihubmix，open router，azure 多个 AI 供应商，在有些时候需要做自动的切换，需要设计一个足够灵活优雅的 AI 供应商切换策略。

## 调用方式

```typescript
const result = await chatCompletion({
  model: 'gpt-4o-mini',
  messages: [{ role: 'user', content: 'who are you?' }],
  // 可选参数 order
  order: ['openRouterApi', 'aihubmix'],
})
```

在原始的 OpenAI 库的调用方式上增加一个参数 `order`, 默认是`aihubmix` > `openrouter`>`azureOpenai`,也支持传入参数自定义顺序。

## 新增模型必需的配置

1. 在 `/apps/scrawler/src/config/models.ts` 文件中新增模型的 `AIModelCodes` 名称定义和 `AIModelMap` 映射，几个不同的供应商都要填入对应的模型名称，暂不提供的供应商可以填入 undefined，就不会被调用到了。

2. 在新的业务中使用上面的 `chatCompletion` 函数，模型名写成自己新增的就完成了。🎉

## 新增供应商必需的配置

1. 在 `/apps/scrawler/src/api/openai.ts` 添加新的供应商的定义和初始化（现在要求必须使用 OpenAI 的库实现）

2. 在 `/apps/scrawler/src/api/openai.ts` 的 `defaultOrder` 中，给新供应商填入合适的调用次序。

3. 在 `/apps/scrawler/src/api/openai.ts` 的 `AIProvider` 中，给新供应商增加定义。

4. 完成。🎉
