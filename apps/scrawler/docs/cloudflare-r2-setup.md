# Cloudflare R2 图片存储设置指南

## 概述

本系统已集成 Cloudflare R2 存储，用于自动备份 `PublicationStatisticsSheetData` 表中的图片。当创建新的发布数据时，系统会自动将外部图片链接下载并上传到您的 R2 存储中。

## 环境配置

在 `.env` 文件中添加以下配置：

```bash
# Cloudflare R2 存储配置
CLOUDFLARE_R2_ACCOUNT_ID=your-cloudflare-account-id
CLOUDFLARE_R2_ACCESS_KEY_ID=your-r2-access-key-id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-r2-secret-access-key
CLOUDFLARE_R2_BUCKET_NAME=your-r2-bucket-name
CLOUDFLARE_R2_PUBLIC_URL=https://your-custom-domain.com  # 可选，如果配置了自定义域名
```

## 获取 R2 配置信息

### 1. 创建 R2 存储桶

1. 登录 Cloudflare Dashboard
2. 进入 R2 Object Storage
3. 创建新的存储桶
4. 记录存储桶名称

### 2. 获取 API 令牌

1. 在 Cloudflare Dashboard 中，进入 "My Profile" > "API Tokens"
2. 创建自定义令牌，权限设置为：
   - Account: `Cloudflare R2:Edit`
   - Zone Resources: `Include - All zones`
3. 记录 Access Key ID 和 Secret Access Key

### 3. 获取 Account ID

1. 在 Cloudflare Dashboard 右侧边栏可以找到 Account ID
2. 复制 Account ID

## 功能说明

### 自动图片备份

- 当创建新的 `PublicationStatisticsSheetData` 记录时，系统会自动检查 `postThumbnailUrl` 字段
- 如果是有效的 HTTP 链接，系统会异步下载图片并上传到 R2
- 上传成功后，会更新记录的 `r2ImageKey` 和 `r2ImageUrl` 字段

### 数据库字段说明

- `postThumbnailUrl`: 原始外部图片链接（保留不变）
- `r2ImageKey`: R2 存储中的文件路径（如：`publication/**********-abc123.jpg`）
- `r2ImageUrl`: R2 的公开访问链接

### API 接口

#### 1. 手动上传图片到 R2

```http
POST /api/imageStorage/upload-image
Authorization: Bearer <token>
Content-Type: application/json

{
  "publicationId": "publication-id",
  "imageUrl": "https://example.com/image.jpg"  // 可选，不提供则使用数据库中的链接
}
```

#### 2. 获取 R2 图片链接

```http
GET /api/imageStorage/get-image/:publicationId
Authorization: Bearer <token>
```

## 使用建议

### 前端显示图片

建议按以下优先级显示图片：

1. 优先使用 `r2ImageUrl`（如果存在）
2. 备用 `postThumbnailUrl`

```javascript
const imageUrl = publication.r2ImageUrl || publication.postThumbnailUrl
```

### 性能优化

- R2 图片通过 Cloudflare CDN 分发，全球访问速度更快
- 减少对第三方图片服务的依赖
- 提高图片加载的可靠性

## 故障排除

### 常见问题

1. **图片上传失败**

   - 检查 R2 配置是否正确
   - 确认 API 令牌权限是否足够
   - 查看服务器日志中的错误信息

2. **图片无法访问**

   - 检查 R2 存储桶的公开访问设置
   - 确认自定义域名配置（如果使用）

3. **自动上传不工作**
   - 确认原始图片链接是有效的 HTTP/HTTPS 链接
   - 检查服务器日志中的异步处理错误

### 日志监控

系统会记录以下关键日志：

- `[ImageStorage] 开始处理图片: <url>`
- `[ImageStorage] 图片上传并更新数据库成功: <publicationId>`
- `[PublicationStatistics] 自动上传图片到 R2 失败: <publicationId>`

## 成本估算

Cloudflare R2 的定价（截至文档编写时）：

- 存储：$0.015/GB/月
- Class A 操作（写入）：$4.50/百万次请求
- Class B 操作（读取）：$0.36/百万次请求
- 出站流量：免费（通过 Cloudflare CDN）

对于大多数应用场景，R2 的成本非常低廉。
