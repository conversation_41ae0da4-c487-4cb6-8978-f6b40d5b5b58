# Cloudflare R2 存储配置
# 在 Cloudflare Dashboard -> R2 -> API tokens 中创建
CLOUDFLARE_R2_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_id_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_access_key_here
CLOUDFLARE_R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
CLOUDFLARE_R2_BUCKET_NAME=your_bucket_name
CLOUDFLARE_R2_PUBLIC_URL=https://your-custom-domain.com

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/database_name
SHADOW_DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# OpenAI 配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o-mini

# Google 配置
GOOGLE_API_KEY=your_google_api_key
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# API Keys
NAINF_KEY=your_nainf_key
RAPIDAPI_API_KEY=your_rapidapi_key
TT_RAPID_API_KEY=your_tiktok_rapid_api_key
IG_RAPID_API_KEY=your_instagram_rapid_api_key

# Supabase 配置
SUPABASE_URL=your_supabase_url
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
SUPABASE_SERVER_ROLE_KEY=your_supabase_server_role_key
SUPABASE_ANON_KEY=your_supabase_anon_key

# OSS 配置
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_ENDPOINT=your_oss_endpoint
OSS_REGION=your_oss_region
OSS_BUCKET=your_oss_bucket

# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=easykol:beta:

# Jina 配置
EASYKOL_JINA_V4_MILVUS_COLLECTION_NAME=easykol_jina_v4_beta
JINA_API_KEY=your_jina_api_key

# 其他配置
FILTER_OFFICIAL_ACCOUNT=true
OPENROUTER_API_KEY=your_openrouter_api_key
TWITTER241_API_KEY=your_twitter_api_key
CLOUDFLARE_WORKER_URL=your_cloudflare_worker_url
