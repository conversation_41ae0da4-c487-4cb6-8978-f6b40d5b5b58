name: server

on:
  push:
    paths:
      - 'apps/scrawler/**'
      # - 'apps/server/**'
      # - 'packages/database/**'
      - '.github/workflows/server.yaml'

env:
  PROJECT_NAME: talent-marketing-server

jobs:

  build:
    runs-on: arc-runner-set

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          always-auth: true
          registry-url: https://npm.midway.run
          scope: '@ruguoapp'

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        id: pnpm-install
        with:
          version: 9.7.1
          run_install: false

      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: Cache from oss
        uses: B1F030/actions-cache-aliyun@v0.1
        with:
          aws-endpoint: https://oss-cn-hangzhou-internal.aliyuncs.com
          aws-access-key-id: ${{ secrets.ALIYUN_OSS_ACCESSKEY_ID }}
          aws-secret-access-key: ${{ secrets.ALIYUN_OSS_ACCESSKEY_SECRET }}
          aws-region: oss-cn-hangzhou
          aws-s3-bucket: ${{ vars.ALIYUN_OSS_BUCKET_GITHUB_ACTION_CACHE }}
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        # run: pnpm install --filter=server...
        run: pnpm install --filter=worker...
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_AUTH_TOKEN}}

      - name: Compile
        run: pnpm run build --filter=worker...
        env:
          NODE_ENV: production

      - name: PNPM Deploy
        run: pnpm --filter=worker --prod deploy server

      - name: Copy dist
        run: cp -r apps/scrawler/dist server/

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          # list of Docker images to use as base name for tags
          images: |
            registry.jellow.site/iftech/talent-marketing-server
          # generate Docker tags based on the following events/attributes
          tags: |
            type=sha,prefix={{branch}}-

      - name: Login to Aliyun
        uses: docker/login-action@v2
        with:
          registry: registry.jellow.site
          username: ${{ secrets.ALIYUN_CR_USERNAME }}
          password: ${{ secrets.ALIYUN_CR_PASSWORD }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: image=registry.jellow.site/iftech/buildkit:buildx-stable-1

      - name: Build and push
        uses: docker/build-push-action@v4
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.ALIYUN_OSS_ACCESSKEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.ALIYUN_OSS_ACCESSKEY_SECRET }}
        with:
          push: ${{ github.event_name != 'pull_request' }}
          context: server
          file: server/Dockerfile-server
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=s3,region=oss-cn-hangzhou,bucket=iftech-github-action-cache,endpoint_url=https://oss-cn-hangzhou-internal.aliyuncs.com
          cache-to: type=s3,region=oss-cn-hangzhou,bucket=iftech-github-action-cache,endpoint_url=https://oss-cn-hangzhou-internal.aliyuncs.com
          build-args: |
            NPM_TOKEN=${{ secrets.NPM_AUTH_TOKEN }}
          # labels: |
          #   org.opencontainers.image.source=${{ github.event.repository.clone_url }}
          #   org.opencontainers.image.created=${{ steps.prep.outputs.created }}
          #   org.opencontainers.image.revision=${{ github.sha }}
      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}
      - name: Notify JKDPY
        run: |
          curl ${{ secrets.JKDPY_WEBHOOK_URL }} \
            -u ${{ secrets.JKDPY_WEBHOOK_USERNAME }}:${{ secrets.JKDPY_WEBHOOK_PASSWORD }} \
            -d 'image_name=${{ fromJSON(steps.meta.outputs.json).tags[0] }}&branch_name=${{ github.ref_name }}&service_name=${{ env.PROJECT_NAME }}&github_url=${{ github.server_url }}/${{ github.repository }}&commit_message="${{ github.event.head_commit.message }}"'
