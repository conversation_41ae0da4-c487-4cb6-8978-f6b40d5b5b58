generator client {
  provider        = "prisma-client-js"
  output          = "../../generated/client"
  previewFeatures = ["prismaSchemaFolder", "omitApi", "tracing", "postgresqlExtensions"]
  binaryTargets   = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider          = "postgresql"
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  relationMode      = "prisma"
  extensions        = [pgvector(map: "vector", schema: "public")]
}

model UserInfo {
  userId                String                     @id
  email                 String?
  phoneNumber           String?
  avatar                String?
  supabase              Json?
  createdAt             DateTime                   @default(now())
  updatedAt             DateTime                   @updatedAt
  ProviderCredential    ProviderCredential[]
  membership            UserMembership?
  UserSignatureLog      UserSignatureLog[]
  userSubmitEmails      UserSubmitEmail[]
  UserGoogleSheet       UserGoogleSheet[]
  ExcludeListRecord     ExcludeListRecord[]
  KolRelationRecord     KolRelationRecord[]
  ProjectKol            ProjectKol[]
  UserPluginVersion     UserPluginVersion[]
  EasykolTrackStrategy  EasykolTrackStrategy[]
  trackStrategyTemplate UserTrackStrategyTemplate?
  stripeCustomer        StripeCustomer?
  stripeSubscriptions   StripeSubscription[]
  KolEmailAuditLog      KolEmailAuditLog[]
  CreatorTag            CreatorTag[]
  KolTag                KolTag[]
  KolNote               KolNote[]
  KolRecord             KolRecord[]
  KolRecordLog          KolRecordLog[]
  BatchTaskRecord       BatchTaskRecord[]
  UserAuditLog          UserAuditLog[]
  EmailFollowup         EmailFollowup[]
  EmailPlan             EmailPlan[]
}

model KolInfo {
  id               String                       @id @default(cuid())
  title            String?
  description      String?
  email            String?
  emailSource      String?                      @default("")
  historyEmails    String[]                     @default([])
  platformAccount  String?
  platform         KolPlatform                  @map("source")
  createdAt        DateTime                     @default(now())
  updatedAt        DateTime                     @updatedAt
  avatar           String?                      @default("")
  embedding        Unsupported("vector(3072)")?
  links            String[]                     @default([])
  contacts         Json?
  emailUpdatedAt   DateTime?                    @default(now())
  audienceAnalysis Json?                        @default("{}")

  ProjectKol       ProjectKol[]
  userSubmitEmails UserSubmitEmail[]

  youtubeChannel                 YouTubeChannel?                  @relation(fields: [platformAccount], references: [channelId], map: "youtube_fkey", onDelete: Cascade, onUpdate: Cascade)
  tiktokUser                     TikTokUserInfo?                  @relation(fields: [platformAccount], references: [uniqueId], map: "tiktok_fkey", onDelete: Cascade, onUpdate: Cascade)
  instagramUser                  InstagramUserInfo?               @relation(fields: [platformAccount], references: [username], map: "instagram_fkey", onDelete: Cascade, onUpdate: Cascade)
  twitterUser                    TwitterUser?                     @relation(fields: [platformAccount], references: [restId], map: "twitter_fkey", onDelete: Cascade, onUpdate: Cascade)
  ExcludeList                    ExcludeList[]
  KolRelationRecord              KolRelationRecord[]
  PublicationStatisticsSheetData PublicationStatisticsSheetData[]
  KolEmailAuditLog               KolEmailAuditLog[]
  KolTag                         KolTag[]
  KolNote                        KolNote[]
  KolRecord                      KolRecord[]
  EmailFollowupThread            EmailFollowupThread[]

  @@unique([platform, platformAccount])
  @@index([platformAccount])
  @@index([email])
  @@index([platform])
}

model Project {
  id          String    @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  candidates  Json?
  config      Json?

  EmailTemplate      EmailTemplate[]
  ProjectKol         ProjectKol[]
  ProjectMembership  ProjectMembership[]
  SimilarChannelTask SimilarChannelTask[]
  QuotaLogs          QuotaLog[]
  ProjectCandidate   ProjectCandidate[]
}

model ProjectMembership {
  id        String                @id @default(cuid())
  projectId String
  userId    String
  role      ProjectMembershipRole
  createdAt DateTime              @default(now())
  updatedAt DateTime              @updatedAt
  Project   Project               @relation(fields: [projectId], references: [id])
}

model EmailTemplate {
  id                 String            @id @default(cuid())
  name               String?
  subject            String
  content            String
  renderType         EmailTemplateType @default(PLAIN)
  tags               String[]          @default([])
  createdBy          String
  belongsToProjectId String?           @map("belongsToProductId")
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  deletedAt          DateTime?
  cc                 String[]          @default([])
  bcc                String[]          @default([])
  belongsToProject   Project?          @relation(fields: [belongsToProjectId], references: [id])
  EmailRecord        EmailRecord[]
  EmailFollowup      EmailFollowup[]
}

model ProjectKol {
  id            String             @id @default(cuid())
  attitude      ProjectKolAttitude
  rateBy        String
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  lastSimilarAt DateTime?
  kolId         String             @map("kolId")
  projectId     String             @map("projectId")
  similarTaskId String             @map("similarTaskId")

  Project            Project            @relation(fields: [projectId], references: [id])
  KolInfo            KolInfo?           @relation(fields: [kolId], references: [id])
  SimilarChannelTask SimilarChannelTask @relation(fields: [similarTaskId], references: [id])
  EmailRecord        EmailRecord[]
  user               UserInfo           @relation(fields: [rateBy], references: [userId])

  @@unique([projectId, kolId])
  @@index([projectId, similarTaskId, kolId, attitude, lastSimilarAt])
}

model EmailRecord {
  id           String        @id @default(cuid())
  templateId   String
  type         SentEmailType
  status       SendStatus
  from         String
  to           String
  subject      String
  content      String
  cc           String[]      @default([])
  bcc          String[]      @default([])
  tags         String[]
  score        Decimal       @default(0)
  resultObj    Json?
  projectKolId String        @map("ProductChannelId")
  sentAt       DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  ProjectKol    ProjectKol    @relation(fields: [projectKolId], references: [id])
  EmailTemplate EmailTemplate @relation(fields: [templateId], references: [id])
  EmailPlan     EmailPlan[]
}

model Provider {
  id                 String               @id @default(cuid())
  type               ProviderType
  key                String               @unique
  clientId           String
  clientSecret       String
  scopes             ScopeType[]
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  ProviderCredential ProviderCredential[]
}

model ProviderCredential {
  id              String        @id @default(cuid())
  providerId      String
  accessToken     String?
  refreshToken    String?
  expiresAt       DateTime?
  createdBy       String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  EmailCredential EmailSender[]
  Provider        Provider      @relation(fields: [providerId], references: [id])
  CreatedUser     UserInfo?     @relation(fields: [createdBy], references: [userId])

  @@unique([providerId, createdBy])
}

model EmailSender {
  id                   String              @id @default(cuid())
  email                String
  type                 EmailCredentialType
  providerCredentialId String?
  createdBy            String
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  providerCredential   ProviderCredential? @relation(fields: [providerCredentialId], references: [id])

  @@unique([email, createdBy])
}

model SimilarChannelTask {
  id           String                   @id @default(cuid())
  projectId    String                   @default("0")
  status       SimilarChannelTaskStatus
  params       Json
  result       Json?
  errors       Json?
  meta         Json?
  createdBy    String?
  createdAt    DateTime                 @default(now())
  updatedAt    DateTime                 @updatedAt
  isTerminated Boolean                  @default(false)
  reason       TaskReason               @default(LIKE)
  type         TaskType                 @default(SIMILAR)
  strategyId   String? // 关联的策略ID
  project      Project                  @relation(fields: [projectId], references: [id])
  strategy     EasykolTrackStrategy?    @relation(fields: [strategyId], references: [id])
  candidate    Json?                    @default("{}")
  ProjectKols  ProjectKol[]
  QuotaLogs    QuotaLog[]

  @@index([projectId])
  @@index([createdBy])
  @@index([status])
  @@index([type])
  @@index([strategyId])
}

model YouTubeChannel {
  channelId              String   @id
  channelName            String
  channelHandle          String   @default("")
  channelDescription     String   @default("")
  numericSubscriberCount BigInt   @default(0)
  country                String?
  haveCrawlered          Boolean  @default(false)
  videosAverageViewCount BigInt
  officialEmail          Boolean  @default(false)
  createdAt              DateTime @default(now())
  updatedAt              DateTime @updatedAt
  lastPublishedTime      BigInt
  videos                 Json?    @default("[]")
  publicationStats       Json?    @default("{}")

  kols KolInfo[]

  @@index([lastPublishedTime])
  @@index([videosAverageViewCount])
  @@index([numericSubscriberCount])
  @@index([country])
}

model TikTokUserInfo {
  userId            String   @id
  uniqueId          String   @unique
  nickname          String
  region            String   @default("")
  lastPublishedTime BigInt
  haveCrawlered     Boolean  @default(false)
  followerCount     BigInt   @default(0)
  averagePlayCount  BigInt
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  avatar            String
  videos            Json?    @default("[]")
  publicationStats  Json?    @default("{}")

  kols KolInfo[]

  @@index([uniqueId])
  @@index([userId])
}

model InstagramUserInfo {
  id                  String   @id
  username            String   @unique
  profilePicUrl       String   @default("")
  fullName            String   @default("")
  followerCount       Int      @default(0)
  averageLikeCount    Int      @default(0)
  averageCommentCount Int      @default(0)
  lastPublishedTime   BigInt   @default(0)
  region              String   @default("")
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now())
  posts               Json?    @default("[]")
  accountInfo         Json?    @default("{}") // 账号信息
  publicationStats    Json?    @default("{}")

  kols KolInfo[]

  @@index([id])
  @@index([username])
}

model TwitterUser {
  id         String @id @default(cuid()) // 建议使用cuid()或uuid()作为主键
  restId     String @unique // 用户的数字ID, 非常重要
  screenName String @unique // 用户的@句柄, 可能会变, 但常用
  rawData    Json   @default("{}") // 完整原始JSON数据，用于兜底和未来分析

  name             String? // 显示名称, e.g., "Snoop Dogg"
  description      String? // 个人简介
  location         String? // 地理位置
  profileImageUrl  String? // 头像URL
  profileBannerUrl String? // 主页横幅URL
  url              String? // 个人网站链接
  isProtected      Boolean @default(false) // 是否是私密账户
  isBlueVerified   Boolean @default(false) // 是否蓝V认证

  followersCount Int @default(0) // 粉丝数
  friendsCount   Int @default(0) // 关注数
  statusesCount  Int @default(0) // 推文总数
  mediaCount     Int @default(0) // 媒体推文数
  listedCount    Int @default(0) // 被加入列表次数

  accountCreatedAt DateTime? // 用户的推特账号创建时间
  createdAt        DateTime  @default(now()) // 记录在我的数据库中创建的时间
  updatedAt        DateTime  @updatedAt // 记录在我的数据库中更新的时间

  // --- 关系 (保持不变) ---
  kols KolInfo[]

  // --- 索引 (保持不变，非常重要) ---
  @@index([restId])
  @@index([screenName])
}

model SystemNotify {
  id        String               @id
  content   String               @default("")
  position  SystemNotifyPosition @default(SIDEPANEL)
  valid     Boolean              @default(true)
  createdAt DateTime             @default(now())
  updatedAt DateTime             @default(now())
}

model UserSignatureLog {
  id          String   @id @default(cuid())
  userId      String   @default("")
  browserId   String   @default("")
  extensionId String   @default("")
  version     String   @default("")
  createdAt   DateTime @default(now())

  user UserInfo? @relation(fields: [userId], references: [userId])

  @@index([extensionId])
  @@index([userId])
}

model UserSubmitEmail {
  id        String            @id @default(cuid())
  userId    String            @default("")
  kolId     String            @default("")
  email     String            @default("")
  status    EmailVerifyStatus @default(PENDING)
  createdAt DateTime          @default(now())

  user UserInfo? @relation(fields: [userId], references: [userId])
  kol  KolInfo?  @relation(fields: [kolId], references: [id])
}

model ExcludeListRecord {
  id           String                  @id @default(cuid())
  userId       String                  @default("")
  enterpriseId String                  @default("")
  rawContent   String                  @default("")
  result       Json?
  status       ExcludeListRecordStatus @default(PROCESSING)
  createdAt    DateTime                @default(now())

  user       UserInfo?   @relation(fields: [userId], references: [userId])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])
}

model ExcludeList {
  id              String      @id @default(cuid())
  kolId           String      @default("")
  platform        KolPlatform
  platformAccount String
  userId          String
  enterpriseId    String?
  createdAt       DateTime    @default(now())

  kol        KolInfo?    @relation(fields: [kolId], references: [id])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])

  @@index([enterpriseId])
  @@index([userId])
}

model Enterprise {
  id            String  @id @default(cuid()) // 企业ID
  name          String // 企业名称
  contactPerson String? @default("") // 联系人
  contactPhone  String? @default("") // 联系电话
  contactEmail  String? @default("") // 联系邮箱
  address       String? @default("") // 企业地址
  industry      String? @default("") // 所属行业
  scale         String? @default("") // 企业规模

  accountQuota          Int              @default(0) // 企业总配额
  usedQuota             Int              @default(0) // 已使用配额
  dailyUsage            Int              @default(0) // 日使用量
  dailyLimit            Int              @default(10000) // 每日总上限
  memberUsageDailyLimit Int              @default(1000) // 企业内单个成员的每日使用额度上限
  allowInfoCardAccess   Boolean          @default(true) // 允许企业成员访问信息卡功能
  description           String?          @default("") // 企业描述
  status                EnterpriseStatus @default(ACTIVE) // 企业状态
  effectiveAt           DateTime         @default(now()) // 生效时间
  expireAt              DateTime // 过期时间
  createdAt             DateTime         @default(now()) // 创建时间
  updatedAt             DateTime         @updatedAt // 更新时间

  members           UserMembership[] // 企业成员关联
  quotaLogs         QuotaLog[] // 配额日志
  ExcludeListRecord ExcludeListRecord[]
  ExcludeList       ExcludeList[]
  KolRelationRecord KolRelationRecord[]
  CreatorTag        CreatorTag[]
  KolTag            KolTag[]
  KolNote           KolNote[]
}

model UserMembership {
  id           String       @id @default(cuid()) // 会员ID
  userId       String       @unique // 用户ID
  type         MemberType   @default(FREE) // 会员类型
  timezone     String       @default("Asia/Shanghai") // 时区
  effectiveAt  DateTime // 会员生效时间
  expireAt     DateTime // 会员过期时间
  accountQuota Int          @default(30) // 账户总配额
  usedQuota    Int          @default(0) // 已使用配额
  dailyUsage   Int          @default(0) // 日使用量
  status       MemberStatus @default(ACTIVE) // 会员状态
  lastResetAt  DateTime     @default(now()) // 上次重置时间,用于处理配额每日使用量
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  enterpriseId                    String? @default("") // 所属企业ID
  isEnterpriseAdmin               Boolean @default(false) // 是否是企业管理员
  enterpriseQuotaDailyLimit       Int     @default(1000) // 企业个人每日配额限制
  enableEnterpriseQuotaDailyLimit Boolean @default(false) // 是否开启企业配额每日限制

  cardSubscriptionEffectiveAt DateTime? // 信息卡包月开始时间
  cardSubscriptionExpireAt    DateTime? // 信息卡包月结束时间
  cardSubscriptionStatus      CardSubscriptionStatus @default(INACTIVE) // 信息卡包月状态

  user       UserInfo    @relation(fields: [userId], references: [userId])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])
  quotaLogs  QuotaLog[]

  @@index([type])
  @@index([expireAt])
  @@index([userId, status])
  @@index([enterpriseId])
  @@map("UserMemberships")
}

model QuotaLog {
  id           String    @id @default(cuid()) // 配额日志ID
  userId       String // 用户ID
  membershipId String // 会员ID
  projectId    String? // 项目ID
  taskId       String? // 任务ID
  kolId        String? // 达人ID
  usage        Int // 使用量
  type         QuotaType // 配额变更类型
  description  String? // 描述
  metadata     Json? // 元数据
  createdAt    DateTime  @default(now()) // 创建时间
  createdBy    String // 操作人
  enterpriseId String?   @default("") // 企业ID

  membership UserMembership      @relation(fields: [membershipId], references: [id])
  task       SimilarChannelTask? @relation(fields: [taskId], references: [id])
  project    Project?            @relation(fields: [projectId], references: [id])
  enterprise Enterprise?         @relation(fields: [enterpriseId], references: [id])

  @@index([userId])
  @@index([type])
  @@index([createdAt])
  @@index([membershipId, createdAt])
  @@index([userId, createdAt])
  @@map("QuotaLogs")
}

model RapidApiDailyStat {
  id           String         @id @default(cuid())
  date         DateTime       @default(now())
  platform     String
  status       RapidApiStatus
  endpoint     String
  responseCode Int            @default(1000)
  count        Int            @default(0)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt

  @@unique([date, endpoint, status, responseCode])
  @@index([date])
  @@index([endpoint])
  @@index([status])
  @@index([platform])
  @@index([responseCode])
}

model UserGoogleSheet {
  id            String      @id @default(cuid())
  userId        String      @unique // 关联到 UserInfo
  spreadsheetId String      @unique // Google Sheet 的唯一标识
  sheetUrl      String      @default("") // Google Sheet 的可访问链接
  title         String      @default("easykol-sheet") // Sheet 标题
  status        SheetStatus @default(ACTIVE)
  lastSyncAt    DateTime? // 最后同步时间
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  user                           UserInfo                         @relation(fields: [userId], references: [userId])
  publicationStatisticsSheetData PublicationStatisticsSheetData[] // 新增关联

  @@index([userId])
  @@index([spreadsheetId])
}

model PublicationStatisticsSheetData {
  id                 String       @id @default(cuid())
  spreadsheetId      String
  videoId            String?
  postType           String?
  authorUniqueId     String?
  authorUserId       String?
  authorAvatar       String?
  region             String?
  nickName           String?      @default("")
  contactInformation String?      @default("")
  notes1             String?      @default("")
  notes2             String?      @default("")
  publishDate        DateTime?
  influencer         String?
  followers          Int?
  postLink           String?
  postThumbnailUrl   String?
  views              Int?
  likes              Int?
  comments           Int?
  favorites          Int?
  shares             Int?
  totalEngagement    Int?
  engagementRate     Float?
  totalCost          Float?
  cpm                Float?
  countryData        Json?
  kolId              String?
  platform           KolPlatform?

  // Cloudflare R2 存储字段
  r2ImageKey         String?      // R2 存储的文件 key
  r2ImageUrl         String?      // R2 存储的公开访问 URL

  easykolTrackStrategyId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userGoogleSheet      UserGoogleSheet       @relation(fields: [spreadsheetId], references: [spreadsheetId])
  easykolTrackStrategy EasykolTrackStrategy? @relation(fields: [easykolTrackStrategyId], references: [id], onDelete: SetNull)
  kol                  KolInfo?              @relation(fields: [kolId], references: [id])
  tags                 PublicationTag[]

  @@index([spreadsheetId])
  @@index([publishDate])
  @@index([videoId])
}

model UserTrackStrategyTemplate {
  id            String   @id @default(cuid())
  userId        String   @unique
  name          String?
  totalRuns     Int
  intervalHours Int
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  user UserInfo @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@index([userId])
}

model EasykolTrackStrategy {
  id     String @id @default(cuid())
  userId String

  name          String? // 任务名称
  totalRuns     Int // 总运行次数
  remainingRuns Int // 剩余运行次数
  intervalHours Int // 间隔时间
  nextRunAt     DateTime // 下次运行时间
  isActive      Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  sheetDataEntries PublicationStatisticsSheetData[]
  tasks            SimilarChannelTask[]
  userInfo         UserInfo?                        @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@index([userId, isActive])
  @@index([isActive, nextRunAt])
}

model Tag {
  id           String           @id @default(cuid())
  name         String
  color        String           @default("#EAD094")
  createdBy    String
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  publications PublicationTag[]

  @@unique([name])
}

model PublicationTag {
  id            String   @id @default(cuid())
  publicationId String
  tagId         String
  createdAt     DateTime @default(now())

  publication PublicationStatisticsSheetData @relation(fields: [publicationId], references: [id], onDelete: Cascade)
  tag         Tag                            @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([publicationId, tagId])
  @@index([publicationId])
  @@index([tagId])
}

model KolRelationRecord {
  id              String          @id @default(cuid())
  kolId           String          @default("")
  platform        KolPlatform
  platformAccount String
  userId          String
  enterpriseId    String
  type            KolRelationType
  extra           Json?
  date            DateTime        @default(now()) //实际发生时间
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @default(now())

  kol        KolInfo?    @relation(fields: [kolId], references: [id])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])
  user       UserInfo    @relation(fields: [userId], references: [userId])
}

model UserPluginVersion {
  id        String   @id @default(cuid())
  userId    String   @unique
  version   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user UserInfo @relation(fields: [userId], references: [userId])

  @@index([userId])
}

model ProjectCandidate {
  id         String   @id @default(cuid())
  projectId  String
  type       TaskType
  candidates Json?    @default("{}")
  meta       Json?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  project Project @relation(fields: [projectId], references: [id])

  @@unique([projectId, type])
  @@index([projectId])
  @@index([type])
}

model AudienceAnalysis {
  id           String      @id @default(cuid())
  usernameOrId String
  platform     KolPlatform
  postId       String      @default("")
  city         String      @default("")
  region       String      @default("") // countryCode
  location     Json?
  taskId       String
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@index([usernameOrId])
  @@index([platform])
  @@index([taskId])
}

model KolEmailAuditLog {
  id          String   @id @default(cuid())
  email       String
  oldEmail    String?
  userId      String
  kolId       String
  emailSource String
  createdAt   DateTime @default(now())

  user UserInfo @relation(fields: [userId], references: [userId])
  kol  KolInfo  @relation(fields: [kolId], references: [id])

  @@index([userId])
  @@index([kolId])
}

// 博主的标签信息
model CreatorTag {
  id           String    @id @default(cuid())
  userId       String
  enterpriseId String?
  name         String
  color        String    @default("#EAD094")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  user       UserInfo    @relation(fields: [userId], references: [userId])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])
  KolTag     KolTag[]

  @@index([userId])
  @@index([enterpriseId])
  @@index([name])
}

model KolRecord {
  id           String  @id @default(cuid())
  kolId        String
  userId       String?
  enterpriseId String?

  ownerId   String
  ownerType String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  updatedBy String?
  deletedAt DateTime?

  kol   KolInfo?       @relation(fields: [kolId], references: [id])
  tags  KolTag[]
  notes KolNote[]
  user  UserInfo?      @relation(fields: [updatedBy], references: [userId])
  logs  KolRecordLog[]

  @@unique([ownerId, kolId])
  @@index([kolId])
  @@index([userId])
  @@index([enterpriseId])
}

// 博主打标信息
model KolTag {
  id           String    @id @default(cuid())
  recordId     String
  kolId        String
  tagId        String
  userId       String
  enterpriseId String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  deletedAt    DateTime?

  record     KolRecord   @relation(fields: [recordId], references: [id], onDelete: Cascade)
  kol        KolInfo?    @relation(fields: [kolId], references: [id])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])
  user       UserInfo    @relation(fields: [userId], references: [userId])
  tag        CreatorTag  @relation(fields: [tagId], references: [id])

  @@index([kolId])
  @@index([userId])
  @@index([enterpriseId])
  @@index([tagId])
}

model KolNote {
  id           String   @id @default(cuid())
  recordId     String
  kolId        String
  userId       String
  enterpriseId String?
  note         String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  kol        KolInfo?    @relation(fields: [kolId], references: [id])
  enterprise Enterprise? @relation(fields: [enterpriseId], references: [id])
  user       UserInfo    @relation(fields: [userId], references: [userId])
  record     KolRecord   @relation(fields: [recordId], references: [id], onDelete: Cascade)

  @@index([kolId])
  @@index([userId])
  @@index([enterpriseId])
}

model KolRecordLog {
  id           String           @id @default(cuid())
  kolId        String
  tagId        String?
  recordId     String?
  tagName      String? // 备份一下 tag 的名称
  note         String? // 备份一下 note
  type         TagAndNoteOpType
  userId       String
  enterpriseId String?
  description  String?
  createdAt    DateTime         @default(now())

  record KolRecord? @relation(fields: [recordId], references: [id], onDelete: Cascade)
  user   UserInfo?  @relation(fields: [userId], references: [userId])

  @@index([kolId])
  @@index([userId])
  @@index([enterpriseId])
  @@index([tagName])
}

model FeishuShortcutApiKey {
  id             String                     @id @default(cuid())
  apiKey         String                     @unique
  quota          Int                        @default(0)
  remainingQuota Int                        @default(0)
  status         FeishuShortcutApiKeyStatus @default(ACTIVE)
  createdAt      DateTime                   @default(now())
  updatedAt      DateTime                   @default(now()) @updatedAt

  @@index([apiKey])
}

model ApiDailyUsage {
  id             String   @id @default(cuid())
  date           String
  apiCode        String
  usageQuota     Int      @default(0)
  remainingQuota Int      @default(0)
  cost           Float    @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([apiCode, date])
  @@index([date])
  @@index([apiCode])
}

model UserAuditLog {
  id           String           @id @default(cuid())
  userId       String
  enterpriseId String
  type         UserAuditLogType
  createdAt    DateTime         @default(now())

  user UserInfo @relation(fields: [userId], references: [userId])
}

model EmailFollowup {
  id              String   @id @default(cuid())
  userId          String
  emailTemplateId String   @unique
  followupsEmails Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user                UserInfo              @relation(fields: [userId], references: [userId])
  emailTemplate       EmailTemplate         @relation(fields: [emailTemplateId], references: [id])
  EmailFollowupThread EmailFollowupThread[]

  @@index([userId])
  @@index([emailTemplateId])
}

model EmailFollowupThread {
  id             String                    @id @default(cuid())
  kolId          String
  kolEmail       String
  userId         String
  userEmail      String
  followupId     String
  followupEmails Json?
  threadId       String
  status         EmailFollowupThreadStatus @default(PENDING)
  createdAt      DateTime                  @default(now())
  updatedAt      DateTime                  @updatedAt

  emailFollowup EmailFollowup @relation(fields: [followupId], references: [id])
  plans         EmailPlan[]
  kol           KolInfo?      @relation(fields: [kolId], references: [id])

  @@index([followupId])
  @@index([kolId])
  @@index([kolEmail])
  @@index([threadId])
}

model EmailPlan {
  id        String          @id @default(cuid())
  userId    String
  from      String
  to        String
  subject   String
  content   String
  cc        String[]        @default([])
  bcc       String[]        @default([])
  threadId  String
  recordId  String?
  status    EmailPlanStatus @default(PENDING)
  planTime  DateTime?
  sendTime  DateTime?
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  user   UserInfo            @relation(fields: [userId], references: [userId])
  thread EmailFollowupThread @relation(fields: [threadId], references: [id])
  record EmailRecord?        @relation(fields: [recordId], references: [id])

  @@index([userId])
  @@index([threadId])
}

enum EmailVerifyStatus {
  PENDING
  APPROVED
  REJECTED
}

enum KolPlatform {
  YOUTUBE
  TIKTOK
  INSTAGRAM
  DOUYIN
  XHS
  TWITTER
}

enum ProjectMembershipRole {
  OWNER
  MEMBER
}

enum EmailTemplateType {
  PLAIN
  MJML
}

enum ProjectKolAttitude {
  LIKE
  DISLIKE
  NORATE
  SUPERLIKE
}

enum SentEmailType {
  SAY_HI
  FOLLOW_UP
}

enum SendStatus {
  DRAFT
  PENDING
  SENT
  FAILED
}

enum TaskReason {
  SEARCH
  LIKE
  SUPERLIKE
  NEXT_PAGE
  AUDIENCE_ANALYSIS
  EASYKOL_TRACK
  AUDIENCE_FAKE
  POST_AUDIENCE
}

enum ProviderType {
  GOOGLE
}

enum ScopeType {
  GMAIL
}

enum EmailCredentialType {
  GMAIL
}

enum SimilarChannelTaskStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  RESULT_READY
  PAUSED
  PAUSING
  COMPLETING
}

enum TaskType {
  KEYWORD // 关键词搜索
  SIMILAR // 相似频道搜索
  HASH_TAG_BREAK // youtube tiktok hashTag的爆破任务
  SEARCH_INPUT_BREAK //  搜索词爆破
  FOLLOWERS_SIMILAR // 粉丝列表获取相似用户
  FOLLOWING_LIST // 关注列表获取相似用户
  AUDIENCE_ANALYSIS // 受众分析
  EASYKOL_TRACK // 数据跟踪
  BGM_BREAK // 音乐爆破
  WEB_LIST // 网页列表
  TAGGED_BREAK // 被tag爆破
  POST_AUDIENCE // 帖子受众分析
  LONG_CRAWLER // 长时间运行的自动爬取任务
  AUDIENCE_FAKE // 假受众分析
}

enum QuotaType {
  SIMILAR_SEARCH @map("similar_search") // 相似搜索 -10
  AI_SEARCH      @map("ai_search") // AI搜索 -10
  CARD_QUERY     @map("card_query") // 卡片查询 -1
  RESET          @map("reset") // 系统重置配额
  ADMIN          @map("admin") // 管理员操作
  EXPIRE         @map("expire") // 会员时间过期  
  FAILED_TASK    @map("failed_task") // 任务失败返还 +10
  FAILED_SEARCH  @map("failed_search") // 字段搜索失败返还 +10

  TIKTOK_AUTHOR_COMMENTS_ANALYSIS @map("tiktok_author_comments_analysis") // 抖音作者评论分析 -5
  EASYKOL_DATA_TRACK_URL          @map("easykol_data_track_url") // 数据跟踪 -1
  EXCLUDE_LIST                    @map("exclude_list") // 去重列表 -0.1
  AUDIENCE_ANALYSIS               @map("audience_analysis") // 受众分析 -10

  // TIKTOK
  TT_FOLLOWERS_SIMILAR  @map("tt_followers_similar") // 粉丝列表获取相似用户 -10
  TT_FOLLOWING_LIST     @map("tt_following_list") // 关注列表获取相似用户 -10
  TT_HASH_TAG_BREAK     @map("tt_hash_tag_break") // tiktok 爆破 -10
  TT_SEARCH_KEYWORDS    @map("tt_search_keywords") // tiktok 搜索关键词 -10
  TT_SEARCH_INPUT_BREAK @map("tt_search_input_break") // tiktok 搜索词爆破 -10
  TT_BGM_BREAK          @map("tt_bgm_break") // tiktok 音乐爆破 -10
  TT_WEB_LIST           @map("tt_web_list") // tiktok 网页列表 -10

  // INSTAGRAM
  INS_HASH_TAG_BREAK           @map("ins_hash_tag_break") // instagram 爆破 -10
  INS_FOLLOWING_LIST           @map("ins_following_list") // instagram 关注列表 -10
  INS_WEB_LIST                 @map("ins_web_list") // instagram 网页列表 -10
  INS_TAGGED_BREAK             @map("ins_tagged_break") // instagram 被tag爆破 -10
  INS_AUTHOR_COMMENTS_ANALYSIS @map("ins_author_comments_analysis") // instagram 作者评论分析 -10
  LONG_CRAWLER                 @map("long_crawler") // 长时间运行的自动爬取任务 -10

  // YOUTUBE
  YOUTUBE_AUTHOR_COMMENTS_ANALYSIS @map("youtube_author_comments_analysis") // youtube 作者评论分析 -10
  YOUTUBE_HASHTAG_BREAK            @map("youtube_hashtag_break") // youtube hashtag 爆破 -10*个数
  YOUTUBE_SEARCH_INPUT_BREAK       @map("youtube_search_input_break") // youtube 搜索词爆破 -10*个数

  // POST AUDIENCE
  POST_AUDIENCE @map("post_audience") // 帖子受众分析 -40
  AUDIENCE_FAKE @map("audience_fake") // 假受众分析 -40
}

enum MemberType {
  FREE       @map("free") // 免费用户
  PAID       @map("paid") // 付费会员
  ENTERPRISE @map("enterprise") // 企业会员
}

enum MemberStatus {
  ACTIVE     @map("active") // 账户状态-正常
  SUSPENDED  @map("suspended") // 账户状态-已停用
  DEVICE_BAN @map("device_ban") // 设备限制
}

enum EnterpriseStatus {
  ACTIVE    @map("active") // 企业状态-正常
  SUSPENDED @map("suspended") // 企业状态-停用
  EXPIRED   @map("expired") // 企业状态-过期
}

enum CardSubscriptionStatus {
  ACTIVE   @map("active") // 包月有效
  INACTIVE @map("inactive") // 未开通包月
  EXPIRED  @map("expired") // 包月已过期
}

enum SheetStatus {
  ACTIVE // 正常使用
  INACTIVE // 暂停使用
  ERROR // 错误（如：权限问题或者是sheet有效期过期）
}

enum ExcludeListRecordStatus {
  PROCESSING
  SUCCESS
  FAILED
}

enum RapidApiStatus {
  SUCCESS
  ERROR
  WARNING
}

enum KolRelationType {
  LIKED // 点过喜欢
  CONTACTED // 已经建联过
}

enum SystemNotifyPosition {
  SIDEPANEL
  INFOCARD
}

model StripeCustomer {
  id               String   @id @default(cuid())
  userId           String   @unique
  stripeCustomerId String   @unique
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  user             UserInfo @relation(fields: [userId], references: [userId])
}

model StripeSubscription {
  id                   String               @id @default(cuid())
  userId               String
  stripeSubscriptionId String               @unique
  status               SubscriptionStatus   @default(INACTIVE)
  planType             SubscriptionPlanType
  priceId              String
  transitionStatus     TransitionStatus     @default(NORMAL)
  currentPeriodStart   DateTime?
  currentPeriodEnd     DateTime?
  cancelAtPeriodEnd    Boolean              @default(false)
  canceledAt           DateTime?
  paymentScheme        String?              // "A", "B", "C" - 支付方案
  paymentMethod        String?              // "wechat_pay", "alipay", "card" - 支付方式
  isOneTimePayment     Boolean              @default(false) // 是否为一次性支付
  currency             String               @default("CNY") // "CNY", "USD" - 货币类型
  createdAt            DateTime             @default(now())
  updatedAt            DateTime             @updatedAt
  user                 UserInfo             @relation(fields: [userId], references: [userId])

  @@index([priceId])
  @@index([planType])
  @@index([userId, status]) // 添加复合索引以提高查询性能
  @@index([paymentScheme])
  @@index([userId, paymentScheme]) // 用于查询用户的支付方案历史
}

enum SubscriptionStatus {
  ACTIVE
  INACTIVE
  PAST_DUE
  CANCELED
  TRIALING
  INCOMPLETE
  INCOMPLETE_EXPIRED
  UNPAID
  PAUSED

  // 新增取消订阅中和重新订阅中
  CANCELED_IN_PROGRESS
  RESTORING
}

enum TransitionStatus {
  NORMAL
  CANCELED_IN_PROGRESS
  RESTORING
}

enum SubscriptionPlanType {
  FREE // 免费
  BASIC // 300元/月
  PREMIUM // 废弃
  PRO // 600元/月
  TEAM // enterprise
}

enum TagAndNoteOpType {
  ADD_TAG
  DELETE_TAG
  UPDATE_TAG_META
  MODIFY_NOTE
}

enum FeishuShortcutApiKeyStatus {
  ACTIVE
  INACTIVE
}

model BatchTaskRecord {
  id        String   @id @default(cuid()) // 直接使用此ID作为批次标识
  userId    String
  taskType  TaskType
  rawInput  Json? // 原始输入数据（可选，存储链接列表等）
  result    Json? // 创建任务的结果
  summary   Json? // 摘要信息
  tasks     String[] // 关联的任务IDs数组
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user UserInfo @relation(fields: [userId], references: [userId])

  @@index([userId])
  @@index([taskType])
  @@index([createdAt])
}

enum UserAuditLogType {
  DEVICE_BAN // 设备多登陆封禁
  DEVICE_BAN_WEB // 设备多登陆封禁-web
}

model FeishuTrackLog {
  id             String            @id @default(cuid())
  requestId      String            @unique // 请求唯一标识符，对应返回的 id
  apiKey         String // 使用的 API key
  requestUrl     String // 请求的 URL
  platform       String? // 识别出的平台 (TIKTOK, INSTAGRAM, YOUTUBE 等)
  linkType       String? // 链接类型 (Kol, Video 等)
  accountName    String? // 账号名称
  displayName    String? // 显示名称
  status         FeishuTrackStatus // 请求状态
  responseCode   Int? // HTTP 响应码
  responseData   Json? // 完整响应数据
  errorMessage   String? // 错误信息
  processingTime Int? // 处理时间(毫秒)
  quotaUsed      Int               @default(1) // 消耗的配额数量
  ipAddress      String? // 请求方 IP 地址
  userAgent      String? // User-Agent 信息
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt

  @@index([apiKey])
  @@index([platform])
  @@index([status])
  @@index([createdAt])
  @@index([requestId])
}

enum FeishuTrackStatus {
  SUCCESS    @map("success") // 成功
  FAILED     @map("failed") // 失败
  ERROR      @map("error") // 错误
  TIMEOUT    @map("timeout") // 超时
  RATE_LIMIT @map("rate_limit") // 限流
}

enum EmailFollowupThreadStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  PAUSED
}

enum EmailPlanStatus {
  PENDING
  SENDING
  SENT
  FAILED
  CANCELED
}
