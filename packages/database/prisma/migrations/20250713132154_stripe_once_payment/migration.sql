-- AlterTable
ALTER TABLE "StripeSubscription" ADD COLUMN     "currency" TEXT NOT NULL DEFAULT 'CNY',
ADD COLUMN     "isOneTimePayment" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "paymentMethod" TEXT,
ADD COLUMN     "paymentScheme" TEXT;

-- CreateIndex
CREATE INDEX "StripeSubscription_paymentScheme_idx" ON "StripeSubscription"("paymentScheme");

-- CreateIndex
CREATE INDEX "StripeSubscription_userId_paymentScheme_idx" ON "StripeSubscription"("userId", "paymentScheme");
