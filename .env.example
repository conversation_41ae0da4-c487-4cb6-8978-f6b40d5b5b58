# Database
DATABASE_URL=postgresql://[username]:[password]@[host]:[port]/[database]?schema=public
SHADOW_DATABASE_URL=postgresql://[username]:[password]@[host]:[port]/[database]?schema=public

# OpenAI
OPENAI_BASE_URL="[your-openai-base-url]"
OPENAI_API_KEY="[your-openai-api-key]"
OPENAI_MODEL='gpt-4o-mini'

# Google
GOOGLE_CLIENT_ID="[your-google-client-id]"
GOOGLE_CLIENT_SECRET="[your-google-client-secret]"

# API Keys
NAINF_KEY="[your-nainf-key]"
RAPIDAPI_API_KEY="[your-rapidapi-key]"
TT_RAPID_API_KEY='[your-tt-rapid-api-key]'
IG_RAPID_API_KEY='[your-ig-rapid-api-key]'

# Supabase
SUPABASE_URL=[your-supabase-url]
SUPABASE_JWT_SECRET="[your-supabase-jwt-secret]"
SUPABASE_SERVER_ROLE_KEY=[your-supabase-server-role-key]
SUPABASE_ANON_KEY=[your-supabase-anon-key]

# OSS
OSS_ACCESS_KEY_ID=[your-oss-key-id]
OSS_ACCESS_KEY_SECRET=[your-oss-key-secret]
OSS_ENDPOINT=[your-oss-endpoint]
OSS_REGION="oss-cn-hangzhou"
OSS_BUCKET=[your-oss-bucket]

# Cloudflare R2
CLOUDFLARE_R2_ACCOUNT_ID=[your-cloudflare-account-id]
CLOUDFLARE_R2_ACCESS_KEY_ID=[your-r2-access-key-id]
CLOUDFLARE_R2_SECRET_ACCESS_KEY=[your-r2-secret-access-key]
CLOUDFLARE_R2_BUCKET_NAME=[your-r2-bucket-name]
CLOUDFLARE_R2_PUBLIC_URL=[your-r2-public-url]

# Host & API
HOST=http://localhost:3000

# Redis
REDIS_URL=[your-redis-url]
US_REDIS_URL=[your-us-redis-url]
REDIS_KEY_PREFIX=easykol:dev:


# Milvus
MILVUS_URL=[your-milvus-url]
MILVUS_USERNAME=[your-milvus-username]
MILVUS_PASSWORD=[your-milvus-password]
EASYKOL_MILVUS_COLLECTION_NAME='easykol_influencer_embedding'

# Worker
WORKER=true


# Limits
USER_VIDEO_LIMIT=10
RECOMMAND_VIDEO_LIMIT=30
MAX_HASHTAG_VIDEOS=50
TIKTOK_MAX_CONCURRENT_REQUESTS=10
TIKTOK_MAX_HASHTAGS_TO_PROCESS=10
TIKTOK_COMBINED_HASHTAGS_TO_PROCESS=6
MAX_SOURCE_AUTHOR_VIDEOS=30
TIKTOK_MAX_CONCURRENCY=8
TOPK=500
INSTAGRAM_SECOND_ROUND_LIMIT=3

# Task Scheduler
TASK_SCHEDULER_INTERVAL='*/3 * * * *'
TASK_SCHEDULER_BATCH_SIZE='4'
TASK_SCHEDULER_ENABLED='true'

# Queue Concurrency
EMBEDDING_QUEUE_TASK_CONCURRENCY='5'
YOUTUBE_CONCURRENCY='20'
TIKTOK_CONCURRENCY='10'
INSTAGRAM_CONCURRENCY='5'
EMBEDDING_CONCURRENCY='50'

# TikTok Settings
TIKTOK_QUEUE_TASK_LIMIT='10'
TIKTOK_QUEUE_TASK_DURATION='60000'
TIKTOK_USE_SEARCH_WORDS='true'
TIKTOK_USE_HASHTAGS='true'
TIKTOK_USE_TAG_VIDEOS='true'
TIKTOK_MAX_HASHTAG_VIDEOS='30'
TIKTOK_SEARCH_WORDS_VIDEOS_COUNT='30'
TIKTOK_SEARCH_HASHTAG_VIDEOS_COUNT='30'
TIKTOK_VIDEO_BATCH_SIZE='100'
TIKTOK_SIMILAR_SCORE_ADJUSTMENT='0.9'
TIKTOK_MAX_VIDEOS_FOR_AVERAGE='10'

INSTAGRAM_API_CONCURRENCY='10'
# Milvus Search
MILVUS_LIMIT='500'
MILVUS_TOPK='500'
MILVUS_THRESHOLD='0.5'

INSTAGRAM_POST_CONCURRENCY=10

# Instagram Jina Embedding Batch Processing
INS_JINA_EMBEDDING_BATCH_SIZE=5
INS_JINA_EMBEDDING_BATCH_CONCURRENCY=20

DEBUG_TOKEN=easykol123
################
### features ###
################
FEATURE_SEND_EMAIL=false
FEATURE_ENABLE_POLISH_EMAIL=false
FEATURE_ERROR_ON_MISMATCH_REGION=true
FEATURE_YOUTUBE_API_DETECT=false
FEATURE_YOUTUBE_API_DETECT_INTERVALMS=10000
FEATURE_INSTAGRAM_API_DETECT=false
FEATURE_INSTAGRAM_API_DETECT_INTERVALMS=20000
# 是否启用去重列表功能
FEATURE_EXCLUDE_LINK=true
# prompts
EMAIL_PERSONALIZATION_BASIC_PROMPT=
EMAIL_EVALUATION_PROMPT=



# bullmq
YOUTUBE_QUEUE_TASK_LIMIT=40
YOUTUBE_QUEUE_TASK_DURATION=60_000
INSTAGRAM_QUEUE_TASK_LIMIT=20
INSTAGRAM_QUEUE_TASK_DURATION=60_000
# TikTok Union Search
TIKTOK_DEFAULT_LAST_PUBLISHED_DAYS=30

# TikTok Union Search
TT_SIMILAR_QUERY_USE_STOCK=true

YOUTUBE_EMBEDDING_TYPE=ai
INSTAGRAM_EMBEDDING_TYPE=ai
TIKTOK_EMBEDDING_TYPE=ai

NEW_RELIC_APP_NAME=easykol 
NEW_RELIC_LICENSE_KEY=c3a14c7********************************

# 去重列表导入的单价
PRICE_FOR_EXCLUDE_LINK=0.1
# 去重列表导入的最小消费金额
MIN_COST_FOR_EXCLUDE_LINK=1

# 使用打分的大语言模型的名称
RANKING_LLM_NAME=gemini-2.0-flash-search

# 前端安装完成后的跳转页
DEFAULT_WELCOME_PAGE_URL='https://easykol.com'

# youtube 嵌入打分的最低分阈值
YOUTUBE_EMBEDDING_MIN_SCORE=0.4

# 是否启用同设备多登录禁用功能
FEATURE_DEVICE_BAN=true

OPENROUTER_API_KEY=TEST